; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 36
		Millisecond: 87
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/palm_long.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/palm_long.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4866512690108443039, "Model::palm_long", "Null" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",63.5815,2.057448E-13,-71.68248
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.97,0.97,0.97
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Model: 5320986078095243267, "Model::Group 28", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-6.654103,0,6.069369
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.79,0.79,0.79
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4874489470395219942, "Geometry::", "Mesh" {
		Vertices: *216 {
			a: 0,1.3,3.99017,-3.3,1.3,3.99017,-0.3795,6.95,3.61067,-2.9205,6.95,3.61067,-3.3,1.3,0.69017,-2.9205,6.95,1.06967,-3.3,1.3,3.99017,-2.9205,6.95,3.61067,-3.3,1.3,0.69017,0,1.3,0.69017,-2.9205,6.95,1.06967,-0.3795,6.95,1.06967,0,1.3,0.69017,0,1.3,3.99017,-0.3795,6.95,1.06967,-0.3795,6.95,3.61067,-2.59017,16.9,0,-2.383332,24.3,1.656837,-2.59017,16.9,1.88034,-2.383332,24.3,3.123503,-0.70983,16.9,0,-0.70983,16.9,1.88034,-0.9166673,24.3,1.656837,-0.9166673,24.3,3.123503,-2.59017,16.9,0,-0.70983,16.9,0,-2.383332,24.3,1.656837,-0.9166673,24.3,1.656837,-2.9205,6.95,1.06967,-0.3795,6.95,1.06967,-2.59017,16.9,0,-0.70983,16.9,0,-0.70983,16.9,0,-0.3795,6.95,1.06967,-0.70983,16.9,1.88034,-0.3795,6.95,3.61067,-0.70983,16.9,1.88034,-2.59017,16.9,1.88034,-0.9166673,24.3,3.123503,-2.383332,24.3,3.123503,-0.3795,6.95,3.61067,-2.9205,6.95,3.61067,-0.70983,16.9,1.88034,-2.59017,16.9,1.88034,-2.59017,16.9,0,-2.59017,16.9,1.88034,-2.9205,6.95,1.06967,-2.9205,6.95,3.61067,-0.231,0,3.75917,-0.231,0,0.92117,-3.069,0,3.75917,-3.069,0,0.92117,-3.3,1.3,0.69017,-3.3,1.3,3.99017,-3.069,0,0.92117,-3.069,0,3.75917,-3.069,0,0.92117,-0.231,0,0.92117,-3.3,1.3,0.69017,0,1.3,0.69017,0,1.3,0.69017,-0.231,0,0.92117,0,1.3,3.99017,-0.231,0,3.75917,-0.9166673,24.3,1.656837,-0.9166673,24.3,3.123503,-2.383332,24.3,1.656837,-2.383332,24.3,3.123503,-0.231,0,3.75917,-3.069,0,3.75917,0,1.3,3.99017,-3.3,1.3,3.99017
		} 
		PolygonVertexIndex: *108 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *324 {
				a: 0,0.06701714,0.9977518,0,0.06701714,0.9977518,0,0.06701714,0.9977518,0,0.06701714,0.9977518,0,0.06701714,0.9977518,0,0.06701714,0.9977518,-0.9977518,0.06701714,0,-0.9977518,0.06701714,0,-0.9977518,0.06701714,0,-0.9977518,0.06701714,0,-0.9977518,0.06701714,0,-0.9977518,0.06701714,0,0,0.06701714,-0.9977518,0,0.06701714,-0.9977518,0,0.06701714,-0.9977518,0,0.06701714,-0.9977518,0,0.06701714,-0.9977518,0,0.06701714,-0.9977518,0.9977518,0.06701714,0,0.9977518,0.06701714,0,0.9977518,0.06701714,0,0.9977518,0.06701714,0,0.9977518,0.06701714,0,0.9977518,0.06701714,0,-0.9996096,0.02794009,0,-0.9996096,0.02794009,0,-0.9996096,0.02794009,0,-0.9996096,0.02794009,0,-0.9996096,0.02794009,0,-0.9996096,0.02794009,0,0.9996096,0.02794009,0,0.9996096,0.02794009,0,0.9996096,0.02794009,0,0.9996096,0.02794009,0,0.9996096,0.02794009,0,0.9996096,0.02794009,0,0,0.2184875,-0.9758397,0,0.2184875,-0.9758397,0,0.2184875,-0.9758397,0,0.2184875,-0.9758397,0,0.2184875,-0.9758397,0,0.2184875,-0.9758397,0,-0.1068886,-0.994271,0,-0.1068886,-0.994271,0,-0.1068886,-0.994271,0,-0.1068886,-0.994271,0,-0.1068886,-0.994271,0,-0.1068886,-0.994271,0.9994493,0.03318071,0,0.9994493,0.03318071,0,0.9994493,0.03318071,0,0.9994493,0.03318071,0,0.9994493,0.03318071,0,0.9994493,0.03318071,0,0,-0.1656734,0.9861807,0,-0.1656734,0.9861807,0,-0.1656734,0.9861807,0,-0.1656734,0.9861807,0,-0.1656734,0.9861807,0,-0.1656734,0.9861807,0,0.1713311,0.9852135,0,0.1713311,0.9852135,0,0.1713311,0.9852135,0,0.1713311,0.9852135,0,0.1713311,0.9852135,0,0.1713311,0.9852135,-0.9994493,0.03318071,0,-0.9994493,0.03318071,0,-0.9994493,0.03318071,0,-0.9994493,0.03318071,0,-0.9994493,0.03318071,0,-0.9994493,0.03318071,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.984577,-0.1749518,0,-0.984577,-0.1749518,0,-0.984577,-0.1749518,0,-0.984577,-0.1749518,0,-0.984577,-0.1749518,0,-0.984577,-0.1749518,0,0,-0.1749518,-0.984577,0,-0.1749518,-0.984577,0,-0.1749518,-0.984577,0,-0.1749518,-0.984577,0,-0.1749518,-0.984577,0,-0.1749518,-0.984577,0.984577,-0.1749518,0,0.984577,-0.1749518,0,0.984577,-0.1749518,0,0.984577,-0.1749518,0,0.984577,-0.1749518,0,0.984577,-0.1749518,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.1749518,0.984577,0,-0.1749518,0.984577,0,-0.1749518,0.984577,0,-0.1749518,0.984577,0,-0.1749518,0.984577,0,-0.1749518,0.984577
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *144 {
				a: -1.30054E-16,8.107619,-25.98425,8.107619,-2.988189,52.69605,-22.99606,52.69605,5.43441,8.471818,8.422599,53.06025,31.41866,8.471818,28.43047,53.06025,25.98425,10.57741,-2.249513E-17,10.57741,22.99606,55.16584,2.988189,55.16584,-5.43441,10.21321,-31.41866,10.21321,-8.422599,54.80164,-28.43047,54.80164,2.479301E-16,132.4491,13.04596,190.7395,14.80583,132.4491,24.59451,190.7395,-6.794467E-17,133.1751,-14.80583,133.1751,-13.04596,191.4655,-24.59451,191.4655,20.39504,129.8558,5.589212,129.8558,18.7664,189.5662,7.217854,189.5662,22.99606,53.51061,2.988189,53.51061,20.39504,132.3085,5.589212,132.3085,0,133.183,-8.422599,54.79343,-14.80583,133.183,-28.43047,54.79343,-5.589212,133.6848,-20.39504,133.6848,-7.217854,192.7691,-18.7664,192.7691,-2.988189,49.0442,-22.99606,49.0442,-5.589212,128.5665,-20.39504,128.5665,3.121802E-16,132.3209,14.80583,132.3209,8.422599,53.93125,28.43047,53.93125,-1.818898,29.59976,-1.818898,7.253307,-24.16535,29.59976,-24.16535,7.253307,5.43441,14.62434,31.41866,14.62434,7.253307,4.227771,29.59976,4.227771,24.16535,-1.268979,1.818898,-1.268979,25.98425,9.127588,0,9.127588,-5.43441,10.07835,-7.253307,-0.3182193,-31.41866,10.07835,-29.59976,-0.3182193,7.217854,13.04596,7.217854,24.59451,18.7664,13.04596,18.7664,24.59451,-1.818898,5.178531,-24.16535,5.178531,-9.503379E-17,15.5751,-25.98425,15.5751
				}
			UVIndex: *108 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *36 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 5314816518804827548, "Model::Group 30", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,17.87846,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.573276,0.573276,0.573276
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5305176864542834944, "Geometry::", "Mesh" {
		Vertices: *864 {
			a: -12.24949,3.75,12.24949,-12.24949,3.75,15.51237,-12.73892,4.5,12.73892,-12.73892,4.5,15.02293,-12.73892,4.5,12.73892,-12.73892,4.5,15.02293,-15.02293,4.5,12.73892,-15.02293,4.5,15.02293,-12.24949,2.3,12.24949,-12.87036,2.3,14.89149,-12.24949,2.3,15.51237,-15.51237,2.3,15.51237,-14.89149,2.3,14.89149,-14.89149,2.3,12.87036,-12.87036,2.3,12.87036,-15.51237,2.3,12.24949,-15.51237,3.75,12.24949,-12.24949,3.75,12.24949,-15.02293,4.5,12.73892,-12.73892,4.5,12.73892,-15.51237,3.75,12.24949,-15.02293,4.5,12.73892,-15.51237,3.75,15.51237,-15.02293,4.5,15.02293,-12.24949,3.75,15.51237,-15.51237,3.75,15.51237,-12.73892,4.5,15.02293,-15.02293,4.5,15.02293,-12.24949,3.75,12.24949,-12.24949,3.75,15.51237,-15.51237,3.75,12.24949,-15.51237,3.75,15.51237,-11.70568,3.1,17.41236,-12.24949,2.3,15.51237,-16.05618,3.1,17.41236,-15.51237,2.3,15.51237,-15.51237,2.3,15.51237,-15.51237,3.75,15.51237,-16.05618,3.1,17.41236,-16.05618,5.2,17.41236,-11.70568,5.2,17.41236,-16.05618,5.2,17.41236,-12.24949,3.75,15.51237,-15.51237,3.75,15.51237,-11.70568,3.1,21.21237,-11.70568,3.1,17.41236,-16.05618,3.1,21.21237,-16.05618,3.1,17.41236,-11.70568,5.2,21.21237,-11.70568,3.1,21.21237,-11.70568,3.2,25.06237,-11.70568,1.85,25.06237,-11.70568,3.1,17.41236,-11.70568,5.2,17.41236,-16.05618,3.1,21.21237,-16.05618,5.2,21.21237,-16.05618,1.85,25.06237,-16.05618,3.2,25.06237,-16.05618,5.2,17.41236,-16.05618,3.1,17.41236,-12.31315,0,27.76186,-14.92345,0,27.76186,-12.31315,0.65,27.76186,-14.92345,0.65,27.76186,-11.70568,3.2,25.06237,-12.31315,0.65,27.76186,-16.05618,3.2,25.06237,-14.92345,0.65,27.76186,-11.70568,5.2,21.21237,-16.05618,5.2,21.21237,-11.70568,5.2,17.41236,-16.05618,5.2,17.41236,-11.70568,3.2,25.06237,-16.05618,3.2,25.06237,-11.70568,5.2,21.21237,-16.05618,5.2,21.21237,-11.70568,1.85,25.06237,-11.70568,3.1,21.21237,-16.05618,1.85,25.06237,-16.05618,3.1,21.21237,-16.05618,1.85,25.06237,-16.05618,3.2,25.06237,-14.92345,0,27.76186,-14.92345,0.65,27.76186,-11.70568,1.85,25.06237,-16.05618,1.85,25.06237,-12.31315,0,27.76186,-14.92345,0,27.76186,-12.24949,3.75,15.51237,-12.24949,2.3,15.51237,-11.70568,5.2,17.41236,-11.70568,3.1,17.41236,-11.70568,3.2,25.06237,-11.70568,1.85,25.06237,-12.31315,0.65,27.76186,-12.31315,0,27.76186,-2.699491,3.2,16.05618,-6.549491,5.2,16.05618,-2.699491,3.2,11.70568,-6.549491,5.2,11.70568,-6.549491,3.1,16.05618,-6.549491,3.1,11.70568,-10.34949,3.1,16.05618,-10.34949,3.1,11.70568,1.443823E-13,0,12.31315,1.443823E-13,0.65,12.31315,-2.699491,1.85,11.70568,-2.699491,3.2,11.70568,-2.699491,1.85,11.70568,-2.699491,3.2,11.70568,-6.549491,3.1,11.70568,-6.549491,5.2,11.70568,-10.34949,5.2,11.70568,-10.34949,3.1,11.70568,-6.549491,3.1,16.05618,-10.34949,3.1,16.05618,-6.549491,5.2,16.05618,-10.34949,5.2,16.05618,-2.699491,3.2,16.05618,-2.699491,1.85,16.05618,1.443823E-13,0.65,12.31315,1.443823E-13,0,12.31315,7.063903E-12,0.65,14.92345,7.063903E-12,0,14.92345,7.063903E-12,0.65,14.92345,-2.699491,3.2,16.05618,1.443823E-13,0.65,12.31315,-2.699491,3.2,11.70568,7.063903E-12,0,14.92345,1.443823E-13,0,12.31315,-2.699491,1.85,16.05618,-2.699491,1.85,11.70568,-12.24949,2.3,12.24949,-10.34949,3.1,11.70568,-12.24949,3.75,12.24949,-10.34949,5.2,11.70568,-10.34949,3.1,16.05618,-10.34949,3.1,11.70568,-12.24949,2.3,15.51237,-12.24949,2.3,12.24949,-12.24949,2.3,15.51237,-12.24949,3.75,15.51237,-10.34949,3.1,16.05618,-10.34949,5.2,16.05618,-2.699491,1.85,16.05618,-2.699491,1.85,11.70568,-6.549491,3.1,16.05618,-6.549491,3.1,11.70568,-10.34949,5.2,16.05618,-12.24949,3.75,15.51237,-10.34949,5.2,11.70568,-12.24949,3.75,12.24949,-6.549491,5.2,16.05618,-10.34949,5.2,16.05618,-6.549491,5.2,11.70568,-10.34949,5.2,11.70568,7.063903E-12,0,14.92345,-2.699491,1.85,16.05618,7.063903E-12,0.65,14.92345,-2.699491,3.2,16.05618,-11.70568,3.1,10.34949,-11.70568,3.1,6.549491,-16.05618,3.1,10.34949,-16.05618,3.1,6.549491,-16.05618,1.85,2.699491,-16.05618,3.2,2.699491,-16.05618,3.1,6.549491,-16.05618,5.2,6.549491,-16.05618,3.1,10.34949,-16.05618,5.2,10.34949,-11.70568,5.2,10.34949,-16.05618,5.2,10.34949,-11.70568,5.2,6.549491,-16.05618,5.2,6.549491,-11.70568,5.2,6.549491,-11.70568,3.1,6.549491,-11.70568,5.2,10.34949,-11.70568,3.1,10.34949,-11.70568,1.85,2.699491,-11.70568,3.2,2.699491,-12.8384,0.65,1.313879E-12,-12.8384,0,1.313879E-12,-11.70568,3.2,2.699491,-11.70568,1.85,2.699491,-11.70568,1.85,2.699491,-12.8384,0,1.313879E-12,-16.05618,1.85,2.699491,-15.4487,0,7.233552E-12,-11.70568,3.2,2.699491,-16.05618,3.2,2.699491,-12.8384,0.65,1.313879E-12,-15.4487,0.65,7.233552E-12,-15.4487,0,7.233552E-12,-15.4487,0.65,7.233552E-12,-16.05618,1.85,2.699491,-16.05618,3.2,2.699491,-11.70568,3.1,10.34949,-16.05618,3.1,10.34949,-12.24949,2.3,12.24949,-15.51237,2.3,12.24949,-16.05618,3.1,10.34949,-16.05618,5.2,10.34949,-15.51237,2.3,12.24949,-15.51237,3.75,12.24949,-11.70568,5.2,10.34949,-11.70568,3.1,10.34949,-12.24949,3.75,12.24949,-12.24949,2.3,12.24949,-11.70568,5.2,10.34949,-12.24949,3.75,12.24949,-16.05618,5.2,10.34949,-15.51237,3.75,12.24949,-15.4487,0,7.233552E-12,-12.8384,0,1.313879E-12,-15.4487,0.65,7.233552E-12,-12.8384,0.65,1.313879E-12,-11.70568,5.2,6.549491,-16.05618,5.2,6.549491,-11.70568,3.2,2.699491,-16.05618,3.2,2.699491,-11.70568,3.1,6.549491,-11.70568,1.85,2.699491,-16.05618,3.1,6.549491,-16.05618,1.85,2.699491,-21.21237,3.1,16.05618,-21.21237,3.1,11.70568,-25.06237,1.85,16.05618,-25.06237,1.85,11.70568,-15.51237,2.3,15.51237,-17.41236,3.1,16.05618,-15.51237,3.75,15.51237,-17.41236,5.2,16.05618,-25.06237,3.2,16.05618,-27.76186,0.65,15.4487,-25.06237,3.2,11.70568,-27.76186,0.65,12.8384,-15.51237,2.3,12.24949,-15.51237,3.75,12.24949,-17.41236,3.1,11.70568,-17.41236,5.2,11.70568,-25.06237,1.85,11.70568,-21.21237,3.1,11.70568,-25.06237,3.2,11.70568,-21.21237,5.2,11.70568,-17.41236,3.1,11.70568,-17.41236,5.2,11.70568,-17.41236,3.1,16.05618,-17.41236,3.1,11.70568,-21.21237,3.1,16.05618,-21.21237,3.1,11.70568,-25.06237,1.85,16.05618,-25.06237,3.2,16.05618,-21.21237,3.1,16.05618,-21.21237,5.2,16.05618,-17.41236,5.2,16.05618,-17.41236,3.1,16.05618,-25.06237,1.85,16.05618,-25.06237,1.85,11.70568,-27.76186,0,15.4487,-27.76186,0,12.8384,-27.76186,0,15.4487,-27.76186,0.65,15.4487,-25.06237,1.85,16.05618,-25.06237,3.2,16.05618,-21.21237,5.2,16.05618,-25.06237,3.2,16.05618,-21.21237,5.2,11.70568,-25.06237,3.2,11.70568,-15.51237,2.3,15.51237,-15.51237,2.3,12.24949,-17.41236,3.1,16.05618,-17.41236,3.1,11.70568,-17.41236,5.2,16.05618,-21.21237,5.2,16.05618,-17.41236,5.2,11.70568,-21.21237,5.2,11.70568,-27.76186,0,12.8384,-27.76186,0.65,12.8384,-27.76186,0,15.4487,-27.76186,0.65,15.4487,-15.51237,3.75,12.24949,-15.51237,3.75,15.51237,-17.41236,5.2,11.70568,-17.41236,5.2,16.05618,-27.76186,0,12.8384,-25.06237,1.85,11.70568,-27.76186,0.65,12.8384,-25.06237,3.2,11.70568
		} 
		PolygonVertexIndex: *474 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,9,-12,13,12,-12,9,14,-9,9,12,-15,13,14,-13,15,8,-15,13,15,-15,11,15,-14,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,49,52,-49,53,48,-53,54,56,-56,57,55,-57,55,58,-55,59,54,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,111,110,-113,113,112,-111,114,116,-116,117,115,-117,116,114,-119,119,118,-115,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,166,168,-168,169,167,-169,170,172,-172,173,171,-173,174,176,-176,177,175,-177,175,178,-175,179,174,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,210,-210,211,209,-211,212,214,-214,215,213,-215,216,218,-218,219,217,-219,220,222,-222,223,221,-223,224,226,-226,227,225,-227,228,230,-230,231,229,-231,232,234,-234,235,233,-235,236,238,-238,239,237,-239,240,242,-242,243,241,-243,241,243,-245,245,244,-244,246,248,-248,249,247,-249,250,252,-252,253,251,-253,253,252,-255,255,254,-253,256,258,-258,259,257,-259,260,262,-262,263,261,-263,264,266,-266,267,265,-267,268,270,-270,271,269,-271,272,274,-274,275,273,-275,276,278,-278,279,277,-279,280,282,-282,283,281,-283,284,286,-286,287,285,-287
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1422 {
				a: 0.8374569,0.5465034,0,0.8374569,0.5465034,0,0.8374569,0.5465034,0,0.8374569,0.5465034,0,0.8374569,0.5465034,0,0.8374569,0.5465034,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.5465034,-0.8374569,0,0.5465034,-0.8374569,0,0.5465034,-0.8374569,0,0.5465034,-0.8374569,0,0.5465034,-0.8374569,0,0.5465034,-0.8374569,-0.8374569,0.5465034,0,-0.8374569,0.5465034,0,-0.8374569,0.5465034,0,-0.8374569,0.5465034,0,-0.8374569,0.5465034,0,-0.8374569,0.5465034,0,0,0.5465034,0.8374569,0,0.5465034,0.8374569,0,0.5465034,0.8374569,0,0.5465034,0.8374569,0,0.5465034,0.8374569,0,0.5465034,0.8374569,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.9216354,0.388057,0,-0.9216354,0.388057,0,-0.9216354,0.388057,0,-0.9216354,0.388057,0,-0.9216354,0.388057,0,-0.9216354,0.388057,-0.9613961,0,-0.275168,-0.9613961,0,-0.275168,-0.9613961,0,-0.275168,-0.9613961,0,-0.275168,-0.9613961,0,-0.275168,-0.9613961,0,-0.275168,0,0.7949513,-0.6066733,0,0.7949513,-0.6066733,0,0.7949513,-0.6066733,0,0.7949513,-0.6066733,0,0.7949513,-0.6066733,0,0.7949513,-0.6066733,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7269484,0.6866919,0,0.7269484,0.6866919,0,0.7269484,0.6866919,0,0.7269484,0.6866919,0,0.7269484,0.6866919,0,0.7269484,0.6866919,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.8874055,0.4609899,0,0.8874055,0.4609899,0,0.8874055,0.4609899,0,0.8874055,0.4609899,0,0.8874055,0.4609899,0,0.8874055,0.4609899,0,-0.9511248,-0.3088068,0,-0.9511248,-0.3088068,0,-0.9511248,-0.3088068,0,-0.9511248,-0.3088068,0,-0.9511248,-0.3088068,0,-0.9511248,-0.3088068,-0.9221116,0,0.3869239,-0.9221116,0,0.3869239,-0.9221116,0,0.3869239,-0.9221116,0,0.3869239,-0.9221116,0,0.3869239,-0.9221116,0,0.3869239,0,-0.8248826,-0.565304,0,-0.8248826,-0.565304,0,-0.8248826,-0.565304,0,-0.8248826,-0.565304,0,-0.8248826,-0.565304,0,-0.8248826,-0.565304,0.9613961,0,-0.275168,0.9613961,0,-0.275168,0.9613961,0,-0.275168,0.9613961,0,-0.275168,0.9613961,0,-0.275168,0.9613961,0,-0.275168,0.9756026,0,0.2195435,0.9756026,0,0.2195435,0.9756026,0,0.2195435,0.9756026,0,0.2195435,0.9756026,0,0.2195435,0.9756026,0,0.2195435,0.4609899,0.8874055,0,0.4609899,0.8874055,0,0.4609899,0.8874055,0,0.4609899,0.8874055,0,0.4609899,0.8874055,0,0.4609899,0.8874055,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.2195435,0,-0.9756026,0.2195435,0,-0.9756026,0.2195435,0,-0.9756026,0.2195435,0,-0.9756026,0.2195435,0,-0.9756026,0.2195435,0,-0.9756026,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.6866919,0.7269484,0,0.6866919,0.7269484,0,0.6866919,0.7269484,0,0.6866919,0.7269484,0,0.6866919,0.7269484,0,0.6866919,0.7269484,0,-0.565304,-0.8248826,0,-0.565304,-0.8248826,0,-0.565304,-0.8248826,0,-0.565304,-0.8248826,0,-0.565304,-0.8248826,0,-0.565304,-0.8248826,0,-0.275168,0,-0.9613961,-0.275168,0,-0.9613961,-0.275168,0,-0.9613961,-0.275168,0,-0.9613961,-0.275168,0,-0.9613961,-0.275168,0,-0.9613961,0.388057,-0.9216354,0,0.388057,-0.9216354,0,0.388057,-0.9216354,0,0.388057,-0.9216354,0,0.388057,-0.9216354,0,0.388057,-0.9216354,0,-0.275168,0,0.9613961,-0.275168,0,0.9613961,-0.275168,0,0.9613961,-0.275168,0,0.9613961,-0.275168,0,0.9613961,-0.275168,0,0.9613961,-0.3088068,-0.9511248,0,-0.3088068,-0.9511248,0,-0.3088068,-0.9511248,0,-0.3088068,-0.9511248,0,-0.3088068,-0.9511248,0,-0.3088068,-0.9511248,0,-0.6066733,0.7949513,0,-0.6066733,0.7949513,0,-0.6066733,0.7949513,0,-0.6066733,0.7949513,0,-0.6066733,0.7949513,0,-0.6066733,0.7949513,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.3869239,0,0.9221116,0.3869239,0,0.9221116,0.3869239,0,0.9221116,0.3869239,0,0.9221116,0.3869239,0,0.9221116,0.3869239,0,0.9221116,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.9221116,0,-0.3869239,0.9221116,0,-0.3869239,0.9221116,0,-0.3869239,0.9221116,0,-0.3869239,0.9221116,0,-0.3869239,0.9221116,0,-0.3869239,0,-0.8248826,0.565304,0,-0.8248826,0.565304,0,-0.8248826,0.565304,0,-0.8248826,0.565304,0,-0.8248826,0.565304,0,-0.8248826,0.565304,0,0.7269484,-0.6866919,0,0.7269484,-0.6866919,0,0.7269484,-0.6866919,0,0.7269484,-0.6866919,0,0.7269484,-0.6866919,0,0.7269484,-0.6866919,-0.9756026,0,-0.2195435,-0.9756026,0,-0.2195435,-0.9756026,0,-0.2195435,-0.9756026,0,-0.2195435,-0.9756026,0,-0.2195435,-0.9756026,0,-0.2195435,0,-0.9216354,-0.388057,0,-0.9216354,-0.388057,0,-0.9216354,-0.388057,0,-0.9216354,-0.388057,0,-0.9216354,-0.388057,0,-0.9216354,-0.388057,-0.9613961,0,0.275168,-0.9613961,0,0.275168,-0.9613961,0,0.275168,-0.9613961,0,0.275168,-0.9613961,0,0.275168,-0.9613961,0,0.275168,0.9613961,0,0.275168,0.9613961,0,0.275168,0.9613961,0,0.275168,0.9613961,0,0.275168,0.9613961,0,0.275168,0.9613961,0,0.275168,0,0.7949513,0.6066733,0,0.7949513,0.6066733,0,0.7949513,0.6066733,0,0.7949513,0.6066733,0,0.7949513,0.6066733,0,0.7949513,0.6066733,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.8874055,-0.4609899,0,0.8874055,-0.4609899,0,0.8874055,-0.4609899,0,0.8874055,-0.4609899,0,0.8874055,-0.4609899,0,0.8874055,-0.4609899,0,-0.9511248,0.3088068,0,-0.9511248,0.3088068,0,-0.9511248,0.3088068,0,-0.9511248,0.3088068,0,-0.9511248,0.3088068,0,-0.9511248,0.3088068,0.3088068,-0.9511248,0,0.3088068,-0.9511248,0,0.3088068,-0.9511248,0,0.3088068,-0.9511248,0,0.3088068,-0.9511248,0,0.3088068,-0.9511248,0,0.275168,0,0.9613961,0.275168,0,0.9613961,0.275168,0,0.9613961,0.275168,0,0.9613961,0.275168,0,0.9613961,0.275168,0,0.9613961,-0.6866919,0.7269484,0,-0.6866919,0.7269484,0,-0.6866919,0.7269484,0,-0.6866919,0.7269484,0,-0.6866919,0.7269484,0,-0.6866919,0.7269484,0,0.275168,0,-0.9613961,0.275168,0,-0.9613961,0.275168,0,-0.9613961,0.275168,0,-0.9613961,0.275168,0,-0.9613961,0.275168,0,-0.9613961,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.565304,-0.8248826,0,0.565304,-0.8248826,0,0.565304,-0.8248826,0,0.565304,-0.8248826,0,0.565304,-0.8248826,0,0.565304,-0.8248826,0,-0.2195435,0,0.9756026,-0.2195435,0,0.9756026,-0.2195435,0,0.9756026,-0.2195435,0,0.9756026,-0.2195435,0,0.9756026,-0.2195435,0,0.9756026,-0.4609899,0.8874055,0,-0.4609899,0.8874055,0,-0.4609899,0.8874055,0,-0.4609899,0.8874055,0,-0.4609899,0.8874055,0,-0.4609899,0.8874055,0,-0.388057,-0.9216354,0,-0.388057,-0.9216354,0,-0.388057,-0.9216354,0,-0.388057,-0.9216354,0,-0.388057,-0.9216354,0,-0.388057,-0.9216354,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.6066733,0.7949513,0,0.6066733,0.7949513,0,0.6066733,0.7949513,0,0.6066733,0.7949513,0,0.6066733,0.7949513,0,0.6066733,0.7949513,0,-0.3869239,0,-0.9221116,-0.3869239,0,-0.9221116,-0.3869239,0,-0.9221116,-0.3869239,0,-0.9221116,-0.3869239,0,-0.9221116,-0.3869239,0,-0.9221116
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *576 {
				a: -96.45268,77.43978,-122.1446,77.43978,-100.3065,84.4915,-118.2908,84.4915,100.3065,100.3065,100.3065,118.2908,118.2908,100.3065,118.2908,118.2908,-96.45268,96.45268,-101.3414,117.2559,-96.45268,122.1446,-122.1446,122.1446,-117.2559,117.2559,-117.2559,101.3414,-101.3414,101.3414,-122.1446,96.45268,122.1446,77.43978,96.45268,77.43978,118.2908,84.4915,100.3065,84.4915,96.45268,-42.02439,100.3065,-34.97267,122.1446,-42.02439,118.2908,-34.97267,-96.45268,-42.02439,-122.1446,-42.02439,-100.3065,-34.97267,-118.2908,-34.97267,96.45268,96.45268,96.45268,122.1446,122.1446,96.45268,122.1446,122.1446,-92.17069,135.8333,-96.45268,119.6006,-126.4266,135.8333,-122.1446,119.6006,151.0397,18.11024,151.0397,29.52756,166.601,24.40945,166.601,40.94488,92.17069,133.8322,126.4266,133.8322,96.45268,115.0126,122.1446,115.0126,-92.17069,167.0265,-92.17069,137.1052,-126.4266,167.0265,-126.4266,137.1052,-167.0265,40.94488,-167.0265,24.40945,-197.3415,25.19685,-197.3415,14.56693,-137.1052,24.40945,-137.1052,40.94488,167.0265,24.40945,167.0265,40.94488,197.3415,14.56693,197.3415,25.19685,137.1052,40.94488,137.1052,24.40945,-96.95397,0,-117.5075,0,-96.95397,5.11811,-117.5075,5.11811,-92.17069,-126.1546,-96.95397,-155.3944,-126.4266,-126.1546,-117.5075,-155.3944,92.17069,167.0265,126.4266,167.0265,92.17069,137.1052,126.4266,137.1052,-92.17069,-163.5064,-126.4266,-163.5064,-92.17069,-129.345,-126.4266,-129.345,92.17069,-183.198,92.17069,-151.3252,126.4266,-183.198,126.4266,-151.3252,133.0534,14.56693,133.0534,25.19685,156.1046,6.339741E-14,156.1046,5.11811,92.17069,-154.5488,126.4266,-154.5488,96.95397,-180.3171,117.5075,-180.3171,-90.88866,29.52756,-90.88866,18.11024,-106.45,40.94488,-106.45,24.40945,-212.7623,25.19685,-212.7623,14.56693,-234.5497,5.11811,-234.5497,0,-126.4266,30.47803,-126.4266,64.63937,-92.17069,30.47803,-92.17069,64.63937,-51.57079,126.4266,-51.57079,92.17069,-81.49205,126.4266,-81.49205,92.17069,-21.28562,0,-21.28562,5.11811,0.5017653,14.56693,0.5017653,25.19685,21.25583,14.56693,21.25583,25.19685,51.57079,24.40945,51.57079,40.94488,81.49205,40.94488,81.49205,24.40945,-51.57079,24.40945,-81.49205,24.40945,-51.57079,40.94488,-81.49205,40.94488,-21.25583,25.19685,-21.25583,14.56693,-96.95397,5.11811,-96.95397,0,-117.5075,5.11811,-117.5075,0,-117.5075,3.514565,-126.4266,32.75437,-96.95397,3.514565,-92.17069,32.75437,117.5075,2.106072E-10,96.95397,2.106874E-10,126.4266,25.76831,92.17069,25.76831,119.2699,18.11024,103.7086,24.40945,119.2699,29.52756,103.7086,40.94488,-126.4266,-65.6337,-92.17069,-65.6337,-122.1446,-81.8664,-96.45268,-81.8664,-59.11895,18.11024,-59.11895,29.52756,-43.55759,24.40945,-43.55759,40.94488,126.4266,24.71531,92.17069,24.71531,126.4266,56.58806,92.17069,56.58806,126.4266,-39.94204,122.1446,-58.7616,92.17069,-39.94204,96.45268,-58.7616,51.57079,126.4266,81.49205,126.4266,51.57079,92.17069,81.49205,92.17069,-45.46647,3.560479E-14,-68.51772,14.56693,-45.46647,5.11811,-68.51772,25.19685,-92.17069,81.49205,-92.17069,51.57079,-126.4266,81.49205,-126.4266,51.57079,21.25583,14.56693,21.25583,25.19685,51.57079,24.40945,51.57079,40.94488,81.49205,24.40945,81.49205,40.94488,92.17069,81.49205,126.4266,81.49205,92.17069,51.57079,126.4266,51.57079,-51.57079,40.94488,-51.57079,24.40945,-81.49205,40.94488,-81.49205,24.40945,-21.25583,14.56693,-21.25583,25.19685,39.11405,5.11811,39.11405,-3.063021E-14,16.06279,25.19685,16.06279,14.56693,-92.17069,25.76831,-101.0898,-1.800595E-10,-126.4266,25.76831,-121.6433,-1.799552E-10,92.17069,32.75437,126.4266,32.75437,101.0898,3.514565,121.6433,3.514565,26.70601,0,26.70601,5.11811,48.49339,14.56693,48.49339,25.19685,92.17069,-65.6337,126.4266,-65.6337,96.45268,-81.8664,122.1446,-81.8664,43.55759,24.40945,43.55759,40.94488,59.11895,18.11024,59.11895,29.52756,-103.7086,40.94488,-103.7086,24.40945,-119.2699,29.52756,-119.2699,18.11024,-92.17069,-39.94204,-96.45268,-58.7616,-126.4266,-39.94204,-122.1446,-58.7616,121.6433,0,101.0898,0,121.6433,5.11811,101.0898,5.11811,92.17069,64.63937,126.4266,64.63937,92.17069,30.47803,126.4266,30.47803,-92.17069,56.58806,-92.17069,24.71531,-126.4266,56.58806,-126.4266,24.71531,-126.4266,-151.3252,-92.17069,-151.3252,-126.4266,-183.198,-92.17069,-183.198,-151.0397,18.11024,-166.601,24.40945,-151.0397,29.52756,-166.601,40.94488,126.4266,-126.1546,121.6433,-155.3944,92.17069,-126.1546,101.0898,-155.3944,90.88866,18.11024,90.88866,29.52756,106.45,24.40945,106.45,40.94488,197.3415,14.56693,167.0265,24.40945,197.3415,25.19685,167.0265,40.94488,137.1052,24.40945,137.1052,40.94488,-137.1052,126.4266,-137.1052,92.17069,-167.0265,126.4266,-167.0265,92.17069,-197.3415,14.56693,-197.3415,25.19685,-167.0265,24.40945,-167.0265,40.94488,-137.1052,40.94488,-137.1052,24.40945,-126.4266,-154.5488,-92.17069,-154.5488,-121.6433,-180.3171,-101.0898,-180.3171,-186.5581,0,-186.5581,5.11811,-164.7707,14.56693,-164.7707,25.19685,126.4266,-129.345,126.4266,-163.5064,92.17069,-129.345,92.17069,-163.5064,122.1446,119.6006,96.45268,119.6006,126.4266,135.8333,92.17069,135.8333,137.1052,126.4266,167.0265,126.4266,137.1052,92.17069,167.0265,92.17069,101.0898,0,101.0898,5.11811,121.6433,0,121.6433,5.11811,-96.45268,115.0126,-122.1446,115.0126,-92.17069,133.8322,-126.4266,133.8322,240.6852,-2.837585E-15,217.6339,14.56693,240.6852,5.11811,217.6339,25.19685
				}
			UVIndex: *474 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,9,11,13,12,11,9,14,8,9,12,14,13,14,12,15,8,14,13,15,14,11,15,13,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,49,52,48,53,48,52,54,56,55,57,55,56,55,58,54,59,54,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,111,110,112,113,112,110,114,116,115,117,115,116,116,114,118,119,118,114,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166,166,168,167,169,167,168,170,172,171,173,171,172,174,176,175,177,175,176,175,178,174,179,174,178,180,182,181,183,181,182,184,186,185,187,185,186,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202,204,206,205,207,205,206,208,210,209,211,209,210,212,214,213,215,213,214,216,218,217,219,217,218,220,222,221,223,221,222,224,226,225,227,225,226,228,230,229,231,229,230,232,234,233,235,233,234,236,238,237,239,237,238,240,242,241,243,241,242,241,243,244,245,244,243,246,248,247,249,247,248,250,252,251,253,251,252,253,252,254,255,254,252,256,258,257,259,257,258,260,262,261,263,261,262,264,266,265,267,265,266,268,270,269,271,269,270,272,274,273,275,273,274,276,278,277,279,277,278,280,282,281,283,281,282,284,286,285,287,285,286
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *158 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 23412, "Material::leaves", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.227451,0.5568628,0.3215686
			P: "DiffuseColor", "Color", "", "A",0.227451,0.5568628,0.3215686
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::palm_long, Model::RootNode
	C: "OO",4866512690108443039,0

	;Model::Mesh Group 28, Model::USING PARENT
	C: "OO",5320986078095243267,4866512690108443039

	;Geometry::, Model::Mesh Group 28
	C: "OO",4874489470395219942,5320986078095243267

	;Material::wood, Model::Mesh Group 28
	C: "OO",19362,5320986078095243267

	;Model::Mesh Group 25, Model::USING PARENT
	C: "OO",5314816518804827548,4866512690108443039

	;Geometry::, Model::Mesh Group 25
	C: "OO",5305176864542834944,5314816518804827548

	;Material::leaves, Model::Mesh Group 25
	C: "OO",23412,5314816518804827548

}
