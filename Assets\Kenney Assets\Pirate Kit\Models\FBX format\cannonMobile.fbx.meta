fileFormatVersion: 2
guid: 69caf8acb24748848a6b5964245e8a41
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: barrel 2
    100002: //RootNode
    100004: wheel_backLeft
    100006: wheel_backRight
    100008: wheel_frontLeft
    100010: wheel_frontRight
    400000: barrel 2
    400002: //RootNode
    400004: wheel_backLeft
    400006: wheel_backRight
    400008: wheel_frontLeft
    400010: wheel_frontRight
    2300000: barrel 2
    2300002: //RootNode
    2300004: wheel_backLeft
    2300006: wheel_backRight
    2300008: wheel_frontLeft
    2300010: wheel_frontRight
    3300000: barrel 2
    3300002: //RootNode
    3300004: wheel_backLeft
    3300006: wheel_backRight
    3300008: wheel_frontLeft
    3300010: wheel_frontRight
    4300000: cannonMobile_8angles
    4300002: barrel 2
    4300004: wheel_backLeft
    4300006: wheel_backRight
    4300008: wheel_frontLeft
    4300010: wheel_frontRight
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
