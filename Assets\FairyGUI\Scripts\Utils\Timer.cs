using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using DashGame;
public class Timer : MonoBehaviour
{
    public delegate void TimerCallback0();
    public delegate void TimerCallback1(object param);

    public int Count;
    private void Update()
    {
        Timer.OnUpdate();
    }


    public static int GetCount()
    {
        return GetTimer().Count;
    }
    private static Timer timer;
    private static List<TimerInfo> timerList = new List<TimerInfo>();
    private static Timer GetTimer()
    {
        if (timer == null)
        {
            timer = new GameObject("Timer").AddComponent<Timer>();
        }
        return timer;
    }

    public static TimerInfo Delay(float delayTime, TimerCallback0 callBack)
    {
        return Add(delayTime, 1, callBack);
    }

    public static TimerInfo Add(float interval, int repeat, TimerCallback0 callBack, object caller = null, bool coverBefore = true)
    {
        if (coverBefore)
        {
            var oldInfo = Has(caller, callBack, null);
            if(oldInfo != null)
            {
                oldInfo.interval = interval;
                oldInfo.repeat = repeat;
                oldInfo.lastInvokeTime = GameTime.time;
                oldInfo.invokeCount = 0;
                oldInfo.callBack0 = callBack;
                return oldInfo;
            }
        }
        TimerInfo timer = _Add(interval, repeat, caller, coverBefore);
        timer.callBack0 = callBack;
        return timer;
    }

    public static TimerInfo Add(float interval, int repeat, TimerCallback1 callBack, object param, object caller = null, bool coverBefore = true)
    {
        if (coverBefore)
        {
            var oldInfo = Has(caller, null, callBack);
            if (oldInfo != null)
            {
                oldInfo.interval = interval;
                oldInfo.repeat = repeat;
                oldInfo.lastInvokeTime = GameTime.time;
                oldInfo.invokeCount = 0;
                oldInfo.param = param;
                oldInfo.callBack1 = callBack;
                return oldInfo;
            }
        }
        TimerInfo timer = _Add(interval, repeat, caller, coverBefore);
        timer.callBack1 = callBack;
        timer.param = param;

        return timer;
    }
    private static TimerInfo _Add(float interval, int repeat, object caller = null, bool coverBefore = true)
    {
        GetTimer().Count++;

        var info = new TimerInfo
        {
            interval = interval,
            repeat = repeat,
            caller = caller,
            lastInvokeTime = GameTime.time
        };
        timerList.Add(info);
        return info;
    }

    public static TimerInfo Has(object caller, TimerCallback0 c0,TimerCallback1 c1)
    {
        for (int i = 0; i < timerList.Count; i++)
        {
            var info = timerList[i];
            if (info.caller == caller && ((info.callBack0 != null && info.callBack0.Equals(c0)) ||
                (info.callBack1 != null && info.callBack1.Equals(c1))))
            {
                return info;
            }
        }
        return null;
    }

    public static TimerInfo CallLater(TimerCallback0 callBack)
    {
        return Add(0.001f, 1, callBack);
    }

    public static void OnUpdate()
    {
        if(timerList.Count > 0)
        {
            float currentTime = GameTime.time;
            for (int i = 0; i < timerList.Count; i++)
            {
                var info = timerList[i];
                if (currentTime - info.lastInvokeTime > info.interval)
                {
                    info.lastInvokeTime = currentTime;
                    try
                    {
                        info.invokeCount++;
                        if (info.IsDone())
                        {
                            _Remove(i);
                            i--;
                        }
                        info.Run();
                    }
                    catch (System.Exception e)
                    {
                        Log.Debug("====Timer====:" + e.Message);
                        if (!info.deleted)
                        {
                            _Remove(i);
                            i--;
                        }
                    }
                }
            }
        }
    }

    public static void Remove(object caller, TimerCallback0 callback)
    {
        for (int i = 0; i < timerList.Count; i++)
        {
            var info = timerList[i];
            if (info.caller == caller && info.callBack0 == callback)
            {
                _Remove(i);
                i--;
            }
        }
    }
    public static void Remove(object caller, TimerCallback1 callback)
    {
        for (int i = 0; i < timerList.Count; i++)
        {
            var info = timerList[i];
            if (info.caller == caller && info.callBack1 == callback)
            {
                _Remove(i);
                i--;
            }
        }
    }

    private static void _Remove(int i)
    {
        GetTimer().Count--;
        var info = timerList[i];
        info.deleted = true;
        timerList.RemoveAt(i);
    }

    public static void RemoveAll(object caller)
    {
        for (int i = 0; i < timerList.Count; i++)
        {
            var info = timerList[i];
            if (info.caller == caller)
            {
                _Remove(i);
                i--;
            }
        }
    }

    public static void Dispose()
    {
        GetTimer().Count = 0;
        timerList.Clear();
    }
}

public class TimerInfo
{
    public Timer.TimerCallback0 callBack0;
    public Timer.TimerCallback1 callBack1;
    public float interval;
    public int repeat;
    public int invokeCount;
    public float lastInvokeTime;
    public object param;
    public object caller;
    public bool deleted;
    public void Run()
    {
        callBack0?.Invoke();
        callBack1?.Invoke(param);
    }

    public bool IsDone()
    {
        if (repeat != 0 && invokeCount >= repeat)
        {
            return true;
        }
        return false;
    }

    private void Remove()
    {
        callBack0 = null;
        callBack1 = null;
        param = null;
        caller = null;
        lastInvokeTime = 0;
        invokeCount = 0;
        deleted = false;
    }
}
