using UnityEngine;
using System.Collections;
using Lit<PERSON>son;

public class CallBackUtil
{

	public delegate void SimpleCallBack();
	public delegate void Bool<PERSON><PERSON><PERSON>ack(bool value);
	public delegate void String<PERSON><PERSON>Back(string value);
	public delegate void Integer<PERSON>allBack(int value);
	public delegate void Float<PERSON><PERSON><PERSON><PERSON>(float value);
	public delegate void UnsignedInteger<PERSON><PERSON>Back(uint value);
	public delegate void Vector3CallBack(Vector3 value);
	public delegate void Json<PERSON><PERSON><PERSON>ack(JsonData value);
	public delegate void GameObject<PERSON>allBack(GameObject value);
	public delegate void BytesCallBack(byte[] bytes);
	public delegate void AssetBundleCallBack(AssetBundle assetBundle);
	public delegate void Type<PERSON>allBack<T>(T t);
    public delegate void TypeCallBack<T0,T1>(T0 t0,T1 t1);
    public delegate void RequestCallBack(bool success, JsonData data);
}

