; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 36
		Millisecond: 321
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/plant.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/plant.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4893042609686182418, "Model::plant", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",6.374152,1.985256E-13,-64.09544
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",2.419794E-14,-8.62024E-31,-2.449316E-14
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.69,0.69,0.69
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5306811907601273643, "Geometry::", "Mesh" {
		Vertices: *396 {
			a: 2.338269,2.6,-1.35,3.632111,5.5,-2.097,2.887646E-14,2.6,-2.7,-5.775292E-14,5.5,-4.194,2.887646E-14,2.6,-2.7,3.632111,5.5,-2.097,2.338269,2.6,-1.35,-5.775292E-14,5.5,-4.194,-3.632111,5.5,2.097,-3.632111,5.5,-2.097,-4.676537,5.925362,1.155058E-13,-4.676537,5.925362,1.155058E-13,-3.632111,5.5,-2.097,-3.632111,5.5,2.097,-5.775292E-14,0,1.8,2.887646E-14,2.6,2.7,1.558846,1.804779E-15,0.9,2.338269,2.6,1.35,1.558846,1.804779E-15,0.9,2.887646E-14,2.6,2.7,-5.775292E-14,0,1.8,2.338269,2.6,1.35,-2.338269,2.6,-1.35,-2.338269,2.6,1.35,-1.558846,5.414336E-15,-0.9,-1.558846,5.414336E-15,0.9,-1.558846,5.414336E-15,-0.9,-2.338269,2.6,1.35,-2.338269,2.6,-1.35,-1.558846,5.414336E-15,0.9,2.338269,2.6,1.35,2.887646E-14,2.6,2.7,3.632111,5.5,2.097,-5.775292E-14,5.5,4.194,3.632111,5.5,2.097,2.887646E-14,2.6,2.7,2.338269,2.6,1.35,-5.775292E-14,5.5,4.194,-3.632111,5.5,-2.097,-3.632111,5.5,2.097,-2.338269,2.6,-1.35,-2.338269,2.6,1.35,-2.338269,2.6,-1.35,-3.632111,5.5,2.097,-3.632111,5.5,-2.097,-2.338269,2.6,1.35,1.558846,0,-0.9,2.338269,2.6,-1.35,-5.775292E-14,5.414336E-15,-1.8,2.887646E-14,2.6,-2.7,-5.775292E-14,5.414336E-15,-1.8,2.338269,2.6,-1.35,1.558846,0,-0.9,2.887646E-14,2.6,-2.7,-1.02759E-07,3.275188,-4.194,-2.338269,3.38029,-4.05,-3.632111,3.275188,-2.097,-3.632111,3.275188,-2.097,-2.338269,3.38029,-4.05,-1.02759E-07,3.275188,-4.194,3.63211,3.275188,-2.097,2.338269,1.548271,-1.35,3.63211,3.275188,2.097,2.338269,1.548271,1.35,3.63211,3.275188,2.097,2.338269,1.548271,-1.35,3.63211,3.275188,-2.097,2.338269,1.548271,1.35,-5.775292E-14,0,1.8,-1.558846,5.414336E-15,0.9,2.887646E-14,1.548271,2.7,-2.338269,1.548271,1.35,2.887646E-14,1.548271,2.7,-1.558846,5.414336E-15,0.9,-5.775292E-14,0,1.8,-2.338269,1.548271,1.35,-2.338269,3.38029,4.05,2.2269E-08,3.275188,4.194,-3.632111,3.275188,2.097,-3.632111,3.275188,2.097,2.2269E-08,3.275188,4.194,-2.338269,3.38029,4.05,-1.558846,5.414336E-15,-0.9,-5.775292E-14,5.414336E-15,-1.8,-2.338269,1.548271,-1.35,-8.049006E-08,1.548271,-2.7,-2.338269,1.548271,-1.35,-5.775292E-14,5.414336E-15,-1.8,-1.558846,5.414336E-15,-0.9,-8.049006E-08,1.548271,-2.7,-2.338269,1.548271,-1.35,-8.049006E-08,1.548271,-2.7,-3.632111,3.275188,-2.097,-1.02759E-07,3.275188,-4.194,-3.632111,3.275188,-2.097,-8.049006E-08,1.548271,-2.7,-2.338269,1.548271,-1.35,-1.02759E-07,3.275188,-4.194,3.63211,3.275188,2.097,4.676537,3.090435,-6.97062E-08,3.63211,3.275188,-2.097,3.63211,3.275188,-2.097,4.676537,3.090435,-6.97062E-08,3.63211,3.275188,2.097,2.338269,1.548271,-1.35,1.558846,0,-0.9,2.338269,1.548271,1.35,1.558846,1.804779E-15,0.9,2.338269,1.548271,1.35,1.558846,0,-0.9,2.338269,1.548271,-1.35,1.558846,1.804779E-15,0.9,2.887646E-14,1.548271,2.7,-2.338269,1.548271,1.35,2.2269E-08,3.275188,4.194,-3.632111,3.275188,2.097,2.2269E-08,3.275188,4.194,-2.338269,1.548271,1.35,2.887646E-14,1.548271,2.7,-3.632111,3.275188,2.097,2.338269,5.852899,4.05,3.632111,5.5,2.097,-5.775292E-14,5.5,4.194,-5.775292E-14,5.5,4.194,3.632111,5.5,2.097,2.338269,5.852899,4.05,3.632111,5.5,-2.097,2.338269,6.07029,-4.05,-5.775292E-14,5.5,-4.194,-5.775292E-14,5.5,-4.194,2.338269,6.07029,-4.05,3.632111,5.5,-2.097
		} 
		PolygonVertexIndex: *180 {
			a: 0,2,-2,3,1,-3,4,6,-6,5,7,-5,8,10,-10,11,13,-13,14,16,-16,17,15,-17,18,20,-20,19,21,-19,22,24,-24,25,23,-25,26,28,-28,27,29,-27,30,32,-32,33,31,-33,34,36,-36,35,37,-35,38,40,-40,41,39,-41,42,44,-44,43,45,-43,46,48,-48,49,47,-49,50,52,-52,51,53,-51,54,56,-56,57,59,-59,60,62,-62,63,61,-63,64,66,-66,65,67,-65,68,70,-70,71,69,-71,72,74,-74,73,75,-73,76,78,-78,79,81,-81,82,84,-84,85,83,-85,86,88,-88,87,89,-87,90,92,-92,93,91,-93,94,96,-96,95,97,-95,98,100,-100,101,103,-103,104,106,-106,107,105,-107,108,110,-110,109,111,-109,112,114,-114,115,113,-115,116,118,-118,117,119,-117,120,122,-122,123,125,-125,126,128,-128,129,131,-131
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *540 {
				a: 0.4566159,-0.4074405,-0.7908819,0.4566159,-0.4074405,-0.7908819,0.4566159,-0.4074405,-0.7908819,0.4566159,-0.4074405,-0.7908819,0.4566159,-0.4074405,-0.7908819,0.4566159,-0.4074405,-0.7908819,-0.4566159,0.4074405,0.7908819,-0.4566159,0.4074405,0.7908819,-0.4566159,0.4074405,0.7908819,-0.4566159,0.4074405,0.7908819,-0.4566159,0.4074405,0.7908819,-0.4566159,0.4074405,0.7908819,-0.3771867,-0.9261372,0,-0.3771867,-0.9261372,0,-0.3771867,-0.9261372,0,0.3771867,0.9261372,0,0.3771867,0.9261372,0,0.3771867,0.9261372,0,0.4789424,-0.2871528,0.8295525,0.4789424,-0.2871528,0.8295525,0.4789424,-0.2871528,0.8295525,0.4789424,-0.2871528,0.8295525,0.4789424,-0.2871528,0.8295525,0.4789424,-0.2871528,0.8295525,-0.4789424,0.2871528,-0.8295525,-0.4789424,0.2871528,-0.8295525,-0.4789424,0.2871528,-0.8295525,-0.4789424,0.2871528,-0.8295525,-0.4789424,0.2871528,-0.8295525,-0.4789424,0.2871528,-0.8295525,-0.9578848,-0.2871528,0,-0.9578848,-0.2871528,0,-0.9578848,-0.2871528,0,-0.9578848,-0.2871528,0,-0.9578848,-0.2871528,0,-0.9578848,-0.2871528,0,0.9578848,0.2871528,0,0.9578848,0.2871528,0,0.9578848,0.2871528,0,0.9578848,0.2871528,0,0.9578848,0.2871528,0,0.9578848,0.2871528,0,0.4566159,-0.4074405,0.7908819,0.4566159,-0.4074405,0.7908819,0.4566159,-0.4074405,0.7908819,0.4566159,-0.4074405,0.7908819,0.4566159,-0.4074405,0.7908819,0.4566159,-0.4074405,0.7908819,-0.4566159,0.4074405,-0.7908819,-0.4566159,0.4074405,-0.7908819,-0.4566159,0.4074405,-0.7908819,-0.4566159,0.4074405,-0.7908819,-0.4566159,0.4074405,-0.7908819,-0.4566159,0.4074405,-0.7908819,-0.9132317,-0.4074405,0,-0.9132317,-0.4074405,0,-0.9132317,-0.4074405,0,-0.9132317,-0.4074405,0,-0.9132317,-0.4074405,0,-0.9132317,-0.4074405,0,0.9132317,0.4074405,0,0.9132317,0.4074405,0,0.9132317,0.4074405,0,0.9132317,0.4074405,0,0.9132317,0.4074405,0,0.9132317,0.4074405,0,0.4789424,-0.2871528,-0.8295525,0.4789424,-0.2871528,-0.8295525,0.4789424,-0.2871528,-0.8295525,0.4789424,-0.2871528,-0.8295525,0.4789424,-0.2871528,-0.8295525,0.4789424,-0.2871528,-0.8295525,-0.4789424,0.2871528,0.8295525,-0.4789424,0.2871528,0.8295525,-0.4789424,0.2871528,0.8295525,-0.4789424,0.2871528,0.8295525,-0.4789424,0.2871528,0.8295525,-0.4789424,0.2871528,0.8295525,-0.05006275,-0.9949749,-0.08671123,-0.05006275,-0.9949749,-0.08671123,-0.05006275,-0.9949749,-0.08671123,0.05006275,0.9949749,0.08671123,0.05006275,0.9949749,0.08671123,0.05006275,0.9949749,0.08671123,0.8002993,-0.5996008,0,0.8002993,-0.5996008,0,0.8002993,-0.5996008,0,0.8002993,-0.5996008,0,0.8002993,-0.5996008,0,0.8002993,-0.5996008,0,-0.8002993,0.5996008,0,-0.8002993,0.5996008,0,-0.8002993,0.5996008,0,-0.8002993,0.5996008,0,-0.8002993,0.5996008,0,-0.8002993,0.5996008,0,-0.4466019,-0.4496523,0.7735372,-0.4466019,-0.4496523,0.7735372,-0.4466019,-0.4496523,0.7735372,-0.4466019,-0.4496523,0.7735372,-0.4466019,-0.4496523,0.7735372,-0.4466019,-0.4496523,0.7735372,0.4466019,0.4496523,-0.7735372,0.4466019,0.4496523,-0.7735372,0.4466019,0.4496523,-0.7735372,0.4466019,0.4496523,-0.7735372,0.4466019,0.4496523,-0.7735372,0.4466019,0.4496523,-0.7735372,-0.05006275,-0.9949749,0.08671123,-0.05006275,-0.9949749,0.08671123,-0.05006275,-0.9949749,0.08671123,0.05006275,0.9949749,-0.08671123,0.05006275,0.9949749,-0.08671123,0.05006275,0.9949749,-0.08671123,-0.4466019,-0.4496523,-0.7735372,-0.4466019,-0.4496523,-0.7735372,-0.4466019,-0.4496523,-0.7735372,-0.4466019,-0.4496523,-0.7735372,-0.4466019,-0.4496523,-0.7735372,-0.4466019,-0.4496523,-0.7735372,0.4466019,0.4496523,0.7735372,0.4466019,0.4496523,0.7735372,0.4466019,0.4496523,0.7735372,0.4466019,0.4496523,0.7735372,0.4466019,0.4496523,0.7735372,0.4466019,0.4496523,0.7735372,-0.4001496,-0.5996007,-0.6930795,-0.4001496,-0.5996007,-0.6930795,-0.4001496,-0.5996007,-0.6930795,-0.4001496,-0.5996007,-0.6930795,-0.4001496,-0.5996007,-0.6930795,-0.4001496,-0.5996007,-0.6930795,0.4001496,0.5996007,0.6930795,0.4001496,0.5996007,0.6930795,0.4001496,0.5996007,0.6930795,0.4001496,0.5996007,0.6930795,0.4001496,0.5996007,0.6930795,0.4001496,0.5996007,0.6930795,-0.17419,-0.984712,0,-0.17419,-0.984712,0,-0.17419,-0.984712,0,0.17419,0.984712,0,0.17419,0.984712,0,0.17419,0.984712,0,0.8932037,-0.4496522,0,0.8932037,-0.4496522,0,0.8932037,-0.4496522,0,0.8932037,-0.4496522,0,0.8932037,-0.4496522,0,0.8932037,-0.4496522,0,-0.8932037,0.4496522,0,-0.8932037,0.4496522,0,-0.8932037,0.4496522,0,-0.8932037,0.4496522,0,-0.8932037,0.4496522,0,-0.8932037,0.4496522,0,-0.4001496,-0.5996007,0.6930795,-0.4001496,-0.5996007,0.6930795,-0.4001496,-0.5996007,0.6930795,-0.4001496,-0.5996007,0.6930795,-0.4001496,-0.5996007,0.6930795,-0.4001496,-0.5996007,0.6930795,0.4001496,0.5996007,-0.6930795,0.4001496,0.5996007,-0.6930795,0.4001496,0.5996007,-0.6930795,0.4001496,0.5996007,-0.6930795,0.4001496,0.5996007,-0.6930795,0.4001496,0.5996007,-0.6930795,0.160054,-0.947381,0.2772217,0.160054,-0.947381,0.2772217,0.160054,-0.947381,0.2772217,-0.160054,0.947381,-0.2772217,-0.160054,0.947381,-0.2772217,-0.160054,0.947381,-0.2772217,0.2396212,-0.8776826,-0.415036,0.2396212,-0.8776826,-0.415036,0.2396212,-0.8776826,-0.415036,-0.2396212,0.8776826,0.415036,-0.2396212,0.8776826,0.415036,-0.2396212,0.8776826,0.415036
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *264 {
				a: -10.62992,26.1977,-16.51181,51.20192,10.62992,26.1977,16.51181,51.20192,10.62992,26.1977,-16.51181,51.20192,-10.62992,26.1977,16.51181,51.20192,16.51181,42.82173,-16.51181,42.82173,8.211282E-13,51.70144,8.211282E-13,51.70144,-16.51181,42.82173,16.51181,42.82173,-7.086614,3.524621,-10.62992,24.89717,7.086614,3.524621,10.62992,24.89717,7.086614,3.524621,-10.62992,24.89717,-7.086614,3.524621,10.62992,24.89717,-10.62992,24.89717,10.62992,24.89717,-7.086614,3.524621,7.086614,3.524621,-7.086614,3.524621,10.62992,24.89717,-10.62992,24.89717,7.086614,3.524621,10.62992,26.1977,-10.62992,26.1977,16.51181,51.20192,-16.51181,51.20192,16.51181,51.20192,-10.62992,26.1977,10.62992,26.1977,-16.51181,51.20192,-16.51181,51.20192,16.51181,51.20192,-10.62992,26.1977,10.62992,26.1977,-10.62992,26.1977,16.51181,51.20192,-16.51181,51.20192,10.62992,26.1977,-7.086614,3.524621,-10.62992,24.89717,7.086614,3.524621,10.62992,24.89717,7.086614,3.524621,-10.62992,24.89717,-7.086614,3.524621,10.62992,24.89717,-16.51181,31.0377,2.744377E-07,39.30307,16.51181,31.0377,16.51181,31.0377,2.744377E-07,39.30307,-16.51181,31.0377,16.51181,37.78698,10.62992,20.79612,-16.51181,37.78698,-10.62992,20.79612,-16.51181,37.78698,10.62992,20.79612,16.51181,37.78698,-10.62992,20.79612,7.086614,5.519201,-7.086614,5.519201,10.62992,19.16794,-10.62992,19.16794,10.62992,19.16794,-7.086614,5.519201,7.086614,5.519201,-10.62992,19.16794,-2.744347E-07,39.30307,16.51181,31.0377,-16.51181,31.0377,-16.51181,31.0377,16.51181,31.0377,-2.744347E-07,39.30307,7.086614,5.519201,-7.086614,5.519201,10.62992,19.16794,-10.62992,19.16794,10.62992,19.16794,-7.086614,5.519201,7.086614,5.519201,-10.62992,19.16794,10.62992,20.79612,-10.62992,20.79612,16.51181,37.78698,-16.51181,37.78698,16.51181,37.78698,-10.62992,20.79612,10.62992,20.79612,-16.51181,37.78698,16.51181,-23.6699,2.20275E-12,-32.02142,-16.51181,-23.6699,-16.51181,-23.6699,2.20275E-12,-32.02142,16.51181,-23.6699,10.62992,19.16794,7.086614,5.5192,-10.62992,19.16794,-7.086614,5.5192,-10.62992,19.16794,7.086614,5.5192,10.62992,19.16794,-7.086614,5.5192,10.62992,20.79612,-10.62992,20.79612,16.51181,37.78698,-16.51181,37.78698,16.51181,37.78698,-10.62992,20.79612,10.62992,20.79612,-16.51181,37.78698,1.509903E-13,49.63797,16.51181,40.95738,-16.51181,40.95738,-16.51181,40.95738,16.51181,40.95738,1.509903E-13,49.63797,-16.51181,45.85569,-1.39444E-13,55.22563,16.51181,45.85569,16.51181,45.85569,-1.39444E-13,55.22563,-16.51181,45.85569
				}
			UVIndex: *180 {
				a: 0,2,1,3,1,2,4,6,5,5,7,4,8,10,9,11,13,12,14,16,15,17,15,16,18,20,19,19,21,18,22,24,23,25,23,24,26,28,27,27,29,26,30,32,31,33,31,32,34,36,35,35,37,34,38,40,39,41,39,40,42,44,43,43,45,42,46,48,47,49,47,48,50,52,51,51,53,50,54,56,55,57,59,58,60,62,61,63,61,62,64,66,65,65,67,64,68,70,69,71,69,70,72,74,73,73,75,72,76,78,77,79,81,80,82,84,83,85,83,84,86,88,87,87,89,86,90,92,91,93,91,92,94,96,95,95,97,94,98,100,99,101,103,102,104,106,105,107,105,106,108,110,109,109,111,108,112,114,113,115,113,114,116,118,117,117,119,116,120,122,121,123,125,124,126,128,127,129,131,130
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *60 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 23412, "Material::leaves", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.227451,0.5568628,0.3215686
			P: "DiffuseColor", "Color", "", "A",0.227451,0.5568628,0.3215686
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh plant, Model::RootNode
	C: "OO",4893042609686182418,0

	;Geometry::, Model::Mesh plant
	C: "OO",5306811907601273643,4893042609686182418

	;Material::leaves, Model::Mesh plant
	C: "OO",23412,4893042609686182418

}
