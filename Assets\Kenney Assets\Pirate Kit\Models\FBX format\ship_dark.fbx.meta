fileFormatVersion: 2
guid: d4e11b9e630d2ed41bef099db564e5b4
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: cannon_front
    100002: cannon_front 1
    100004: cannon_left
    100006: cannon_left 1
    100008: cannon_left 2
    100010: cannon_left 3
    100012: cannon_left 4
    100014: cannon_left 5
    100016: cannon_right
    100018: cannon_right 1
    100020: cannon_right 2
    100022: cannon_right 3
    100024: cannon_right 4
    100026: cannon_right 5
    100028: sail_back
    100030: sail_front
    100032: sail_middle
    100034: //RootNode
    100036: steering
    400000: cannon_front
    400002: cannon_front 1
    400004: cannon_left
    400006: cannon_left 1
    400008: cannon_left 2
    400010: cannon_left 3
    400012: cannon_left 4
    400014: cannon_left 5
    400016: cannon_right
    400018: cannon_right 1
    400020: cannon_right 2
    400022: cannon_right 3
    400024: cannon_right 4
    400026: cannon_right 5
    400028: sail_back
    400030: sail_front
    400032: sail_middle
    400034: //RootNode
    400036: steering
    2300000: cannon_front
    2300002: cannon_front 1
    2300004: cannon_left
    2300006: cannon_left 1
    2300008: cannon_left 2
    2300010: cannon_left 3
    2300012: cannon_left 4
    2300014: cannon_left 5
    2300016: cannon_right
    2300018: cannon_right 1
    2300020: cannon_right 2
    2300022: cannon_right 3
    2300024: cannon_right 4
    2300026: cannon_right 5
    2300028: sail_back
    2300030: sail_front
    2300032: sail_middle
    2300034: //RootNode
    2300036: steering
    3300000: cannon_front
    3300002: cannon_front 1
    3300004: cannon_left
    3300006: cannon_left 1
    3300008: cannon_left 2
    3300010: cannon_left 3
    3300012: cannon_left 4
    3300014: cannon_left 5
    3300016: cannon_right
    3300018: cannon_right 1
    3300020: cannon_right 2
    3300022: cannon_right 3
    3300024: cannon_right 4
    3300026: cannon_right 5
    3300028: sail_back
    3300030: sail_front
    3300032: sail_middle
    3300034: //RootNode
    3300036: steering
    4300000: ship_dark_8angles
    4300002: cannon_front
    4300004: cannon_front
    4300006: cannon_left
    4300008: cannon_left
    4300010: cannon_left
    4300012: cannon_left
    4300014: cannon_left
    4300016: cannon_left
    4300018: cannon_right
    4300020: cannon_right
    4300022: cannon_right
    4300024: cannon_right
    4300026: cannon_right
    4300028: cannon_right
    4300030: sail_back
    4300032: sail_front
    4300034: sail_middle
    4300036: steering
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
