# 游戏系统规范


## 系统分类
### GlobalSystem
* 全局系统，整个游戏生命周期内存在
* 继承自`BaseSystem`
* 目标目录：Assets\_MyGame\Scripts\Systems\Global
* 示例：CoinSystem, DailyRewardSystem

### GameplaySystem
* 玩法系统，仅在具体玩法场景中存在
* 继承自`BaseSystem`
* 目标目录：Assets\_MyGame\Scripts\Systems\Gameplay
* 示例：ComboSystem, LifeSystem

## 规范
* 需要在目标目录下为新系统创建新目录
* 所有系统必须实现`OnInit()`方法
* 初始化逻辑应在`OnInit()`中完成
* 需要每帧更新的系统应实现`OnUpdate()`方法
* 资源释放和清理逻辑应在`Dispose()`中完成
* 在SystemFacade（Assets\_MyGame\Scripts\Systems\SystemFacade.cs）中添加系统访问接口
    比如：public static ItemSystem ItemSystem => GetGlobalSystem<ItemSystem>();
    
## 调试支持
* 系统应实现`GetDebugInfo()`方法
* 返回系统当前状态的调试信息
* 调试信息在Editor模式下可见

## 最佳实践
* 系统类名应以`System`结尾
* 数据类应以`Data`结尾
* 枚举类应以`Enum`结尾


## 数据管理
* 根据需要实现数据持久化
例如：
```csharp
private SkinSaveData saveData;
protected override void OnInit()
{
    themes = ConfigTheme.GetThemes();

    LoadData();
    if (saveData == null)
    {
        InitializeDefaultThemes();
    }
    else
    {
        SyncThemeData();
    }
}
```

## 7. 示例代码

### 7.1 系统基类
```csharp
public abstract class BaseSystem
{
    protected bool isInitialized;

    public virtual void Init() { /*...*/ }
    public virtual void Update() { /*...*/ }
    public virtual void Dispose() { /*...*/ }
    public virtual string GetDebugInfo() { /*...*/ }
}
```

### 7.2 全局系统
```csharp
public abstract class GlobalSystem : BaseSystem { }
```

### 7.3 玩法系统
```csharp
public abstract class GameplaySystem : BaseSystem { }