﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Xml;

public class Language
{
	public const string NO_LANGUAGE_STR = "????";

	private static Dictionary<string, string> lanDict = new Dictionary<string, string>();
	private static SystemLanguage currentLan;
	private static bool initialized;
	
	public static void Init (SystemLanguage lan)
	{
		Log.Debug("Init Language "+lan.ToString());

		currentLan = lan;
		initialized = true;

		TextAsset textAsset = (TextAsset)Resources.Load("XML/Config/All/word_config");

		if (textAsset == null)
			return;

		lanDict.Clear ();

		XmlDocument xml = new XmlDocument();
		xml.LoadXml(textAsset.text);

		XmlNode mapNode = xml.SelectSingleNode("data");
		XmlNodeList nodeList = mapNode.SelectNodes("item");
		for(int i=0; i<nodeList.Count; i++)
		{
			XmlElement el = nodeList [i] as XmlElement;
			string title = el.GetAttribute ("title");
			string label = el.GetAttribute ("label");
			string word = el.GetAttribute (lan.ToString());

			string key = title + "_" + label;
			if (lanDict.ContainsKey (key))
				Log.Error ("Language duplicate "+key);
			else
				lanDict.Add (key, word);
		}
	}

	private static SystemLanguage GetSystemLanguage()
	{
//		return SystemLanguage.Chinese;
//		return SystemLanguage.English;

		if(Application.systemLanguage == SystemLanguage.Chinese || Application.systemLanguage == SystemLanguage.ChineseSimplified)
			return SystemLanguage.Chinese;

		return SystemLanguage.Chinese;
	}

	public static SystemLanguage GetCurrentLanguage()
	{
		if(!initialized)
			Init (GetSystemLanguage());
		return currentLan;
	}
	
	public static string GetStr(string page, string id)
	{
		if (!initialized)
			Init (GetSystemLanguage ());

		string key = page + "_" + id;

		if (lanDict.ContainsKey (key)) {
			string str = lanDict [key];
			str = str.Replace ("\\n", "\n");
			return str;
		} 

		//string error = "Topic missing page["+page+"] id["+id+"]";
		Log.Error("Can not find language : [" + page + ":" + id + "]");
		return NO_LANGUAGE_STR;
	}

}

