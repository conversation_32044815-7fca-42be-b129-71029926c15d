﻿<?xml version="1.0" encoding="utf-8"?>
<linker>
  <assembly fullname="DOTween">
    <type fullname="DG.Tweening.Core.DOGetter`1" preserve="all" />
    <type fullname="DG.Tweening.Core.DOSetter`1" preserve="all" />
    <type fullname="DG.Tweening.Core.TweenerCore`3" preserve="all" />
    <type fullname="DG.Tweening.DOTween" preserve="all" />
    <type fullname="DG.Tweening.DOVirtual" preserve="all" />
    <type fullname="DG.Tweening.Ease" preserve="all" />
    <type fullname="DG.Tweening.PathMode" preserve="all" />
    <type fullname="DG.Tweening.PathType" preserve="all" />
    <type fullname="DG.Tweening.Plugins.Core.PathCore.Path" preserve="all" />
    <type fullname="DG.Tweening.Plugins.Options.ColorOptions" preserve="all" />
    <type fullname="DG.Tweening.Plugins.Options.NoOptions" preserve="all" />
    <type fullname="DG.Tweening.Plugins.Options.PathOptions" preserve="all" />
    <type fullname="DG.Tweening.Plugins.Options.QuaternionOptions" preserve="all" />
    <type fullname="DG.Tweening.Plugins.Options.VectorOptions" preserve="all" />
    <type fullname="DG.Tweening.RotateMode" preserve="all" />
    <type fullname="DG.Tweening.Sequence" preserve="all" />
    <type fullname="DG.Tweening.ShortcutExtensions" preserve="all" />
    <type fullname="DG.Tweening.Tween" preserve="all" />
    <type fullname="DG.Tweening.TweenCallback" preserve="all" />
    <type fullname="DG.Tweening.TweenExtensions" preserve="all" />
    <type fullname="DG.Tweening.TweenSettingsExtensions" preserve="all" />
    <type fullname="DG.Tweening.Tweener" preserve="all" />
  </assembly>
  <!-- <assembly fullname="Google.Protobuf">
    <type fullname="Google.Protobuf.IMessage" preserve="all" />
    <type fullname="Google.Protobuf.MessageExtensions" preserve="all" />
    <type fullname="Google.Protobuf.MessageParser" preserve="all" />
    <type fullname="Google.Protobuf.Reflection.MessageDescriptor" preserve="all" />
  </assembly> -->
  <assembly fullname="LitJson">
    <type fullname="LitJson.JsonData" preserve="all" />
    <type fullname="LitJson.JsonMapper" preserve="all" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Universal.Runtime">
    <type fullname="UnityEngine.Rendering.Universal.CameraExtensions" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.CameraRenderType" preserve="all" />
    <type fullname="UnityEngine.Rendering.Universal.UniversalAdditionalCameraData" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule">
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AssetBundleModule">
    <type fullname="UnityEngine.AssetBundle" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule">
    <type fullname="UnityEngine.AudioClip" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule">
    <type fullname="UnityEngine.AddComponentMenu" preserve="all" />
    <type fullname="UnityEngine.AnimationCurve" preserve="all" />
    <type fullname="UnityEngine.Application" preserve="all" />
    <type fullname="UnityEngine.Application/LogCallback" preserve="all" />
    <type fullname="UnityEngine.AsyncOperation" preserve="all" />
    <type fullname="UnityEngine.Behaviour" preserve="all" />
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.CameraClearFlags" preserve="all" />
    <type fullname="UnityEngine.Color" preserve="all" />
    <type fullname="UnityEngine.Color32" preserve="all" />
    <type fullname="UnityEngine.ColorUtility" preserve="all" />
    <type fullname="UnityEngine.Component" preserve="all" />
    <type fullname="UnityEngine.Coroutine" preserve="all" />
    <type fullname="UnityEngine.CreateAssetMenuAttribute" preserve="all" />
    <type fullname="UnityEngine.Cursor" preserve="all" />
    <type fullname="UnityEngine.CursorMode" preserve="all" />
    <type fullname="UnityEngine.Debug" preserve="all" />
    <type fullname="UnityEngine.DeviceType" preserve="all" />
    <type fullname="UnityEngine.Display" preserve="all" />
    <type fullname="UnityEngine.Events.UnityAction" preserve="all" />
    <type fullname="UnityEngine.Events.UnityAction`1" preserve="all" />
    <type fullname="UnityEngine.Events.UnityAction`2" preserve="all" />
    <type fullname="UnityEngine.Events.UnityEventBase" preserve="all" />
    <type fullname="UnityEngine.Events.UnityEvent`1" preserve="all" />
    <type fullname="UnityEngine.ExecuteInEditMode" preserve="all" />
    <type fullname="UnityEngine.FilterMode" preserve="all" />
    <type fullname="UnityEngine.GL" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Gizmos" preserve="all" />
    <type fullname="UnityEngine.Gradient" preserve="all" />
    <type fullname="UnityEngine.Graphics" preserve="all" />
    <type fullname="UnityEngine.HideFlags" preserve="all" />
    <type fullname="UnityEngine.KeyCode" preserve="all" />
    <type fullname="UnityEngine.LayerMask" preserve="all" />
    <type fullname="UnityEngine.LogType" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.MaterialPropertyBlock" preserve="all" />
    <type fullname="UnityEngine.Mathf" preserve="all" />
    <type fullname="UnityEngine.Matrix4x4" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.PlayerPrefs" preserve="all" />
    <type fullname="UnityEngine.PropertyAttribute" preserve="all" />
    <type fullname="UnityEngine.Quaternion" preserve="all" />
    <type fullname="UnityEngine.Random" preserve="all" />
    <type fullname="UnityEngine.Ray" preserve="all" />
    <type fullname="UnityEngine.Rect" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.RenderTexture" preserve="all" />
    <type fullname="UnityEngine.RenderTextureFormat" preserve="all" />
    <type fullname="UnityEngine.Renderer" preserve="all" />
    <type fullname="UnityEngine.Rendering.BlendMode" preserve="all" />
    <type fullname="UnityEngine.Rendering.ReflectionProbeUsage" preserve="all" />
    <type fullname="UnityEngine.Rendering.ShadowCastingMode" preserve="all" />
    <type fullname="UnityEngine.RequireComponent" preserve="all" />
    <type fullname="UnityEngine.Resources" preserve="all" />
    <type fullname="UnityEngine.RuntimePlatform" preserve="all" />
    <type fullname="UnityEngine.SceneManagement.LoadSceneMode" preserve="all" />
    <type fullname="UnityEngine.SceneManagement.Scene" preserve="all" />
    <type fullname="UnityEngine.SceneManagement.SceneManager" preserve="all" />
    <type fullname="UnityEngine.Screen" preserve="all" />
    <type fullname="UnityEngine.ScriptableObject" preserve="all" />
    <type fullname="UnityEngine.SerializeField" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Space" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.StereoTargetEyeMask" preserve="all" />
    <type fullname="UnityEngine.SystemInfo" preserve="all" />
    <type fullname="UnityEngine.SystemLanguage" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.TextureFormat" preserve="all" />
    <type fullname="UnityEngine.TextureWrapMode" preserve="all" />
    <type fullname="UnityEngine.Time" preserve="all" />
    <type fullname="UnityEngine.TooltipAttribute" preserve="all" />
    <type fullname="UnityEngine.TouchScreenKeyboard" preserve="all" />
    <type fullname="UnityEngine.TouchScreenKeyboard/Status" preserve="all" />
    <type fullname="UnityEngine.TouchScreenKeyboardType" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.Vector2" preserve="all" />
    <type fullname="UnityEngine.Vector2Int" preserve="all" />
    <type fullname="UnityEngine.Vector3" preserve="all" />
    <type fullname="UnityEngine.Vector4" preserve="all" />
    <type fullname="UnityEngine.WaitForSeconds" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.IMGUIModule">
    <type fullname="UnityEngine.Event" preserve="all" />
    <type fullname="UnityEngine.EventModifiers" preserve="all" />
    <type fullname="UnityEngine.EventType" preserve="all" />
    <type fullname="UnityEngine.GUI" preserve="all" />
    <type fullname="UnityEngine.GUILayout" preserve="all" />
    <type fullname="UnityEngine.GUILayoutOption" preserve="all" />
    <type fullname="UnityEngine.TextEditor" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.ImageConversionModule">
    <type fullname="UnityEngine.ImageConversion" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.InputLegacyModule">
    <type fullname="UnityEngine.IMECompositionMode" preserve="all" />
    <type fullname="UnityEngine.Input" preserve="all" />
    <type fullname="UnityEngine.Touch" preserve="all" />
    <type fullname="UnityEngine.TouchPhase" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.JSONSerializeModule">
    <type fullname="UnityEngine.JsonUtility" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.Collider" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.Physics" preserve="all" />
    <type fullname="UnityEngine.RaycastHit" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule">
    <type fullname="UnityEngine.CharacterInfo" preserve="all" />
    <type fullname="UnityEngine.Font" preserve="all" />
    <type fullname="UnityEngine.FontStyle" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.RenderMode" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UnityWebRequestModule">
    <type fullname="UnityEngine.Networking.DownloadHandler" preserve="all" />
    <type fullname="UnityEngine.Networking.UnityWebRequest" preserve="all" />
    <type fullname="UnityEngine.Networking.UnityWebRequestAsyncOperation" preserve="all" />
    <type fullname="UnityEngine.WWWForm" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UnityWebRequestTextureModule">
    <type fullname="UnityEngine.Networking.DownloadHandlerTexture" preserve="all" />
    <type fullname="UnityEngine.Networking.UnityWebRequestTexture" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UnityWebRequestWWWModule">
    <type fullname="UnityEngine.WWW" preserve="all" />
  </assembly>
  <!-- <assembly fullname="UnityWebSocket.Runtime">
    <type fullname="UnityWebSocket.CloseEventArgs" preserve="all" />
    <type fullname="UnityWebSocket.CloseStatusCode" preserve="all" />
    <type fullname="UnityWebSocket.ErrorEventArgs" preserve="all" />
    <type fullname="UnityWebSocket.IWebSocket" preserve="all" />
    <type fullname="UnityWebSocket.MessageEventArgs" preserve="all" />
    <type fullname="UnityWebSocket.OpenEventArgs" preserve="all" />
    <type fullname="UnityWebSocket.WebSocket" preserve="all" />
    <type fullname="UnityWebSocket.WebSocketState" preserve="all" />
  </assembly> -->
  <assembly fullname="Wx">
    <type fullname="WeChatWASM.WX" preserve="all" />
    <type fullname="WeChatWASM.WXBase" preserve="all" />
  </assembly>
  <assembly fullname="netstandard">
    <type fullname="System.Action" preserve="all" />
    <type fullname="System.Action`1" preserve="all" />
    <type fullname="System.Action`2" preserve="all" />
    <type fullname="System.Activator" preserve="all" />
    <type fullname="System.ArgumentException" preserve="all" />
    <type fullname="System.ArgumentNullException" preserve="all" />
    <type fullname="System.ArgumentOutOfRangeException" preserve="all" />
    <type fullname="System.Array" preserve="all" />
    <type fullname="System.AsyncCallback" preserve="all" />
    <type fullname="System.BitConverter" preserve="all" />
    <type fullname="System.Boolean" preserve="all" />
    <type fullname="System.Byte" preserve="all" />
    <type fullname="System.Char" preserve="all" />
    <type fullname="System.Collections.Generic.Dictionary`2" preserve="all" />
    <type fullname="System.Collections.Generic.Dictionary`2/Enumerator" preserve="all" />
    <type fullname="System.Collections.Generic.Dictionary`2/ValueCollection" preserve="all" />
    <type fullname="System.Collections.Generic.Dictionary`2/ValueCollection/Enumerator" preserve="all" />
    <type fullname="System.Collections.Generic.HashSet`1" preserve="all" />
    <type fullname="System.Collections.Generic.ICollection`1" preserve="all" />
    <type fullname="System.Collections.Generic.IComparer`1" preserve="all" />
    <type fullname="System.Collections.Generic.IEnumerable`1" preserve="all" />
    <type fullname="System.Collections.Generic.IEnumerator`1" preserve="all" />
    <type fullname="System.Collections.Generic.IList`1" preserve="all" />
    <type fullname="System.Collections.Generic.KeyValuePair`2" preserve="all" />
    <type fullname="System.Collections.Generic.List`1" preserve="all" />
    <type fullname="System.Collections.Generic.List`1/Enumerator" preserve="all" />
    <type fullname="System.Collections.Generic.Queue`1" preserve="all" />
    <type fullname="System.Collections.Generic.Queue`1/Enumerator" preserve="all" />
    <type fullname="System.Collections.Generic.Stack`1" preserve="all" />
    <type fullname="System.Collections.Generic.Stack`1/Enumerator" preserve="all" />
    <type fullname="System.Collections.Hashtable" preserve="all" />
    <type fullname="System.Collections.IDictionary" preserve="all" />
    <type fullname="System.Collections.IEnumerable" preserve="all" />
    <type fullname="System.Collections.IEnumerator" preserve="all" />
    <type fullname="System.Collections.IList" preserve="all" />
    <type fullname="System.Comparison`1" preserve="all" />
    <type fullname="System.Convert" preserve="all" />
    <type fullname="System.DateTime" preserve="all" />
    <type fullname="System.Delegate" preserve="all" />
    <type fullname="System.Diagnostics.ConditionalAttribute" preserve="all" />
    <type fullname="System.Diagnostics.DebuggableAttribute" preserve="all" />
    <type fullname="System.Diagnostics.DebuggableAttribute/DebuggingModes" preserve="all" />
    <type fullname="System.Diagnostics.DebuggerHiddenAttribute" preserve="all" />
    <type fullname="System.Double" preserve="all" />
    <type fullname="System.Enum" preserve="all" />
    <type fullname="System.EventHandler`1" preserve="all" />
    <type fullname="System.Exception" preserve="all" />
    <type fullname="System.FlagsAttribute" preserve="all" />
    <type fullname="System.Func`1" preserve="all" />
    <type fullname="System.Func`2" preserve="all" />
    <type fullname="System.IAsyncResult" preserve="all" />
    <type fullname="System.IDisposable" preserve="all" />
    <type fullname="System.IO.Directory" preserve="all" />
    <type fullname="System.IO.DirectoryInfo" preserve="all" />
    <type fullname="System.IO.File" preserve="all" />
    <type fullname="System.IO.FileAccess" preserve="all" />
    <type fullname="System.IO.FileInfo" preserve="all" />
    <type fullname="System.IO.FileMode" preserve="all" />
    <type fullname="System.IO.FileStream" preserve="all" />
    <type fullname="System.IO.MemoryStream" preserve="all" />
    <type fullname="System.IO.Path" preserve="all" />
    <type fullname="System.IO.Stream" preserve="all" />
    <type fullname="System.IO.StreamWriter" preserve="all" />
    <type fullname="System.IO.TextWriter" preserve="all" />
    <type fullname="System.IndexOutOfRangeException" preserve="all" />
    <type fullname="System.Int32" preserve="all" />
    <type fullname="System.Int64" preserve="all" />
    <type fullname="System.InvalidOperationException" preserve="all" />
    <type fullname="System.Linq.Enumerable" preserve="all" />
    <type fullname="System.Linq.IOrderedEnumerable`1" preserve="all" />
    <type fullname="System.Math" preserve="all" />
    <type fullname="System.MulticastDelegate" preserve="all" />
    <type fullname="System.NotSupportedException" preserve="all" />
    <type fullname="System.Nullable`1" preserve="all" />
    <type fullname="System.Object" preserve="all" />
    <type fullname="System.ObsoleteAttribute" preserve="all" />
    <type fullname="System.ParamArrayAttribute" preserve="all" />
    <type fullname="System.Predicate`1" preserve="all" />
    <type fullname="System.Reflection.Assembly" preserve="all" />
    <type fullname="System.Reflection.BindingFlags" preserve="all" />
    <type fullname="System.Reflection.DefaultMemberAttribute" preserve="all" />
    <type fullname="System.Reflection.MemberInfo" preserve="all" />
    <type fullname="System.Reflection.MethodBase" preserve="all" />
    <type fullname="System.Reflection.MethodInfo" preserve="all" />
    <type fullname="System.Reflection.PropertyInfo" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.AsyncStateMachineAttribute" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.AsyncVoidMethodBuilder" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.CompilationRelaxationsAttribute" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.CompilerGeneratedAttribute" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.IAsyncStateMachine" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.IteratorStateMachineAttribute" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.RuntimeCompatibilityAttribute" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.RuntimeHelpers" preserve="all" />
    <type fullname="System.Runtime.CompilerServices.TaskAwaiter" preserve="all" />
    <type fullname="System.RuntimeFieldHandle" preserve="all" />
    <type fullname="System.RuntimeTypeHandle" preserve="all" />
    <type fullname="System.Security.Cryptography.HashAlgorithm" preserve="all" />
    <type fullname="System.Security.Cryptography.MD5" preserve="all" />
    <type fullname="System.Security.Cryptography.MD5CryptoServiceProvider" preserve="all" />
    <type fullname="System.Single" preserve="all" />
    <type fullname="System.String" preserve="all" />
    <type fullname="System.StringComparison" preserve="all" />
    <type fullname="System.StringSplitOptions" preserve="all" />
    <type fullname="System.Text.Encoding" preserve="all" />
    <type fullname="System.Text.RegularExpressions.Capture" preserve="all" />
    <type fullname="System.Text.RegularExpressions.Match" preserve="all" />
    <type fullname="System.Text.RegularExpressions.MatchCollection" preserve="all" />
    <type fullname="System.Text.RegularExpressions.Regex" preserve="all" />
    <type fullname="System.Text.StringBuilder" preserve="all" />
    <type fullname="System.Threading.Interlocked" preserve="all" />
    <type fullname="System.Threading.Monitor" preserve="all" />
    <type fullname="System.Threading.Tasks.Task" preserve="all" />
    <type fullname="System.Threading.Tasks.TaskCompletionSource`1" preserve="all" />
    <type fullname="System.Threading.Tasks.Task`1" preserve="all" />
    <type fullname="System.TimeSpan" preserve="all" />
    <type fullname="System.Type" preserve="all" />
    <type fullname="System.UInt32" preserve="all" />
    <type fullname="System.ValueTuple`3" preserve="all" />
    <type fullname="System.ValueType" preserve="all" />
    <type fullname="System.Xml.XmlDocument" preserve="all" />
    <type fullname="System.Xml.XmlElement" preserve="all" />
    <type fullname="System.Xml.XmlNode" preserve="all" />
    <type fullname="System.Xml.XmlNodeList" preserve="all" />
  </assembly>
  <assembly fullname="spine-unity">
    <type fullname="Spine.Animation" preserve="all" />
    <type fullname="Spine.AnimationState" preserve="all" />
    <type fullname="Spine.ExposedList`1" preserve="all" />
    <type fullname="Spine.Skeleton" preserve="all" />
    <type fullname="Spine.SkeletonData" preserve="all" />
    <type fullname="Spine.Skin" preserve="all" />
    <type fullname="Spine.TrackEntry" preserve="all" />
    <type fullname="Spine.Unity.SkeletonAnimation" preserve="all" />
    <type fullname="Spine.Unity.SkeletonDataAsset" preserve="all" />
    <type fullname="Spine.Unity.SkeletonRenderer" preserve="all" />
  </assembly>
  <assembly fullname="wx-runtime">
    <type fullname="PlayerPrefs" preserve="all" />
    <type fullname="WeChatWASM.AuthSetting" preserve="all" />
    <type fullname="WeChatWASM.AuthorizeOption" preserve="all" />
    <type fullname="WeChatWASM.GeneralCallbackResult" preserve="all" />
    <type fullname="WeChatWASM.GetSettingOption" preserve="all" />
    <type fullname="WeChatWASM.GetSettingSuccessCallbackResult" preserve="all" />
    <type fullname="WeChatWASM.HideLoadingOption" preserve="all" />
    <type fullname="WeChatWASM.LoginOption" preserve="all" />
    <type fullname="WeChatWASM.LoginSuccessCallbackResult" preserve="all" />
    <type fullname="WeChatWASM.OnShowListenerResult" preserve="all" />
    <type fullname="WeChatWASM.OpenSettingOption" preserve="all" />
    <type fullname="WeChatWASM.OpenSettingSuccessCallbackResult" preserve="all" />
    <type fullname="WeChatWASM.ShareAppMessageOption" preserve="all" />
    <type fullname="WeChatWASM.ShowLoadingOption" preserve="all" />
    <type fullname="WeChatWASM.ShowModalOption" preserve="all" />
    <type fullname="WeChatWASM.ShowModalSuccessCallbackResult" preserve="all" />
    <type fullname="WeChatWASM.UpdateShareMenuOption" preserve="all" />
    <type fullname="WeChatWASM.VibrateShortOption" preserve="all" />
    <type fullname="WeChatWASM.WXADErrorResponse" preserve="all" />
    <type fullname="WeChatWASM.WXADLoadResponse" preserve="all" />
    <type fullname="WeChatWASM.WXBaseAd" preserve="all" />
    <type fullname="WeChatWASM.WXBaseResponse" preserve="all" />
    <type fullname="WeChatWASM.WXCreateRewardedVideoAdParam" preserve="all" />
    <type fullname="WeChatWASM.WXOpenDataContext" preserve="all" />
    <type fullname="WeChatWASM.WXRewardedVideoAd" preserve="all" />
    <type fullname="WeChatWASM.WXRewardedVideoAdOnCloseResponse" preserve="all" />
    <type fullname="WeChatWASM.WXShareAppMessageParam" preserve="all" />
    <type fullname="WeChatWASM.WXTextResponse" preserve="all" />
    <type fullname="WeChatWASM.WXUserInfo" preserve="all" />
    <type fullname="WeChatWASM.WXUserInfoButton" preserve="all" />
    <type fullname="WeChatWASM.WXUserInfoResponse" preserve="all" />
  </assembly>
</linker>