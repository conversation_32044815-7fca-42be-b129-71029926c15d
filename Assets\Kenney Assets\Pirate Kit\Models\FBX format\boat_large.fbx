; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 179
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/boat_large.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/boat_large.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4645939477916153896, "Model::boat_large_8angles", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",13.1,2.057448E-13,-99.62257
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4697311553117394121, "Geometry::", "Mesh" {
		Vertices: *552 {
			a: 3.4,0,9.35032,3.4,0,0,-3.4,0,9.35032,1.86,0,-2.75652,0,0,-3.81672,-1.86,0,-2.75652,-3.4,-4.511947E-16,0,2.44125,2,-6.135907,-2.887646E-14,2,-7.52742,1.86,0,-2.75652,0,0,-3.81672,2.087608,2,-4.497505,-1.443823E-14,2,-5.687442,2.087608,3.5,-4.497505,-1.443823E-14,3.5,-5.687442,-3.75,2,9.040119,3.75,2,9.040119,-3.75,3.5,9.040119,3.75,3.5,9.040119,0,0,-3.81672,-2.887646E-14,2,-7.52742,-1.86,0,-2.75652,-2.44125,2,-6.135907,-2.44125,2,-6.135907,-2.887646E-14,2,-7.52742,-2.325,3.5,-5.46003,-1.443823E-14,3.5,-6.78528,4.4625,2,10.1985,-4.4625,2,10.1985,4.25,3.5,10.1985,-4.25,3.5,10.1985,-2.725,3.5,10.1985,-2.44125,2,-6.135907,-2.325,3.5,-5.46003,-4.4625,2,-2.517975,-4.25,3.5,-2.01438,4.4625,2,-2.517975,2.44125,2,-6.135907,3.4,0,0,1.86,0,-2.75652,3.4,0,9.35032,-3.4,0,9.35032,4.4625,2,10.1985,-4.4625,2,10.1985,-2.087608,3.5,-4.497505,-2.087608,2,-4.497505,-3.75,3.5,-1.521909,-3.75,2,-1.521909,4.4625,2,-2.517975,4.4625,2,10.1985,4.25,3.5,-2.01438,4.25,3.5,10.1985,3.75,2,6.5773,-3.75,2,6.5773,3.75,2.85,6.5773,-3.75,2.85,6.5773,3.75,2.85,4.4273,3.75,2.85,6.5773,-3.75,2.85,4.4273,-3.75,2.85,6.5773,-3.75,2,-1.521909,-3.75,2.85,-0.4226999,-3.75,3.5,-1.521909,-3.75,3.5,9.040119,-3.75,2.85,1.7273,-3.75,2.85,4.4273,-3.75,2,1.7273,-3.75,2.85,6.5773,-3.75,2,6.5773,-3.75,2,9.040119,-3.75,2,4.4273,-3.75,2,-0.4226999,-3.75,2,4.4273,3.75,2,4.4273,-3.75,2.85,4.4273,3.75,2.85,4.4273,3.75,2,6.5773,3.75,2,9.040119,-3.75,2,6.5773,-3.75,2,9.040119,-2.887646E-14,2,-7.52742,2.44125,2,-6.135907,-1.443823E-14,3.5,-6.78528,2.325,3.5,-5.46003,4.25,3.5,10.1985,3.75,3.5,-1.521909,4.25,3.5,-2.01438,2.325,3.5,-5.46003,2.087608,3.5,-4.497505,-1.443823E-14,3.5,-6.78528,-1.443823E-14,3.5,-5.687442,-2.325,3.5,-5.46003,-2.087608,3.5,-4.497505,-3.75,3.5,-1.521909,-4.25,3.5,-2.01438,-3.75,3.5,9.040119,3.75,3.5,9.040119,-2.725,3.5,10.1985,-4.25,3.5,10.1985,-4.4625,2,-2.517975,-4.25,3.5,-2.01438,-4.4625,2,10.1985,-4.25,3.5,10.1985,2.087608,2,-4.497505,2.087608,3.5,-4.497505,3.75,2,-1.521909,3.75,3.5,-1.521909,-4.4625,2,-2.517975,-4.4625,2,10.1985,-3.4,-4.511947E-16,0,-3.4,0,9.35032,-1.86,0,-2.75652,-2.44125,2,-6.135907,-3.4,-4.511947E-16,0,-4.4625,2,-2.517975,-1.443823E-14,2,-5.687442,-2.087608,2,-4.497505,-1.443823E-14,3.5,-5.687442,-2.087608,3.5,-4.497505,4.4625,2,-2.517975,3.4,0,0,4.4625,2,10.1985,3.4,0,9.35032,2.44125,2,-6.135907,4.4625,2,-2.517975,2.325,3.5,-5.46003,4.25,3.5,-2.01438,3.75,2,-1.521909,3.75,2,-0.4226999,2.087608,2,-4.497505,-3.75,2,-0.4226999,-1.443823E-14,2,-5.687442,-2.087608,2,-4.497505,-3.75,2,-1.521909,4.4625,2,-2.517975,3.75,2,9.040119,4.4625,2,10.1985,-4.4625,2,10.1985,-3.75,2,9.040119,-3.75,2,6.5773,-3.75,2,4.4273,-3.75,2,1.7273,-3.75,2,-0.4226999,-3.75,2,-1.521909,3.75,2,-1.521909,2.44125,2,-6.135907,3.75,2,6.5773,3.75,2,4.4273,3.75,2,1.7273,3.75,2,-0.4226999,2.087608,2,-4.497505,-2.887646E-14,2,-7.52742,-1.443823E-14,2,-5.687442,-2.087608,2,-4.497505,-2.44125,2,-6.135907,-4.4625,2,-2.517975,3.75,2,1.7273,3.75,2.85,1.7273,3.75,2,4.4273,3.75,2.85,4.4273,3.75,3.5,9.040119,3.75,2.85,-0.4226999,3.75,3.5,-1.521909,3.75,2,-0.4226999,3.75,2,-1.521909,3.75,2.85,6.5773,3.75,2,9.040119,3.75,2,6.5773,3.75,2,1.7273,-3.75,2,1.7273,3.75,2.85,1.7273,-3.75,2.85,1.7273,3.75,2,1.7273,3.75,2,4.4273,-3.75,2,1.7273,-3.75,2,4.4273,3.75,2.85,-0.4226999,3.75,2.85,1.7273,-3.75,2.85,-0.4226999,-3.75,2.85,1.7273,-3.75,2,-0.4226999,3.75,2,-0.4226999,-3.75,2.85,-0.4226999,3.75,2.85,-0.4226999
		} 
		PolygonVertexIndex: *378 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,4,-3,6,5,-3,7,9,-9,10,8,-10,11,13,-13,14,12,-14,15,17,-17,18,16,-18,19,21,-21,22,20,-22,23,25,-25,26,24,-26,27,29,-29,30,28,-30,31,30,-30,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,61,-64,65,64,-64,66,64,-66,67,65,-64,68,67,-64,69,68,-64,65,70,-67,61,71,-61,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,85,-88,89,88,-88,90,88,-90,91,90,-90,92,90,-92,93,92,-92,94,93,-92,95,93,-95,85,96,-85,97,84,-97,95,97,-97,98,97,-96,94,98,-96,99,101,-101,102,100,-102,103,105,-105,106,104,-106,107,109,-109,110,108,-110,111,113,-113,114,112,-114,115,117,-117,118,116,-118,119,121,-121,122,120,-122,123,125,-125,126,124,-126,127,129,-129,130,128,-130,131,130,-130,132,130,-132,133,130,-133,134,136,-136,137,135,-137,138,135,-138,139,138,-138,140,139,-138,141,140,-138,142,141,-138,143,142,-138,135,144,-135,145,134,-145,146,144,-136,147,144,-147,146,139,-148,140,147,-140,148,144,-148,149,144,-149,148,141,-150,142,149,-142,150,145,-145,151,145,-151,152,151,-151,153,151,-153,154,151,-154,143,154,-154,155,154,-144,137,155,-144,156,158,-158,158,159,-158,159,160,-158,157,160,-162,160,162,-162,161,162,-164,164,163,-163,165,160,-160,166,160,-166,167,166,-166,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1134 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *368 {
				a: 26.77165,73.62457,26.77165,-2.429472E-33,-26.77165,73.62457,14.64567,-21.70488,0,-30.05291,-14.64567,-21.70488,-26.77165,-8.74257E-33,7.225335,52.05869,29.35119,52.05869,-1.975501,22.18646,14.88229,22.18646,-3.256001,15.74803,-22.17668,15.74803,-3.256001,27.55906,-22.17668,27.55906,29.52756,15.74803,-29.52756,15.74803,29.52756,27.55906,-29.52756,27.55906,-14.88229,22.18646,-29.35119,52.05869,1.975501,22.18646,-7.225335,52.05869,-7.225335,-5.86666,-29.35119,-5.86666,-5.385168,6.989243,-26.45741,6.989243,35.13779,15.74803,-35.13779,15.74803,33.46457,27.55906,-33.46457,27.55906,-21.45669,27.55906,-32.80303,3.990561,-28.60349,16.27975,-0.1710657,3.990561,2.474577,16.27975,0.1710657,40.28699,32.80303,40.28699,-13.05715,17.13315,11.8053,17.13315,26.77165,28.7452,-26.77165,28.7452,35.13779,45.85085,-35.13779,45.85085,22.89875,27.55906,22.89875,15.74803,-3.939653,27.55906,-3.939653,15.74803,19.82657,10.6637,-80.30312,10.6637,15.86126,22.59266,-80.30312,22.59266,29.52756,15.74803,-29.52756,15.74803,29.52756,22.44094,-29.52756,22.44094,-29.52756,34.86063,-29.52756,51.78976,29.52756,34.86063,29.52756,51.78976,11.98354,15.74803,3.328346,22.44094,11.98354,27.55906,-71.18204,27.55906,-13.60079,22.44094,-34.86063,22.44094,-13.60079,15.74803,-51.78976,22.44094,-51.78976,15.74803,-71.18204,15.74803,-34.86063,15.74803,3.328346,15.74803,29.52756,15.74803,-29.52756,15.74803,29.52756,22.44094,-29.52756,22.44094,-29.52756,51.78976,-29.52756,71.18204,29.52756,51.78976,29.52756,71.18204,29.35119,-5.86666,7.225335,-5.86666,26.45741,6.989243,5.385168,6.989243,-33.46457,80.30312,-29.52756,-11.98354,-33.46457,-15.86126,-18.30709,-42.99236,-16.43786,-35.41343,1.136868E-13,-53.4274,1.136868E-13,-44.783,18.30709,-42.99236,16.43786,-35.41343,29.52756,-11.98354,33.46457,-15.86126,29.52756,71.18204,-29.52756,71.18204,21.45669,80.30312,33.46457,80.30312,-19.82657,10.6637,-15.86126,22.59266,80.30312,10.6637,80.30312,22.59266,-22.89875,15.74803,-22.89875,27.55906,3.939653,15.74803,3.939653,27.55906,-19.82657,30.39241,80.30312,30.39241,-8.644318E-16,12.56006,73.62457,12.56006,-11.8053,17.13315,-32.80303,40.28699,13.05715,17.13315,-0.1710657,40.28699,22.17668,15.74803,3.256001,15.74803,22.17668,27.55906,3.256001,27.55906,19.82657,30.39241,0,12.56006,-80.30312,30.39241,-73.62457,12.56006,32.80303,3.990561,0.1710657,3.990561,28.60349,16.27975,-2.474577,16.27975,-29.52756,-11.98354,-29.52756,-3.328346,-16.43786,-35.41343,29.52756,-3.328346,1.128666E-13,-44.783,16.43786,-35.41343,29.52756,-11.98354,35.13779,-19.82657,29.52756,71.18204,35.13779,80.30312,-35.13779,80.30312,-29.52756,71.18204,-29.52756,51.78976,-29.52756,34.86063,-29.52756,13.60079,-29.52756,-3.328346,-29.52756,-11.98354,29.52756,-11.98354,19.22244,-48.31423,29.52756,51.78976,29.52756,34.86063,29.52756,13.60079,29.52756,-3.328346,16.43786,-35.41343,-2.266812E-13,-59.27102,-1.129943E-13,-44.783,-16.43786,-35.41343,-19.22244,-48.31423,-35.13779,-19.82657,13.60079,15.74803,13.60079,22.44094,34.86063,15.74803,34.86063,22.44094,71.18204,27.55906,-3.328346,22.44094,-11.98354,27.55906,-3.328346,15.74803,-11.98354,15.74803,51.78976,22.44094,71.18204,15.74803,51.78976,15.74803,29.52756,15.74803,-29.52756,15.74803,29.52756,22.44094,-29.52756,22.44094,-29.52756,13.60079,-29.52756,34.86063,29.52756,13.60079,29.52756,34.86063,-29.52756,-3.328346,-29.52756,13.60079,29.52756,-3.328346,29.52756,13.60079,29.52756,15.74803,-29.52756,15.74803,29.52756,22.44094,-29.52756,22.44094
				}
			UVIndex: *378 {
				a: 0,2,1,3,1,2,4,3,2,5,4,2,6,5,2,7,9,8,10,8,9,11,13,12,14,12,13,15,17,16,18,16,17,19,21,20,22,20,21,23,25,24,26,24,25,27,29,28,30,28,29,31,30,29,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,61,63,65,64,63,66,64,65,67,65,63,68,67,63,69,68,63,65,70,66,61,71,60,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,85,87,89,88,87,90,88,89,91,90,89,92,90,91,93,92,91,94,93,91,95,93,94,85,96,84,97,84,96,95,97,96,98,97,95,94,98,95,99,101,100,102,100,101,103,105,104,106,104,105,107,109,108,110,108,109,111,113,112,114,112,113,115,117,116,118,116,117,119,121,120,122,120,121,123,125,124,126,124,125,127,129,128,130,128,129,131,130,129,132,130,131,133,130,132,134,136,135,137,135,136,138,135,137,139,138,137,140,139,137,141,140,137,142,141,137,143,142,137,135,144,134,145,134,144,146,144,135,147,144,146,146,139,147,140,147,139,148,144,147,149,144,148,148,141,149,142,149,141,150,145,144,151,145,150,152,151,150,153,151,152,154,151,153,143,154,153,155,154,143,137,155,143,156,158,157,158,159,157,159,160,157,157,160,161,160,162,161,161,162,163,164,163,162,165,160,159,166,160,165,167,166,165,168,170,169,171,169,170,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *126 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh boat_large_8angles, Model::RootNode
	C: "OO",4645939477916153896,0

	;Geometry::, Model::Mesh boat_large_8angles
	C: "OO",4697311553117394121,4645939477916153896

	;Material::wood, Model::Mesh boat_large_8angles
	C: "OO",19362,4645939477916153896

}
