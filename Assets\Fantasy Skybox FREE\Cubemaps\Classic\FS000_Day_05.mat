%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: FS000_Day_05
  m_Shader: {fileID: 103, guid: 0000000000000000f000000000000000, type: 0}
  m_ShaderKeywords: 
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BackTex:
        m_Texture: {fileID: 2800000, guid: 77f0f51d45c9f40c0bca43564f6a3373, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DownTex:
        m_Texture: {fileID: 2800000, guid: 01cc1628045004803a3a09d1a284b53f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FrontTex:
        m_Texture: {fileID: 2800000, guid: 59c477f235773450b9e4023d99aa5bbc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LeftTex:
        m_Texture: {fileID: 2800000, guid: 078a9a22d21c34b97b8d9e358b134710, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RightTex:
        m_Texture: {fileID: 2800000, guid: bf0c073e4d40a475d9c9566ce05de9c0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Tex:
        m_Texture: {fileID: 8900000, guid: 3b9c8413bb7d59b4dbd832a9122271cb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UpTex:
        m_Texture: {fileID: 2800000, guid: 56db82981eabc42c588829a82080d0ce, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Exposure: 1
    - _Rotation: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Tint: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
