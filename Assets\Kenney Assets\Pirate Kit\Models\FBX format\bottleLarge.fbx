; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 307
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/bottleLarge.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/bottleLarge.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5444747137295810428, "Model::bottleLarge", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-34.51283,7.219114E-15,-63.73209
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5524277915013527349, "Geometry::", "Mesh" {
		Vertices: *528 {
			a: 0.625,1.9,-1.225,0.625,1.127987E-16,-1.225,1.225,1.9,-0.625,1.225,1.127987E-16,-0.625,-0.625,-1.127987E-16,-1.225,-0.625,1.9,-1.225,-1.225,-1.127987E-16,-0.625,-1.225,1.9,-0.625,-0.625,3.38396E-16,1.225,-1.225,3.38396E-16,0.625,-0.625,1.9,1.225,-1.225,1.9,0.625,1.225,1.9,0.625,1.225,1.127987E-16,0.625,0.625,1.9,1.225,0.625,1.127987E-16,1.225,-0.625,-1.127987E-16,-1.225,0.625,1.127987E-16,-1.225,-0.625,1.9,-1.225,0.625,1.9,-1.225,-1.225,-1.127987E-16,-0.625,-1.225,1.9,-0.625,-1.225,3.38396E-16,0.625,-1.225,1.9,0.625,1.225,1.9,-0.625,1.225,1.127987E-16,-0.625,1.225,1.9,0.625,1.225,1.127987E-16,0.625,0.625,1.127987E-16,1.225,-0.625,3.38396E-16,1.225,0.625,1.9,1.225,-0.625,1.9,1.225,-0.2242825,3,-0.4395937,0.2242825,3,-0.4395937,-0.2242825,3.65,-0.4395937,0.2242825,3.65,-0.4395937,-0.2242825,3,0.4395937,-0.4395937,3,0.2242825,-0.2242825,3.65,0.4395937,-0.4395937,3.65,0.2242825,0.2242825,3,-0.4395937,0.4395937,3,-0.2242825,0.2242825,3.65,-0.4395937,0.4395937,3.65,-0.2242825,-0.4395937,3,-0.2242825,-0.2242825,3,-0.4395937,-0.4395937,3.65,-0.2242825,-0.2242825,3.65,-0.4395937,0.4395937,3,0.2242825,0.2242825,3,0.4395937,0.4395937,3.65,0.2242825,0.2242825,3.65,0.4395937,0.4395937,3.65,-0.2242825,0.4395937,3,-0.2242825,0.4395937,3.65,0.2242825,0.4395937,3,0.2242825,0.2242825,3,0.4395937,-0.2242825,3,0.4395937,0.2242825,3.65,0.4395937,-0.2242825,3.65,0.4395937,-0.4395937,3,-0.2242825,-0.4395937,3.65,-0.2242825,-0.4395937,3,0.2242825,-0.4395937,3.65,0.2242825,1.225,1.9,-0.625,0.4395937,3,-0.2242825,0.625,1.9,-1.225,0.2242825,3,-0.4395937,-0.2242825,3,0.4395937,-0.625,1.9,1.225,-0.4395937,3,0.2242825,-1.225,1.9,0.625,0.625,1.9,1.225,-0.625,1.9,1.225,0.2242825,3,0.4395937,-0.2242825,3,0.4395937,-0.625,1.9,-1.225,0.625,1.9,-1.225,-0.2242825,3,-0.4395937,0.2242825,3,-0.4395937,-1.225,1.9,-0.625,-0.4395937,3,-0.2242825,-1.225,1.9,0.625,-0.4395937,3,0.2242825,1.225,1.9,0.625,0.625,1.9,1.225,0.4395937,3,0.2242825,0.2242825,3,0.4395937,-0.2242825,3,-0.4395937,-0.4395937,3,-0.2242825,-0.625,1.9,-1.225,-1.225,1.9,-0.625,1.225,1.9,-0.625,1.225,1.9,0.625,0.4395937,3,-0.2242825,0.4395937,3,0.2242825,-0.2803531,3.9,-0.5494921,0.2803531,3.9,-0.5494921,-0.2803531,4.25,-0.5494921,0.2803531,4.25,-0.5494921,0.2242825,3.65,-0.4395937,0.4395937,3.65,-0.2242825,0.2803531,3.9,-0.5494921,0.5494921,3.9,-0.2803531,0.5494921,4.25,-0.2803531,0.5494921,4.25,0.2803531,0.2803531,4.25,-0.5494921,0.2803531,4.25,0.5494921,-0.2803531,4.25,-0.5494921,-0.2803531,4.25,0.5494921,-0.5494921,4.25,-0.2803531,-0.5494921,4.25,0.2803531,-0.4395937,3.65,-0.2242825,-0.2242825,3.65,-0.4395937,-0.5494921,3.9,-0.2803531,-0.2803531,3.9,-0.5494921,0.5494921,3.9,-0.2803531,0.4395937,3.65,-0.2242825,0.5494921,3.9,0.2803531,0.4395937,3.65,0.2242825,-0.2242825,3.65,-0.4395937,0.2242825,3.65,-0.4395937,-0.2803531,3.9,-0.5494921,0.2803531,3.9,-0.5494921,0.5494921,3.9,0.2803531,0.2803531,3.9,0.5494921,0.5494921,4.25,0.2803531,0.2803531,4.25,0.5494921,0.5494921,4.25,-0.2803531,0.5494921,3.9,-0.2803531,0.5494921,4.25,0.2803531,0.5494921,3.9,0.2803531,-0.5494921,3.9,-0.2803531,-0.2803531,3.9,-0.5494921,-0.5494921,4.25,-0.2803531,-0.2803531,4.25,-0.5494921,-0.2803531,3.9,0.5494921,-0.5494921,3.9,0.2803531,-0.2803531,4.25,0.5494921,-0.5494921,4.25,0.2803531,0.2803531,3.9,0.5494921,-0.2803531,3.9,0.5494921,0.2803531,4.25,0.5494921,-0.2803531,4.25,0.5494921,0.2803531,3.9,-0.5494921,0.5494921,3.9,-0.2803531,0.2803531,4.25,-0.5494921,0.5494921,4.25,-0.2803531,-0.5494921,3.9,-0.2803531,-0.5494921,4.25,-0.2803531,-0.5494921,3.9,0.2803531,-0.5494921,4.25,0.2803531,-0.2242825,3.65,0.4395937,-0.4395937,3.65,0.2242825,-0.2803531,3.9,0.5494921,-0.5494921,3.9,0.2803531,-0.5494921,3.9,-0.2803531,-0.5494921,3.9,0.2803531,-0.4395937,3.65,-0.2242825,-0.4395937,3.65,0.2242825,1.225,1.127987E-16,0.625,1.225,1.127987E-16,-0.625,0.625,1.127987E-16,1.225,0.625,1.127987E-16,-1.225,-0.625,3.38396E-16,1.225,-0.625,-1.127987E-16,-1.225,-1.225,3.38396E-16,0.625,-1.225,-1.127987E-16,-0.625,0.2242825,3.65,0.4395937,-0.2242825,3.65,0.4395937,0.2803531,3.9,0.5494921,-0.2803531,3.9,0.5494921,0.4395937,3.65,0.2242825,0.2242825,3.65,0.4395937,0.5494921,3.9,0.2803531,0.2803531,3.9,0.5494921
		} 
		PolygonVertexIndex: *276 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,107,-107,109,107,-109,110,109,-109,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,163,-163,165,163,-165,166,165,-165,167,165,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *828 {
				a: 0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.5623021,0.6063272,-0.5623021,0.5623021,0.6063272,-0.5623021,0.5623021,0.6063272,-0.5623021,0.5623021,0.6063272,-0.5623021,0.5623021,0.6063272,-0.5623021,0.5623021,0.6063272,-0.5623021,-0.5623021,0.6063272,0.5623021,-0.5623021,0.6063272,0.5623021,-0.5623021,0.6063272,0.5623021,-0.5623021,0.6063272,0.5623021,-0.5623021,0.6063272,0.5623021,-0.5623021,0.6063272,0.5623021,0,0.5810874,0.8138412,0,0.5810874,0.8138412,0,0.5810874,0.8138412,0,0.5810874,0.8138412,0,0.5810874,0.8138412,0,0.5810874,0.8138412,0,0.5810874,-0.8138412,0,0.5810874,-0.8138412,0,0.5810874,-0.8138412,0,0.5810874,-0.8138412,0,0.5810874,-0.8138412,0,0.5810874,-0.8138412,-0.8138412,0.5810874,0,-0.8138412,0.5810874,0,-0.8138412,0.5810874,0,-0.8138412,0.5810874,0,-0.8138412,0.5810874,0,-0.8138412,0.5810874,0,0.5623021,0.6063272,0.5623021,0.5623021,0.6063272,0.5623021,0.5623021,0.6063272,0.5623021,0.5623021,0.6063272,0.5623021,0.5623021,0.6063272,0.5623021,0.5623021,0.6063272,0.5623021,-0.5623021,0.6063272,-0.5623021,-0.5623021,0.6063272,-0.5623021,-0.5623021,0.6063272,-0.5623021,-0.5623021,0.6063272,-0.5623021,-0.5623021,0.6063272,-0.5623021,-0.5623021,0.6063272,-0.5623021,0.8138412,0.5810874,0,0.8138412,0.5810874,0,0.8138412,0.5810874,0,0.8138412,0.5810874,0,0.8138412,0.5810874,0,0.8138412,0.5810874,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *352 {
				a: 3.340662,14.96063,3.340662,7.725676E-16,-3.340662,14.96063,-3.340662,7.725676E-16,-3.340662,-7.725676E-16,-3.340662,14.96063,3.340662,-7.725676E-16,3.340662,14.96063,3.340662,2.780146E-15,-3.340662,2.780146E-15,3.340662,14.96063,-3.340662,14.96063,3.340662,14.96063,3.340662,7.725676E-16,-3.340662,14.96063,-3.340662,7.725676E-16,4.92126,-8.881784E-16,-4.92126,8.881784E-16,4.92126,14.96063,-4.92126,14.96063,-4.92126,-8.881784E-16,-4.92126,14.96063,4.92126,2.664535E-15,4.92126,14.96063,4.92126,14.96063,4.92126,8.881784E-16,-4.92126,14.96063,-4.92126,8.881784E-16,4.92126,8.881784E-16,-4.92126,2.664535E-15,4.92126,14.96063,-4.92126,14.96063,1.766004,23.62205,-1.766004,23.62205,1.766004,28.74016,-1.766004,28.74016,1.198803,23.62205,-1.198803,23.62205,1.198803,28.74016,-1.198803,28.74016,1.198803,23.62205,-1.198803,23.62205,1.198803,28.74016,-1.198803,28.74016,1.198803,23.62205,-1.198803,23.62205,1.198803,28.74016,-1.198803,28.74016,1.198803,23.62205,-1.198803,23.62205,1.198803,28.74016,-1.198803,28.74016,1.766004,28.74016,1.766004,23.62205,-1.766004,28.74016,-1.766004,23.62205,1.766004,23.62205,-1.766004,23.62205,1.766004,28.74016,-1.766004,28.74016,-1.766004,23.62205,-1.766004,28.74016,1.766004,23.62205,1.766004,28.74016,-3.340662,5.651524,-1.198803,16.54344,3.340662,5.651524,1.198803,16.54344,1.198803,16.54344,3.340662,5.651524,-1.198803,16.54344,-3.340662,5.651524,4.92126,6.570601,-4.92126,6.570601,1.766004,17.21324,-1.766004,17.21324,4.92126,6.570601,-4.92126,6.570601,1.766004,17.21324,-1.766004,17.21324,-4.92126,6.570601,-1.766004,17.21324,4.92126,6.570601,1.766004,17.21324,3.340662,5.651524,-3.340662,5.651524,1.198803,16.54344,-1.198803,16.54344,-1.198803,16.54344,1.198803,16.54344,-3.340662,5.651524,3.340662,5.651524,4.92126,6.570601,-4.92126,6.570601,1.766004,17.21324,-1.766004,17.21324,2.207505,30.70866,-2.207505,30.70866,2.207505,33.46457,-2.207505,33.46457,1.198803,27.58692,-1.198803,27.58692,1.498504,29.76153,-1.498504,29.76153,-4.32671,-2.207505,-4.32671,2.207505,-2.207505,-4.32671,-2.207505,4.32671,2.207505,-4.32671,2.207505,4.32671,4.32671,-2.207505,4.32671,2.207505,1.198803,27.58692,-1.198803,27.58692,1.498504,29.76153,-1.498504,29.76153,2.207505,29.85349,1.766004,27.70318,-2.207505,29.85349,-1.766004,27.70318,1.766004,27.70318,-1.766004,27.70318,2.207505,29.85349,-2.207505,29.85349,1.498504,30.70866,-1.498504,30.70866,1.498504,33.46457,-1.498504,33.46457,2.207505,33.46457,2.207505,30.70866,-2.207505,33.46457,-2.207505,30.70866,1.498504,30.70866,-1.498504,30.70866,1.498504,33.46457,-1.498504,33.46457,1.498504,30.70866,-1.498504,30.70866,1.498504,33.46457,-1.498504,33.46457,2.207505,30.70866,-2.207505,30.70866,2.207505,33.46457,-2.207505,33.46457,1.498504,30.70866,-1.498504,30.70866,1.498504,33.46457,-1.498504,33.46457,-2.207505,30.70866,-2.207505,33.46457,2.207505,30.70866,2.207505,33.46457,1.198803,27.58692,-1.198803,27.58692,1.498504,29.76153,-1.498504,29.76153,-2.207505,29.85349,2.207505,29.85349,-1.766004,27.70318,1.766004,27.70318,9.645669,4.92126,9.645669,-4.92126,4.92126,9.645669,4.92126,-9.645669,-4.92126,9.645669,-4.92126,-9.645669,-9.645669,4.92126,-9.645669,-4.92126,1.766004,27.70318,-1.766004,27.70318,2.207505,29.85349,-2.207505,29.85349,1.198803,27.58692,-1.198803,27.58692,1.498504,29.76153,-1.498504,29.76153
				}
			UVIndex: *276 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,107,106,109,107,108,110,109,108,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,163,162,165,163,164,166,165,164,167,165,166,168,170,169,171,169,170,172,174,173,175,173,174
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *92 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 16426, "Material::textile", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8196079,0.7529412,0.6705883
			P: "DiffuseColor", "Color", "", "A",0.8196079,0.7529412,0.6705883
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh bottleLarge, Model::RootNode
	C: "OO",5444747137295810428,0

	;Geometry::, Model::Mesh bottleLarge
	C: "OO",5524277915013527349,5444747137295810428

	;Material::textile, Model::Mesh bottleLarge
	C: "OO",16426,5444747137295810428

	;Material::wood, Model::Mesh bottleLarge
	C: "OO",19362,5444747137295810428

}
