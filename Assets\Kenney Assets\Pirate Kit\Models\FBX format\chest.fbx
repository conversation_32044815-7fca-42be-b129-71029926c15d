; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 748
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/chest.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/chest.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5588022771613580682, "Model::chest", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-26.29983,7.219114E-15,-62.94109
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.64,0.64,0.64
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4906214903502137703, "Geometry::", "Mesh" {
		Vertices: *414 {
			a: 3.575,-2.302355E-17,3.15,3.575,-2.302355E-17,2.629254,-3.575,-2.302355E-17,3.15,3.184375,-2.302355E-17,2.629254,3.184375,-2.302355E-17,-2.629254,-3.184375,-2.302355E-17,2.629254,-3.575,-2.302355E-17,2.629254,-3.184375,-2.302355E-17,-2.629254,-3.575,-2.302355E-17,-3.15,3.575,-2.302355E-17,-3.15,3.575,-2.302355E-17,-2.629254,-3.575,-2.302355E-17,-2.629254,3.575,5.15,2.646,2.982777,5.15,-2.053777,3.575,5.15,-2.646,0.89375,5.15,-2.646,-2.982777,5.15,-2.053777,-0.89375,5.15,-2.646,-3.575,5.15,-2.646,-2.982777,5.15,2.053777,2.982777,5.15,2.053777,-3.575,5.15,2.646,-3.575,2.197099,-2.934983,-3.575,5.15,-2.646,-3.575,2.197099,-2.414237,-3.575,4.63173,-2.175974,-3.575,5.15,2.646,-3.575,4.63173,2.175974,-3.575,-2.302355E-17,2.629254,-3.575,-2.302355E-17,3.15,3.575,-2.302355E-17,3.15,-3.575,-2.302355E-17,3.15,3.575,5.15,2.646,-3.575,5.15,2.646,2.982777,1.15,-2.053777,-2.982777,1.15,-2.053777,2.982777,5.15,-2.053777,-2.982777,5.15,-2.053777,-2.982777,5.15,-2.053777,-2.982777,1.15,-2.053777,-2.982777,5.15,2.053777,-2.982777,1.15,2.053777,-2.982777,1.15,2.053777,2.982777,1.15,2.053777,-2.982777,5.15,2.053777,2.982777,5.15,2.053777,3.575,-2.302355E-17,-3.15,3.575,-2.302355E-17,-2.629254,3.575,1.265918,-3.026112,3.575,1.265918,-2.505367,3.184375,-2.302355E-17,-2.629254,3.184375,-2.302355E-17,2.629254,3.184375,4.63173,-2.175974,3.184375,4.63173,2.175974,3.184375,-2.302355E-17,2.629254,3.575,-2.302355E-17,2.629254,3.184375,4.63173,2.175974,3.575,4.63173,2.175974,3.575,-2.302355E-17,-2.629254,3.184375,-2.302355E-17,-2.629254,3.575,1.265918,-2.505367,3.184375,4.63173,-2.175974,3.575,2.197099,-2.414237,3.575,4.63173,-2.175974,3.575,4.63173,2.175974,3.575,4.63173,-2.175974,3.184375,4.63173,2.175974,3.184375,4.63173,-2.175974,-3.184375,-2.302355E-17,-2.629254,-3.184375,4.63173,-2.175974,-3.184375,-2.302355E-17,2.629254,-3.184375,4.63173,2.175974,-3.184375,-2.302355E-17,-2.629254,-3.575,-2.302355E-17,-2.629254,-3.184375,4.63173,-2.175974,-3.575,1.265918,-2.505367,-3.575,2.197099,-2.414237,-3.575,4.63173,-2.175974,-3.575,-2.302355E-17,2.629254,-3.184375,-2.302355E-17,2.629254,-3.575,4.63173,2.175974,-3.184375,4.63173,2.175974,-3.184375,4.63173,2.175974,-3.184375,4.63173,-2.175974,-3.575,4.63173,2.175974,-3.575,4.63173,-2.175974,2.982777,1.15,-2.053777,2.982777,1.15,2.053777,-2.982777,1.15,-2.053777,-2.982777,1.15,2.053777,2.982777,1.15,-2.053777,2.982777,5.15,-2.053777,2.982777,1.15,2.053777,2.982777,5.15,2.053777,3.575,2.197099,-2.934983,-0.89375,3.788869,-2.779206,-3.575,2.197099,-2.934983,-3.575,5.15,-2.646,-0.89375,5.15,-2.646,0.89375,3.788869,-2.779206,3.575,5.15,-2.646,0.89375,5.15,-2.646,-3.575,-2.302355E-17,-3.15,3.575,-2.302355E-17,-3.15,-3.575,1.265918,-3.026112,3.575,1.265918,-3.026112,-3.575,-2.302355E-17,-3.15,-3.575,1.265918,-3.026112,-3.575,-2.302355E-17,-2.629254,-3.575,1.265918,-2.505367,3.575,2.197099,-2.934983,3.575,2.197099,-2.414237,3.575,5.15,-2.646,3.575,4.63173,-2.175974,3.575,5.15,2.646,3.575,4.63173,2.175974,3.575,-2.302355E-17,2.629254,3.575,-2.302355E-17,3.15,-0.89375,3.828313,-3.182252,0.89375,3.828313,-3.182252,-0.89375,5.189444,-3.049046,0.89375,5.189444,-3.049046,0.89375,5.189444,-3.049046,0.89375,5.15,-2.646,-0.89375,5.189444,-3.049046,-0.89375,5.15,-2.646,0.89375,3.828313,-3.182252,0.89375,3.788869,-2.779206,0.89375,5.189444,-3.049046,0.89375,5.15,-2.646,0.89375,3.788869,-2.779206,0.89375,3.828313,-3.182252,-0.89375,3.788869,-2.779206,-0.89375,3.828313,-3.182252,-0.89375,3.828313,-3.182252,-0.89375,5.189444,-3.049046,-0.89375,3.788869,-2.779206,-0.89375,5.15,-2.646
		} 
		PolygonVertexIndex: *276 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,4,-3,6,5,-3,5,7,-5,7,8,-5,8,9,-5,10,4,-10,11,8,-8,12,14,-14,15,13,-15,16,13,-16,17,16,-16,18,16,-18,19,16,-19,13,20,-13,21,12,-21,19,21,-21,18,21,-20,22,24,-24,25,23,-25,26,23,-26,27,26,-26,28,26,-28,29,26,-29,30,32,-32,33,31,-33,34,36,-36,37,35,-37,38,40,-40,41,39,-41,42,44,-44,45,43,-45,46,48,-48,49,47,-49,50,52,-52,53,51,-53,54,56,-56,57,55,-57,58,60,-60,61,59,-61,62,61,-61,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,75,-75,77,76,-75,78,80,-80,81,79,-81,82,84,-84,85,83,-85,86,88,-88,89,87,-89,90,92,-92,93,91,-93,94,96,-96,97,95,-97,98,95,-98,95,99,-95,100,94,-100,101,100,-100,102,104,-104,105,103,-105,106,108,-108,109,107,-109,110,112,-112,113,111,-113,114,113,-113,115,113,-115,116,115,-115,117,116,-115,104,96,-106,94,105,-97,118,120,-120,121,119,-121,122,124,-124,125,123,-125,126,128,-128,129,127,-129,130,132,-132,133,131,-133,134,136,-136,137,135,-137,107,109,-23,24,22,-110,48,110,-50,111,49,-111
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *828 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.09739877,0.9952454,0,0.09739877,0.9952454,0,0.09739877,0.9952454,0,0.09739877,0.9952454,0,0.09739877,0.9952454,0,0.09739877,0.9952454,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-0.09739877,-0.9952454,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.09739877,-0.9952454,0,0.9952454,0.09739877,0,0.9952454,0.09739877,0,0.9952454,0.09739877,0,0.9952454,0.09739877,0,0.9952454,0.09739877,0,0.9952454,0.09739877,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.9952454,-0.09739877,0,-0.9952454,-0.09739877,0,-0.9952454,-0.09739877,0,-0.9952454,-0.09739877,0,-0.9952454,-0.09739877,0,-0.9952454,-0.09739877,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *276 {
				a: 28.14961,24.80315,28.14961,20.70279,-28.14961,24.80315,25.07382,20.70279,25.07382,-20.70279,-25.07382,20.70279,-28.14961,20.70279,-25.07382,-20.70279,-28.14961,-24.80315,28.14961,-24.80315,28.14961,-20.70279,-28.14961,-20.70279,-28.14961,20.83465,-23.48643,-16.17147,-28.14961,-20.83465,-7.037402,-20.83465,23.48643,-16.17147,7.037402,-20.83465,28.14961,-20.83465,23.48643,16.17147,-23.48643,16.17147,28.14961,20.83465,-23.1101,17.29999,-20.83465,40.55118,-19.00974,17.29999,-17.13366,36.47032,20.83465,40.55118,17.13366,36.47032,20.70279,-1.812878E-16,24.80315,-1.812878E-16,28.14961,-2.415797,-28.14961,-2.415797,28.14961,38.32911,-28.14961,38.32911,23.48643,9.055119,-23.48643,9.055119,23.48643,40.55118,-23.48643,40.55118,16.17147,40.55118,16.17147,9.055119,-16.17147,40.55118,-16.17147,9.055119,23.48643,9.055119,-23.48643,9.055119,23.48643,40.55118,-23.48643,40.55118,24.80315,-1.812878E-16,20.70279,-1.812878E-16,23.82765,9.967855,19.72729,9.967855,20.70279,-1.812878E-16,-20.70279,-1.812878E-16,17.13366,36.47032,-17.13366,36.47032,-25.07382,-2.016427,-28.14961,-2.016427,-25.07382,34.62812,-28.14961,34.62812,28.14961,-2.016427,25.07382,-2.016427,28.14961,7.999048,25.07382,34.62812,28.14961,15.36621,28.14961,34.62812,28.14961,17.13366,28.14961,-17.13366,25.07382,17.13366,25.07382,-17.13366,-20.70279,-1.812878E-16,-17.13366,36.47032,20.70279,-1.812878E-16,17.13366,36.47032,-25.07382,-2.016427,-28.14961,-2.016427,-25.07382,34.62812,-28.14961,7.999048,-28.14961,15.36621,-28.14961,34.62812,28.14961,-2.016427,25.07382,-2.016427,28.14961,34.62812,25.07382,34.62812,-25.07382,17.13366,-25.07382,-17.13366,-28.14961,17.13366,-28.14961,-17.13366,-23.48643,-16.17147,-23.48643,16.17147,23.48643,-16.17147,23.48643,16.17147,-16.17147,9.055119,-16.17147,40.55118,16.17147,9.055119,16.17147,40.55118,-28.14961,14.96684,7.037402,27.56034,28.14961,14.96684,28.14961,38.32911,7.037402,38.32911,-7.037402,27.56034,-28.14961,38.32911,-7.037402,38.32911,28.14961,-2.415797,-28.14961,-2.415797,28.14961,7.599679,-28.14961,7.599679,-24.80315,-1.812878E-16,-23.82765,9.967855,-20.70279,-1.812878E-16,-19.72729,9.967855,23.1101,17.29999,19.00974,17.29999,20.83465,40.55118,17.13366,36.47032,-20.83465,40.55118,-17.13366,36.47032,-20.70279,-1.812878E-16,-24.80315,-1.812878E-16,7.037402,27.56034,-7.037402,27.56034,7.037402,38.32911,-7.037402,38.32911,7.037402,27.87398,7.037402,24.68522,-7.037402,27.87398,-7.037402,24.68522,25.0571,30.1442,21.88351,29.83361,24.00824,40.86176,20.83465,40.55118,-7.037402,24.68522,-7.037402,27.87398,7.037402,24.68522,7.037402,27.87398,-25.0571,30.1442,-24.00824,40.86176,-21.88351,29.83361,-20.83465,40.55118
				}
			UVIndex: *276 {
				a: 0,2,1,3,1,2,4,3,2,5,4,2,6,5,2,5,7,4,7,8,4,8,9,4,10,4,9,11,8,7,12,14,13,15,13,14,16,13,15,17,16,15,18,16,17,19,16,18,13,20,12,21,12,20,19,21,20,18,21,19,22,24,23,25,23,24,26,23,25,27,26,25,28,26,27,29,26,28,30,32,31,33,31,32,34,36,35,37,35,36,38,40,39,41,39,40,42,44,43,45,43,44,46,48,47,49,47,48,50,52,51,53,51,52,54,56,55,57,55,56,58,60,59,61,59,60,62,61,60,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,75,74,77,76,74,78,80,79,81,79,80,82,84,83,85,83,84,86,88,87,89,87,88,90,92,91,93,91,92,94,96,95,97,95,96,98,95,97,95,99,94,100,94,99,101,100,99,102,104,103,105,103,104,106,108,107,109,107,108,110,112,111,113,111,112,114,113,112,115,113,114,116,115,114,117,116,114,104,96,105,94,105,96,118,120,119,121,119,120,122,124,123,125,123,124,126,128,127,129,127,128,130,132,131,133,131,132,134,136,135,137,135,136,107,109,22,24,22,109,48,110,49,111,49,110
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *92 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 4814720672468519837, "Model::lid", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,5.15,2.646
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5682268634820913793, "Geometry::", "Mesh" {
		Vertices: *1650 {
			a: -3.575,0,-5.292,-3.575,0.6848352,-5.20184,-3.575,0,0,-3.575,1.323,-4.937503,-3.575,1.871004,-4.517004,-3.575,2.291503,-3.969,-3.575,2.55584,-3.330835,-3.575,2.646,-2.646,-3.575,2.55584,-1.961165,-3.575,2.291503,-1.323,-3.575,1.871004,-0.7749954,-3.575,1.323,-0.3544968,-3.575,0.6848352,-0.09016026,3.575,0,0,3.575,0,-5.292,-3.575,0,0,-3.575,0,-5.292,3.575,0,-5.292,3.575,0,0,3.575,0.6848352,-5.20184,3.575,1.323,-4.937503,3.575,1.871004,-4.517004,3.575,2.291503,-3.969,3.575,2.55584,-3.330835,3.575,2.646,-2.646,3.575,2.55584,-1.961165,3.575,2.291503,-1.323,3.575,1.871004,-0.7749954,3.575,1.323,-0.3544968,3.575,0.6848352,-0.09016026,3.825,0,-5.292,3.825,0.05288882,-5.69373,3.575,0,-5.292,3.04375,0.05288882,-5.69373,-3.575,0,-5.292,2.2625,0.05288882,-5.69373,-2.2625,0.05288882,-5.69373,-3.04375,0.05288882,-5.69373,-3.825,0.05288882,-5.69373,-3.825,0,-5.292,-3.825,0.05288882,-5.69373,-3.04375,0.05288882,-5.69373,-3.825,0.7906128,-5.596607,-3.04375,0.7906128,-5.596607,-3.825,0.7906128,-5.596607,-3.04375,0.7906128,-5.596607,-3.825,1.527347,-5.291442,-3.04375,1.527347,-5.291442,-3.825,1.527347,-5.291442,-3.04375,1.527347,-5.291442,-3.825,2.159994,-4.805995,-3.04375,2.159994,-4.805995,-3.04375,2.159994,-4.805995,-3.04375,2.645442,-4.173347,-3.825,2.159994,-4.805995,-3.825,2.645442,-4.173347,-3.04375,2.645442,-4.173347,-3.04375,2.950607,-3.436613,-3.825,2.645442,-4.173347,-3.825,2.950607,-3.436613,-3.04375,2.950607,-3.436613,-3.04375,3.054693,-2.646,-3.825,2.950607,-3.436613,-3.825,3.054693,-2.646,-3.04375,3.054693,-2.646,-3.04375,2.950607,-1.855387,-3.825,3.054693,-2.646,-3.825,2.950607,-1.855387,-3.04375,2.950607,-1.855387,-3.04375,2.645442,-1.118653,-3.825,2.950607,-1.855387,-3.825,2.645442,-1.118653,-3.04375,2.645442,-1.118653,-3.04375,2.159994,-0.4860056,-3.825,2.645442,-1.118653,-3.825,2.159994,-0.4860056,-3.04375,1.527347,-0.0005579193,-3.825,1.527347,-0.0005579193,-3.04375,2.159994,-0.4860056,-3.825,2.159994,-0.4860056,-3.04375,0.7906128,0.3046072,-3.825,0.7906128,0.3046072,-3.04375,1.527347,-0.0005579193,-3.825,1.527347,-0.0005579193,-3.04375,0.05288882,0.4017304,-3.825,0.05288882,0.4017304,-3.04375,0.7906128,0.3046072,-3.825,0.7906128,0.3046072,3.825,0.05288882,0.4017304,3.825,0,0,3.04375,0.05288882,0.4017304,3.575,0,0,-3.575,0,0,2.2625,0.05288882,0.4017304,-2.2625,0.05288882,0.4017304,-3.04375,0.05288882,0.4017304,-3.825,0.05288882,0.4017304,-3.825,0,0,-3.825,0.05288882,-5.69373,-3.825,0.7906128,-5.596607,-3.825,0,-5.292,-3.825,1.527347,-5.291442,-3.825,0.6848352,-5.20184,-3.825,2.159994,-4.805995,-3.825,1.323,-4.937503,-3.825,1.871004,-4.517004,-3.825,2.645442,-4.173347,-3.825,2.291503,-3.969,-3.825,2.950607,-3.436613,-3.825,2.55584,-3.330835,-3.825,3.054693,-2.646,-3.825,2.646,-2.646,-3.825,2.55584,-1.961165,-3.825,2.950607,-1.855387,-3.825,2.291503,-1.323,-3.825,2.645442,-1.118653,-3.825,1.871004,-0.7749954,-3.825,2.159994,-0.4860056,-3.825,1.323,-0.3544968,-3.825,1.527347,-0.0005579193,-3.825,0.6848352,-0.09016026,-3.825,0,0,-3.825,0.7906128,0.3046072,-3.825,0.05288882,0.4017304,-3.575,0,-5.292,-3.825,0,-5.292,-3.575,0.6848352,-5.20184,-3.825,0.6848352,-5.20184,-3.575,0.6848352,-5.20184,-3.825,0.6848352,-5.20184,-3.575,1.323,-4.937503,-3.825,1.323,-4.937503,-3.575,1.323,-4.937503,-3.825,1.323,-4.937503,-3.575,1.871004,-4.517004,-3.825,1.871004,-4.517004,-3.575,2.291503,-3.969,-3.575,1.871004,-4.517004,-3.825,2.291503,-3.969,-3.825,1.871004,-4.517004,-3.575,2.55584,-3.330835,-3.575,2.291503,-3.969,-3.825,2.55584,-3.330835,-3.825,2.291503,-3.969,-3.575,2.646,-2.646,-3.575,2.55584,-3.330835,-3.825,2.646,-2.646,-3.825,2.55584,-3.330835,-3.575,2.55584,-1.961165,-3.575,2.646,-2.646,-3.825,2.55584,-1.961165,-3.825,2.646,-2.646,-3.575,2.291503,-1.323,-3.575,2.55584,-1.961165,-3.825,2.291503,-1.323,-3.825,2.55584,-1.961165,-3.575,1.871004,-0.7749954,-3.575,2.291503,-1.323,-3.825,1.871004,-0.7749954,-3.825,2.291503,-1.323,-3.825,1.323,-0.3544968,-3.575,1.323,-0.3544968,-3.825,1.871004,-0.7749954,-3.575,1.871004,-0.7749954,-3.825,0.6848352,-0.09016026,-3.575,0.6848352,-0.09016026,-3.825,1.323,-0.3544968,-3.575,1.323,-0.3544968,-3.825,0,0,-3.575,0,0,-3.825,0.6848352,-0.09016026,-3.575,0.6848352,-0.09016026,3.825,0.05288882,-5.69373,3.825,0,-5.292,3.825,0.7906128,-5.596607,3.825,1.527347,-5.291442,3.825,0.6848352,-5.20184,3.825,2.159994,-4.805995,3.825,1.323,-4.937503,3.825,1.871004,-4.517004,3.825,2.645442,-4.173347,3.825,2.291503,-3.969,3.825,2.950607,-3.436613,3.825,2.55584,-3.330835,3.825,3.054693,-2.646,3.825,2.646,-2.646,3.825,2.950607,-1.855387,3.825,2.55584,-1.961165,3.825,2.291503,-1.323,3.825,2.645442,-1.118653,3.825,1.871004,-0.7749954,3.825,2.159994,-0.4860056,3.825,1.323,-0.3544968,3.825,1.527347,-0.0005579193,3.825,0.6848352,-0.09016026,3.825,0,0,3.825,0.7906128,0.3046072,3.825,0.05288882,0.4017304,3.575,0,0,3.825,0,0,3.575,0.6848352,-0.09016026,3.825,0.6848352,-0.09016026,3.575,0.6848352,-0.09016026,3.825,0.6848352,-0.09016026,3.575,1.323,-0.3544968,3.825,1.323,-0.3544968,3.575,1.323,-0.3544968,3.825,1.323,-0.3544968,3.575,1.871004,-0.7749954,3.825,1.871004,-0.7749954,3.825,1.871004,-0.7749954,3.825,2.291503,-1.323,3.575,1.871004,-0.7749954,3.575,2.291503,-1.323,3.825,2.291503,-1.323,3.825,2.55584,-1.961165,3.575,2.291503,-1.323,3.575,2.55584,-1.961165,3.825,2.55584,-1.961165,3.825,2.646,-2.646,3.575,2.55584,-1.961165,3.575,2.646,-2.646,3.825,2.646,-2.646,3.825,2.55584,-3.330835,3.575,2.646,-2.646,3.575,2.55584,-3.330835,3.825,2.55584,-3.330835,3.825,2.291503,-3.969,3.575,2.55584,-3.330835,3.575,2.291503,-3.969,3.825,2.291503,-3.969,3.825,1.871004,-4.517004,3.575,2.291503,-3.969,3.575,1.871004,-4.517004,3.825,1.323,-4.937503,3.575,1.323,-4.937503,3.825,1.871004,-4.517004,3.575,1.871004,-4.517004,3.825,0.6848352,-5.20184,3.575,0.6848352,-5.20184,3.825,1.323,-4.937503,3.575,1.323,-4.937503,3.825,0,-5.292,3.575,0,-5.292,3.825,0.6848352,-5.20184,3.575,0.6848352,-5.20184,3.825,0.7906128,0.3046072,3.04375,0.7906128,0.3046072,3.825,1.527347,-0.0005579193,3.04375,1.527347,-0.0005579193,3.825,2.645442,-4.173347,3.825,2.950607,-3.436613,3.04375,2.645442,-4.173347,3.04375,2.950607,-3.436613,3.04375,0.7906128,-5.596607,3.825,0.7906128,-5.596607,3.04375,1.527347,-5.291442,3.825,1.527347,-5.291442,3.825,0.05288882,0.4017304,3.04375,0.05288882,0.4017304,3.825,0.7906128,0.3046072,3.04375,0.7906128,0.3046072,3.825,2.645442,-1.118653,3.825,2.159994,-0.4860056,3.04375,2.645442,-1.118653,3.04375,2.159994,-0.4860056,3.825,2.950607,-1.855387,3.825,2.645442,-1.118653,3.04375,2.950607,-1.855387,3.04375,2.645442,-1.118653,3.825,2.159994,-4.805995,3.825,2.645442,-4.173347,3.04375,2.159994,-4.805995,3.04375,2.645442,-4.173347,3.825,1.527347,-0.0005579193,3.04375,1.527347,-0.0005579193,3.825,2.159994,-0.4860056,3.04375,2.159994,-0.4860056,3.825,2.950607,-3.436613,3.825,3.054693,-2.646,3.04375,2.950607,-3.436613,3.04375,3.054693,-2.646,3.04375,1.527347,-5.291442,3.825,1.527347,-5.291442,3.04375,2.159994,-4.805995,3.825,2.159994,-4.805995,3.825,3.054693,-2.646,3.825,2.950607,-1.855387,3.04375,3.054693,-2.646,3.04375,2.950607,-1.855387,3.04375,0.05288882,-5.69373,3.825,0.05288882,-5.69373,3.04375,0.7906128,-5.596607,3.825,0.7906128,-5.596607,2.2625,3.054693,-2.646,2.2625,2.950607,-1.855387,-2.2625,3.054693,-2.646,-2.2625,2.950607,-1.855387,2.2625,2.950607,-1.855387,2.2625,2.645442,-1.118653,-2.2625,2.950607,-1.855387,-2.2625,2.645442,-1.118653,2.2625,2.950607,-3.436613,2.2625,3.054693,-2.646,-2.2625,2.950607,-3.436613,-2.2625,3.054693,-2.646,2.2625,2.645442,-1.118653,2.2625,2.159994,-0.4860056,-2.2625,2.645442,-1.118653,-2.2625,2.159994,-0.4860056,2.2625,0.05288882,0.4017304,-2.2625,0.05288882,0.4017304,2.2625,0.7906128,0.3046072,-2.2625,0.7906128,0.3046072,2.2625,1.527347,-0.0005579193,-2.2625,1.527347,-0.0005579193,2.2625,2.159994,-0.4860056,-2.2625,2.159994,-0.4860056,2.2625,2.645442,-4.173347,2.2625,2.950607,-3.436613,-2.2625,2.645442,-4.173347,-2.2625,2.950607,-3.436613,-2.2625,0.7906128,-5.596607,2.2625,0.7906128,-5.596607,-2.2625,1.527347,-5.291442,2.2625,1.527347,-5.291442,-2.2625,0.05288882,-5.69373,2.2625,0.05288882,-5.69373,-2.2625,0.7906128,-5.596607,2.2625,0.7906128,-5.596607,2.2625,2.159994,-4.805995,2.2625,2.645442,-4.173347,-2.2625,2.159994,-4.805995,-2.2625,2.645442,-4.173347,2.2625,0.7906128,0.3046072,-2.2625,0.7906128,0.3046072,2.2625,1.527347,-0.0005579193,-2.2625,1.527347,-0.0005579193,-2.2625,1.527347,-5.291442,2.2625,1.527347,-5.291442,-2.2625,2.159994,-4.805995,2.2625,2.159994,-4.805995,3.04375,1.715741,0.3257501,2.2625,1.715741,0.3257501,3.04375,2.426424,-0.2195762,2.2625,2.426424,-0.2195762,3.04375,2.97175,-0.9302593,3.04375,2.426424,-0.2195762,2.2625,2.97175,-0.9302593,2.2625,2.426424,-0.2195762,3.04375,3.314557,-1.757867,3.04375,2.97175,-0.9302593,2.2625,3.314557,-1.757867,2.2625,2.97175,-0.9302593,3.04375,2.97175,-4.36174,3.04375,3.314557,-3.534132,2.2625,2.97175,-4.36174,2.2625,3.314557,-3.534132,3.04375,0.1016488,-6.064099,3.04375,0.05288882,-5.69373,3.04375,0.8881328,-5.960557,3.04375,1.715741,-5.61775,3.04375,0.7906128,-5.596607,3.04375,2.426424,-5.072424,3.04375,1.527347,-5.291442,3.04375,2.159994,-4.805995,3.04375,2.97175,-4.36174,3.04375,2.645442,-4.173347,3.04375,3.314557,-3.534132,3.04375,2.950607,-3.436613,3.04375,3.431481,-2.646,3.04375,3.054693,-2.646,3.04375,3.314557,-1.757867,3.04375,2.950607,-1.855387,3.04375,2.645442,-1.118653,3.04375,2.97175,-0.9302593,3.04375,2.159994,-0.4860056,3.04375,2.426424,-0.2195762,3.04375,1.527347,-0.0005579193,3.04375,1.715741,0.3257501,3.04375,0.7906128,0.3046072,3.04375,0.05288882,0.4017304,3.04375,0.8881328,0.6685566,3.04375,0.1016488,0.772099,2.2625,0.8881328,-5.960557,3.04375,0.8881328,-5.960557,2.2625,1.715741,-5.61775,3.04375,1.715741,-5.61775,3.04375,0.1016488,0.772099,2.2625,0.1016488,0.772099,3.04375,3.314557,-3.534132,3.04375,3.431481,-2.646,2.2625,3.314557,-3.534132,2.2625,3.431481,-2.646,2.2625,0.1016488,-6.064099,3.04375,0.1016488,-6.064099,2.2625,0.8881328,-5.960557,3.04375,0.8881328,-5.960557,3.04375,0.8881328,0.6685566,2.2625,0.8881328,0.6685566,3.04375,1.715741,0.3257501,2.2625,1.715741,0.3257501,3.04375,0.1016488,0.772099,2.2625,0.1016488,0.772099,3.04375,0.8881328,0.6685566,2.2625,0.8881328,0.6685566,2.2625,0.1016488,-6.064099,2.2625,0.8881328,-5.960557,2.2625,0.05288882,-5.69373,2.2625,1.715741,-5.61775,2.2625,0.7906128,-5.596607,2.2625,2.426424,-5.072424,2.2625,1.527347,-5.291442,2.2625,2.159994,-4.805995,2.2625,2.97175,-4.36174,2.2625,2.645442,-4.173347,2.2625,3.314557,-3.534132,2.2625,2.950607,-3.436613,2.2625,3.431481,-2.646,2.2625,3.054693,-2.646,2.2625,3.314557,-1.757867,2.2625,2.950607,-1.855387,2.2625,2.645442,-1.118653,2.2625,2.97175,-0.9302593,2.2625,2.159994,-0.4860056,2.2625,2.426424,-0.2195762,2.2625,1.527347,-0.0005579193,2.2625,1.715741,0.3257501,2.2625,0.7906128,0.3046072,2.2625,0.05288882,0.4017304,2.2625,0.8881328,0.6685566,2.2625,0.1016488,0.772099,2.2625,1.715741,-5.61775,3.04375,1.715741,-5.61775,2.2625,2.426424,-5.072424,3.04375,2.426424,-5.072424,3.04375,3.431481,-2.646,3.04375,3.314557,-1.757867,2.2625,3.431481,-2.646,2.2625,3.314557,-1.757867,3.04375,2.426424,-5.072424,3.04375,2.97175,-4.36174,2.2625,2.426424,-5.072424,2.2625,2.97175,-4.36174,3.04375,0.1016488,-6.064099,2.2625,0.1016488,-6.064099,-3.04375,0.1016488,-6.064099,-3.04375,0.8881328,-5.960557,-3.04375,0.05288882,-5.69373,-3.04375,1.715741,-5.61775,-3.04375,0.7906128,-5.596607,-3.04375,2.426424,-5.072424,-3.04375,1.527347,-5.291442,-3.04375,2.159994,-4.805995,-3.04375,2.97175,-4.36174,-3.04375,2.645442,-4.173347,-3.04375,3.314557,-3.534132,-3.04375,2.950607,-3.436613,-3.04375,3.431481,-2.646,-3.04375,3.054693,-2.646,-3.04375,3.314557,-1.757867,-3.04375,2.950607,-1.855387,-3.04375,2.645442,-1.118653,-3.04375,2.97175,-0.9302593,-3.04375,2.159994,-0.4860056,-3.04375,2.426424,-0.2195762,-3.04375,1.527347,-0.0005579193,-3.04375,1.715741,0.3257501,-3.04375,0.7906128,0.3046072,-3.04375,0.05288882,0.4017304,-3.04375,0.8881328,0.6685566,-3.04375,0.1016488,0.772099,-2.2625,2.97175,-4.36174,-2.2625,3.314557,-3.534132,-3.04375,2.97175,-4.36174,-3.04375,3.314557,-3.534132,-2.2625,2.426424,-5.072424,-2.2625,2.97175,-4.36174,-3.04375,2.426424,-5.072424,-3.04375,2.97175,-4.36174,-2.2625,1.715741,0.3257501,-3.04375,1.715741,0.3257501,-2.2625,2.426424,-0.2195762,-3.04375,2.426424,-0.2195762,-3.04375,1.715741,-5.61775,-2.2625,1.715741,-5.61775,-3.04375,2.426424,-5.072424,-2.2625,2.426424,-5.072424,-3.04375,0.8881328,-5.960557,-2.2625,0.8881328,-5.960557,-3.04375,1.715741,-5.61775,-2.2625,1.715741,-5.61775,-3.04375,0.1016488,-6.064099,-2.2625,0.1016488,-6.064099,-3.04375,0.8881328,-5.960557,-2.2625,0.8881328,-5.960557,-2.2625,3.314557,-1.757867,-2.2625,2.97175,-0.9302593,-3.04375,3.314557,-1.757867,-3.04375,2.97175,-0.9302593,-2.2625,0.1016488,-6.064099,-3.04375,0.1016488,-6.064099,-2.2625,0.1016488,0.772099,-3.04375,0.1016488,0.772099,-2.2625,2.97175,-0.9302593,-2.2625,2.426424,-0.2195762,-3.04375,2.97175,-0.9302593,-3.04375,2.426424,-0.2195762,-2.2625,3.431481,-2.646,-2.2625,3.314557,-1.757867,-3.04375,3.431481,-2.646,-3.04375,3.314557,-1.757867,-2.2625,3.314557,-3.534132,-2.2625,3.431481,-2.646,-3.04375,3.314557,-3.534132,-3.04375,3.431481,-2.646,-2.2625,0.1016488,0.772099,-3.04375,0.1016488,0.772099,-2.2625,0.8881328,0.6685566,-3.04375,0.8881328,0.6685566,-2.2625,0.8881328,0.6685566,-3.04375,0.8881328,0.6685566,-2.2625,1.715741,0.3257501,-3.04375,1.715741,0.3257501,-2.2625,0.1016488,-6.064099,-2.2625,0.05288882,-5.69373,-2.2625,0.8881328,-5.960557,-2.2625,1.715741,-5.61775,-2.2625,0.7906128,-5.596607,-2.2625,2.426424,-5.072424,-2.2625,1.527347,-5.291442,-2.2625,2.159994,-4.805995,-2.2625,2.97175,-4.36174,-2.2625,2.645442,-4.173347,-2.2625,3.314557,-3.534132,-2.2625,2.950607,-3.436613,-2.2625,3.431481,-2.646,-2.2625,3.054693,-2.646,-2.2625,3.314557,-1.757867,-2.2625,2.950607,-1.855387,-2.2625,2.645442,-1.118653,-2.2625,2.97175,-0.9302593,-2.2625,2.159994,-0.4860056,-2.2625,2.426424,-0.2195762,-2.2625,1.527347,-0.0005579193,-2.2625,1.715741,0.3257501,-2.2625,0.7906128,0.3046072,-2.2625,0.05288882,0.4017304,-2.2625,0.8881328,0.6685566,-2.2625,0.1016488,0.772099
		} 
		PolygonVertexIndex: *1080 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,4,-3,6,5,-3,7,6,-3,8,7,-3,9,8,-3,10,9,-3,11,10,-3,12,11,-3,13,15,-15,16,14,-16,17,19,-19,20,18,-20,21,18,-21,22,18,-22,23,18,-23,24,18,-24,25,18,-25,26,18,-26,27,18,-27,28,18,-28,29,18,-29,30,32,-32,33,31,-33,34,33,-33,35,33,-35,36,35,-35,37,36,-35,38,37,-35,39,38,-35,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,91,-91,93,92,-91,94,92,-94,95,92,-95,96,92,-96,97,92,-97,98,100,-100,101,99,-101,102,101,-101,103,101,-103,104,103,-103,105,103,-105,106,103,-106,107,106,-106,108,106,-108,109,108,-108,110,108,-110,111,110,-110,112,110,-112,113,110,-113,114,113,-113,115,113,-115,116,115,-115,117,115,-117,118,117,-117,119,117,-119,120,119,-119,121,119,-121,122,119,-122,123,122,-122,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,173,-176,177,176,-176,178,176,-178,179,178,-178,180,179,-178,181,179,-181,182,181,-181,183,181,-183,184,183,-183,185,183,-185,186,185,-185,187,185,-187,188,187,-187,189,188,-187,190,188,-190,191,190,-190,192,190,-192,193,192,-192,194,192,-194,195,194,-194,196,195,-194,197,195,-197,198,200,-200,201,199,-201,202,204,-204,205,203,-205,206,208,-208,209,207,-209,210,212,-212,213,211,-213,214,216,-216,217,215,-217,218,220,-220,221,219,-221,222,224,-224,225,223,-225,226,228,-228,229,227,-229,230,232,-232,233,231,-233,234,236,-236,237,235,-237,238,240,-240,241,239,-241,242,244,-244,245,243,-245,246,248,-248,249,247,-249,250,252,-252,253,251,-253,254,256,-256,257,255,-257,258,260,-260,261,259,-261,262,264,-264,265,263,-265,266,268,-268,269,267,-269,270,272,-272,273,271,-273,274,276,-276,277,275,-277,278,280,-280,281,279,-281,282,284,-284,285,283,-285,286,288,-288,289,287,-289,290,292,-292,293,291,-293,294,296,-296,297,295,-297,298,300,-300,301,299,-301,302,304,-304,305,303,-305,306,308,-308,309,307,-309,310,312,-312,313,311,-313,314,316,-316,317,315,-317,318,320,-320,321,319,-321,322,324,-324,325,323,-325,326,328,-328,329,327,-329,330,332,-332,333,331,-333,334,336,-336,337,335,-337,338,340,-340,341,339,-341,342,344,-344,345,343,-345,346,348,-348,349,347,-349,350,352,-352,353,351,-353,354,356,-356,357,355,-357,358,360,-360,361,359,-361,362,359,-362,363,362,-362,364,362,-364,365,364,-364,366,365,-364,367,365,-367,368,367,-367,369,367,-369,370,369,-369,371,369,-371,372,371,-371,373,371,-373,374,373,-373,375,374,-373,376,374,-376,377,376,-376,378,376,-378,379,378,-378,380,378,-380,381,380,-380,382,381,-380,383,381,-383,384,386,-386,387,385,-387,388,389,-91,93,90,-390,390,392,-392,393,391,-393,394,396,-396,397,395,-397,398,400,-400,401,399,-401,402,404,-404,405,403,-405,406,408,-408,409,407,-409,410,409,-409,411,409,-411,412,411,-411,413,411,-413,414,411,-414,415,414,-414,416,414,-416,417,416,-416,418,416,-418,419,418,-418,420,418,-420,421,420,-420,422,420,-422,423,420,-423,424,423,-423,425,423,-425,426,425,-425,427,425,-427,428,427,-427,429,427,-429,430,427,-430,431,430,-430,432,434,-434,435,433,-435,436,438,-438,439,437,-439,440,442,-442,443,441,-443,33,35,-445,445,444,-36,446,448,-448,449,447,-449,450,449,-449,451,449,-451,452,451,-451,453,451,-453,454,451,-454,455,454,-454,456,454,-456,457,456,-456,458,456,-458,459,458,-458,460,458,-460,461,460,-460,462,460,-462,463,460,-463,464,463,-463,465,463,-465,466,465,-465,467,465,-467,468,467,-467,469,467,-469,470,467,-470,471,470,-470,472,474,-474,475,473,-475,476,478,-478,479,477,-479,480,482,-482,483,481,-483,484,486,-486,487,485,-487,488,490,-490,491,489,-491,492,494,-494,495,493,-495,496,498,-498,499,497,-499,36,37,-501,501,500,-38,502,503,-95,95,94,-504,504,506,-506,507,505,-507,508,510,-510,511,509,-511,512,514,-514,515,513,-515,516,518,-518,519,517,-519,520,522,-522,523,521,-523,524,526,-526,527,525,-527,528,525,-528,529,528,-528,530,528,-530,531,530,-530,532,531,-530,533,531,-533,534,533,-533,535,533,-535,536,535,-535,537,535,-537,538,537,-537,539,537,-539,540,539,-539,541,540,-539,542,540,-542,543,542,-542,544,542,-544,545,544,-544,546,544,-546,547,546,-546,548,547,-546,549,547,-549
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *3240 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.7071068,-0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.8660254,-0.5,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,1,0,0,1,0,0,1,0,0,0.9659258,-0.258819,0,1,0,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.9659258,0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.8660254,0.5,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.258819,0.9659258,0,-0.5,0.8660254,0,-0.258819,0.9659258,0,-0.5,0.8660254,0,-0.258819,0.9659258,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.7071068,0.7071068,0,-0.5,0.8660254,0,-0.7071068,0.7071068,0,-0.5,0.8660254,0,-0.7071068,0.7071068,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.8660254,0.5,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.9659258,0.258819,0,-1,0,0,-1,0,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-1,0,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-1,0,0,-1,0,0,-1,0,0,-0.9659258,-0.258819,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.8660254,-0.5,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.7071068,-0.7071068,0,-0.5,-0.8660254,0,-0.7071068,-0.7071068,0,-0.5,-0.8660254,0,-0.7071068,-0.7071068,0,-0.5,-0.8660254,0,-0.7071068,-0.7071068,0,-0.258819,-0.9659258,0,-0.5,-0.8660254,0,-0.258819,-0.9659258,0,-0.5,-0.8660254,0,-0.258819,-0.9659258,0,-0.5,-0.8660254,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,0,-0.258819,-0.9659258,0,-0.5,-0.8660254,0,-0.258819,-0.9659258,0,-0.5,-0.8660254,0,-0.258819,-0.9659258,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.7071068,-0.7071068,0,-0.5,-0.8660254,0,-0.7071068,-0.7071068,0,-0.5,-0.8660254,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.7071068,-0.7071068,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.8660254,-0.5,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-1,0,0,-1,0,0,-1,0,0,-0.9659258,-0.258819,0,-1,0,0,-1,0,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-1,0,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.9659258,0.258819,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.8660254,0.5,0,-0.5,0.8660254,0,-0.7071068,0.7071068,0,-0.5,0.8660254,0,-0.7071068,0.7071068,0,-0.5,0.8660254,0,-0.7071068,0.7071068,0,-0.258819,0.9659258,0,-0.5,0.8660254,0,-0.258819,0.9659258,0,-0.5,0.8660254,0,-0.258819,0.9659258,0,-0.5,0.8660254,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.8660254,-0.5,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.8660254,0.5,0,0.8660254,0.5,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.8660254,0.5,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.9659258,0.258819,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.7071068,-0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,1,0,0,1,0,0,1,0,0,0.9659258,-0.258819,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,1,0,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,1,0,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,1,0,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.9659258,0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,1,0,0,1,0,0,1,0,0,0.9659258,-0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.8660254,0.5,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.8660254,-0.5,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.7071068,-0.7071068,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.8660254,0.5,0,0.8660254,0.5,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.8660254,0.5,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.9659258,0.258819,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.8660254,-0.5,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,1,0,0,1,0,0,1,0,0,0.9659258,-0.258819,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,1,0,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,1,0,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.7071068,-0.7071068,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,0.8660254,-0.5,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.7071068,-0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.5,-0.8660254,0,0.7071068,-0.7071068,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.258819,-0.9659258,0,0.5,-0.8660254,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.1305262,-0.9914449,0,0.258819,-0.9659258,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.9659258,0.258819,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,0.8660254,0.5,0,0.8660254,0.5,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.8660254,0.5,0,1,0,0,1,0,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,1,0,0,0.9659258,-0.258819,0,0.9659258,-0.258819,0,1,0,0,1,0,0,1,0,0,0.9659258,-0.258819,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,0,0.258819,0.9659258,0,0.5,0.8660254,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1100 {
				a: -41.66929,0,-40.95937,5.392403,0,0,-38.87798,10.41732,-35.56696,14.73232,-31.25197,18.04333,-26.22705,20.12472,-20.83465,20.83465,-15.44224,20.12472,-10.41732,18.04333,-6.102326,14.73232,-2.791313,10.41732,-0.7099233,5.392403,28.14961,0,28.14961,-41.66929,-28.14961,0,-28.14961,-41.66929,41.66929,0,0,0,40.95937,5.392403,38.87798,10.41732,35.56696,14.73232,31.25197,18.04333,26.22705,20.12472,20.83465,20.83465,15.44224,20.12472,10.41732,18.04333,6.102326,14.73232,2.791313,10.41732,0.7099233,5.392403,-30.11811,41.31281,-30.11811,44.50333,-28.14961,41.31281,-23.96654,44.50333,28.14961,41.31281,-17.81496,44.50333,17.81496,44.50333,23.96654,44.50333,30.11811,44.50333,30.11811,41.31281,30.11811,-5.438934,23.96654,-5.438934,30.11811,0.4200409,23.96654,0.4200409,30.11811,-11.11258,23.96654,-11.11258,30.11811,-4.833566,23.96654,-4.833566,30.11811,-15.82284,23.96654,-15.82284,30.11811,-9.54382,23.96654,-9.54382,23.96654,-19.66874,23.96654,-13.38973,30.11811,-19.66874,30.11811,-13.38973,23.96654,-22.38821,23.96654,-16.1092,30.11811,-22.38821,30.11811,-16.1092,23.96654,-23.79591,23.96654,-17.5169,30.11811,-23.79591,30.11811,-17.5169,-23.96654,23.79591,-23.96654,17.5169,-30.11811,23.79591,-30.11811,17.5169,-23.96654,22.38821,-23.96654,16.1092,-30.11811,22.38821,-30.11811,16.1092,-23.96654,19.66874,-23.96654,13.38973,-30.11811,19.66874,-30.11811,13.38973,-23.96654,9.54382,-30.11811,9.54382,-23.96654,15.82284,-30.11811,15.82284,-23.96654,4.833566,-30.11811,4.833566,-23.96654,11.11258,-30.11811,11.11258,-23.96654,-1.221245E-14,-30.11811,-1.221245E-14,-23.96654,5.858975,-30.11811,5.858975,30.11811,3.190527,30.11811,-3.170428E-17,23.96654,3.190527,28.14961,-2.963211E-17,-28.14961,2.963211E-17,17.81496,3.190527,-17.81496,3.190527,-23.96654,3.190527,-30.11811,3.190527,-30.11811,3.170428E-17,-44.83252,0.4164474,-44.06777,6.225298,-41.66929,4.271211E-14,-41.6649,12.02635,-40.95937,5.392403,-37.84248,17.00783,-38.87798,10.41732,-35.56696,14.73232,-32.861,20.83025,-31.25197,18.04333,-27.05994,23.23313,-26.22705,20.12472,-20.83465,24.0527,-20.83465,20.83465,-15.44224,20.12472,-14.60935,23.23313,-10.41732,18.04333,-8.808293,20.83025,-6.102326,14.73232,-3.826816,17.00783,-2.791313,10.41732,-0.004393066,12.02635,-0.7099233,5.392403,5.746318E-29,4.271211E-14,2.398482,6.225298,3.163232,0.4164474,-28.14961,-5.438934,-30.11811,-5.438934,-28.14961,-2.726708E-13,-30.11811,-2.726708E-13,-28.14961,-10.69254,-30.11811,-10.69254,-28.14961,-5.253607,-30.11811,-5.253607,-28.14961,-15.4028,-30.11811,-15.4028,-28.14961,-9.963861,-30.11811,-9.963861,-28.14961,-13.80977,-28.14961,-19.2487,-30.11811,-13.80977,-30.11811,-19.2487,-28.14961,-16.52924,-28.14961,-21.96817,-30.11811,-16.52924,-30.11811,-21.96817,-28.14961,-17.93694,-28.14961,-23.37587,-30.11811,-17.93694,-30.11811,-23.37587,28.14961,17.93694,28.14961,23.37587,30.11811,17.93694,30.11811,23.37587,28.14961,16.52924,28.14961,21.96817,30.11811,16.52924,30.11811,21.96817,28.14961,13.80977,28.14961,19.2487,30.11811,13.80977,30.11811,19.2487,30.11811,9.963861,28.14961,9.963861,30.11811,15.4028,28.14961,15.4028,30.11811,5.253607,28.14961,5.253607,30.11811,10.69254,28.14961,10.69254,30.11811,8.003008E-27,28.14961,7.479935E-27,30.11811,5.438934,28.14961,5.438934,44.83252,0.4164474,41.66929,0,44.06777,6.225298,41.6649,12.02635,40.95937,5.392403,37.84248,17.00783,38.87798,10.41732,35.56696,14.73232,32.861,20.83025,31.25197,18.04333,27.05994,23.23313,26.22705,20.12472,20.83465,24.0527,20.83465,20.83465,14.60935,23.23313,15.44224,20.12472,10.41732,18.04333,8.808293,20.83025,6.102326,14.73232,3.826816,17.00783,2.791313,10.41732,0.004393066,12.02635,0.7099233,5.392403,0,0,-2.398482,6.225298,-3.163232,0.4164474,-28.14961,7.479935E-27,-30.11811,8.003008E-27,-28.14961,5.438934,-30.11811,5.438934,-28.14961,5.253607,-30.11811,5.253607,-28.14961,10.69254,-30.11811,10.69254,-28.14961,9.963861,-30.11811,9.963861,-28.14961,15.4028,-30.11811,15.4028,-30.11811,13.80977,-30.11811,19.2487,-28.14961,13.80977,-28.14961,19.2487,-30.11811,16.52924,-30.11811,21.96817,-28.14961,16.52924,-28.14961,21.96817,-30.11811,17.93694,-30.11811,23.37587,-28.14961,17.93694,-28.14961,23.37587,30.11811,-17.93694,30.11811,-23.37587,28.14961,-17.93694,28.14961,-23.37587,30.11811,-16.52924,30.11811,-21.96817,28.14961,-16.52924,28.14961,-21.96817,30.11811,-13.80977,30.11811,-19.2487,28.14961,-13.80977,28.14961,-19.2487,30.11811,-15.4028,28.14961,-15.4028,30.11811,-9.963861,28.14961,-9.963861,30.11811,-10.69254,28.14961,-10.69254,30.11811,-5.253607,28.14961,-5.253607,30.11811,-5.438934,28.14961,-5.438934,30.11811,-2.726708E-13,28.14961,-2.726708E-13,30.11811,4.833566,23.96654,4.833566,30.11811,11.11258,23.96654,11.11258,-30.11811,-22.38821,-30.11811,-16.1092,-23.96654,-22.38821,-23.96654,-16.1092,-23.96654,-11.11258,-30.11811,-11.11258,-23.96654,-4.833566,-30.11811,-4.833566,30.11811,-1.221245E-14,23.96654,-1.221245E-14,30.11811,5.858975,23.96654,5.858975,30.11811,19.66874,30.11811,13.38973,23.96654,19.66874,23.96654,13.38973,30.11811,22.38821,30.11811,16.1092,23.96654,22.38821,23.96654,16.1092,-30.11811,-19.66874,-30.11811,-13.38973,-23.96654,-19.66874,-23.96654,-13.38973,30.11811,9.54382,23.96654,9.54382,30.11811,15.82284,23.96654,15.82284,-30.11811,-23.79591,-30.11811,-17.5169,-23.96654,-23.79591,-23.96654,-17.5169,-23.96654,-15.82284,-30.11811,-15.82284,-23.96654,-9.54382,-30.11811,-9.54382,30.11811,23.79591,30.11811,17.5169,23.96654,23.79591,23.96654,17.5169,-23.96654,-5.438934,-30.11811,-5.438934,-23.96654,0.4200409,-30.11811,0.4200409,17.81496,23.79591,17.81496,17.5169,-17.81496,23.79591,-17.81496,17.5169,17.81496,22.38821,17.81496,16.1092,-17.81496,22.38821,-17.81496,16.1092,-17.81496,-23.79591,-17.81496,-17.5169,17.81496,-23.79591,17.81496,-17.5169,17.81496,19.66874,17.81496,13.38973,-17.81496,19.66874,-17.81496,13.38973,17.81496,-1.221245E-14,-17.81496,-1.221245E-14,17.81496,5.858975,-17.81496,5.858975,17.81496,9.54382,-17.81496,9.54382,17.81496,15.82284,-17.81496,15.82284,-17.81496,-22.38821,-17.81496,-16.1092,17.81496,-22.38821,17.81496,-16.1092,17.81496,-11.11258,-17.81496,-11.11258,17.81496,-4.833566,-17.81496,-4.833566,17.81496,-5.438934,-17.81496,-5.438934,17.81496,0.4200409,-17.81496,0.4200409,-17.81496,-19.66874,-17.81496,-13.38973,17.81496,-19.66874,17.81496,-13.38973,17.81496,4.833566,-17.81496,4.833566,17.81496,11.11258,-17.81496,11.11258,17.81496,-15.82284,-17.81496,-15.82284,17.81496,-9.54382,-17.81496,-9.54382,23.96654,9.156571,17.81496,9.156571,23.96654,16.21009,17.81496,16.21009,23.96654,20.05599,23.96654,13.00248,17.81496,20.05599,17.81496,13.00248,23.96654,22.77546,23.96654,15.72194,17.81496,22.77546,17.81496,15.72194,-23.96654,-22.77546,-23.96654,-15.72194,-17.81496,-22.77546,-17.81496,-15.72194,47.74881,0.800384,44.83252,0.4164474,46.93352,6.993171,44.23425,13.50977,44.06777,6.225298,39.94035,19.1057,41.6649,12.02635,37.84248,17.00783,34.34441,23.39961,32.861,20.83025,27.82782,26.09887,27.05994,23.23313,20.83465,27.01954,20.83465,24.0527,13.84147,26.09887,14.60935,23.23313,8.808293,20.83025,7.324876,23.39961,3.826816,17.00783,1.728947,19.1057,0.004393066,12.02635,-2.564961,13.50977,-2.398482,6.225298,-3.163232,0.4164474,-5.264225,6.993171,-6.07952,0.800384,-17.81496,-11.49983,-23.96654,-11.49983,-17.81496,-4.446316,-23.96654,-4.446316,23.96654,6.13198,17.81496,6.13198,-23.96654,-24.18316,-23.96654,-17.12964,-17.81496,-24.18316,-17.81496,-17.12964,-17.81496,-5.438934,-23.96654,-5.438934,-17.81496,0.8072906,-23.96654,0.8072906,23.96654,4.446316,17.81496,4.446316,23.96654,11.49983,17.81496,11.49983,23.96654,-1.953993E-14,17.81496,-1.953993E-14,23.96654,6.246224,17.81496,6.246224,-47.74881,0.800384,-46.93352,6.993171,-44.83252,0.4164474,-44.23425,13.50977,-44.06777,6.225298,-39.94035,19.1057,-41.6649,12.02635,-37.84248,17.00783,-34.34441,23.39961,-32.861,20.83025,-27.82782,26.09887,-27.05994,23.23313,-20.83465,27.01954,-20.83465,24.0527,-13.84147,26.09887,-14.60935,23.23313,-8.808293,20.83025,-7.324876,23.39961,-3.826816,17.00783,-1.728947,19.1057,-0.004393066,12.02635,2.564961,13.50977,2.398482,6.225298,3.163232,0.4164474,5.264225,6.993171,6.07952,0.800384,-17.81496,-16.21009,-23.96654,-16.21009,-17.81496,-9.156571,-23.96654,-9.156571,23.96654,24.18316,23.96654,17.12964,17.81496,24.18316,17.81496,17.12964,-23.96654,-20.05599,-23.96654,-13.00248,-17.81496,-20.05599,-17.81496,-13.00248,-23.96654,47.44479,-17.81496,47.44479,-47.74881,0.800384,-46.93352,6.993171,-44.83252,0.4164474,-44.23425,13.50977,-44.06777,6.225298,-39.94035,19.1057,-41.6649,12.02635,-37.84248,17.00783,-34.34441,23.39961,-32.861,20.83025,-27.82782,26.09887,-27.05994,23.23313,-20.83465,27.01954,-20.83465,24.0527,-13.84147,26.09887,-14.60935,23.23313,-8.808293,20.83025,-7.324876,23.39961,-3.826816,17.00783,-1.728947,19.1057,-0.004393066,12.02635,2.564961,13.50977,2.398482,6.225298,3.163232,0.4164474,5.264225,6.993171,6.07952,0.800384,17.81496,-22.77546,17.81496,-15.72194,23.96654,-22.77546,23.96654,-15.72194,17.81496,-20.05599,17.81496,-13.00248,23.96654,-20.05599,23.96654,-13.00248,-17.81496,9.156571,-23.96654,9.156571,-17.81496,16.21009,-23.96654,16.21009,23.96654,-16.21009,17.81496,-16.21009,23.96654,-9.156571,17.81496,-9.156571,23.96654,-11.49983,17.81496,-11.49983,23.96654,-4.446316,17.81496,-4.446316,23.96654,-5.438934,17.81496,-5.438934,23.96654,0.8072906,17.81496,0.8072906,-17.81496,22.77546,-17.81496,15.72194,-23.96654,22.77546,-23.96654,15.72194,17.81496,47.44479,23.96654,47.44479,-17.81496,6.13198,-23.96654,6.13198,-17.81496,20.05599,-17.81496,13.00248,-23.96654,20.05599,-23.96654,13.00248,-17.81496,24.18316,-17.81496,17.12964,-23.96654,24.18316,-23.96654,17.12964,17.81496,-24.18316,17.81496,-17.12964,23.96654,-24.18316,23.96654,-17.12964,-17.81496,-1.953993E-14,-23.96654,-1.953993E-14,-17.81496,6.246224,-23.96654,6.246224,-17.81496,4.446316,-23.96654,4.446316,-17.81496,11.49983,-23.96654,11.49983,47.74881,0.800384,44.83252,0.4164474,46.93352,6.993171,44.23425,13.50977,44.06777,6.225298,39.94035,19.1057,41.6649,12.02635,37.84248,17.00783,34.34441,23.39961,32.861,20.83025,27.82782,26.09887,27.05994,23.23313,20.83465,27.01954,20.83465,24.0527,13.84147,26.09887,14.60935,23.23313,8.808293,20.83025,7.324876,23.39961,3.826816,17.00783,1.728947,19.1057,0.004393066,12.02635,-2.564961,13.50977,-2.398482,6.225298,-3.163232,0.4164474,-5.264225,6.993171,-6.07952,0.800384
				}
			UVIndex: *1080 {
				a: 0,2,1,3,1,2,4,3,2,5,4,2,6,5,2,7,6,2,8,7,2,9,8,2,10,9,2,11,10,2,12,11,2,13,15,14,16,14,15,17,19,18,20,18,19,21,18,20,22,18,21,23,18,22,24,18,23,25,18,24,26,18,25,27,18,26,28,18,27,29,18,28,30,32,31,33,31,32,34,33,32,35,33,34,36,35,34,37,36,34,38,37,34,39,38,34,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,91,90,93,92,90,94,92,93,95,92,94,96,92,95,97,92,96,98,100,99,101,99,100,102,101,100,103,101,102,104,103,102,105,103,104,106,103,105,107,106,105,108,106,107,109,108,107,110,108,109,111,110,109,112,110,111,113,110,112,114,113,112,115,113,114,116,115,114,117,115,116,118,117,116,119,117,118,120,119,118,121,119,120,122,119,121,123,122,121,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166,168,170,169,171,169,170,172,174,173,175,173,174,176,173,175,177,176,175,178,176,177,179,178,177,180,179,177,181,179,180,182,181,180,183,181,182,184,183,182,185,183,184,186,185,184,187,185,186,188,187,186,189,188,186,190,188,189,191,190,189,192,190,191,193,192,191,194,192,193,195,194,193,196,195,193,197,195,196,198,200,199,201,199,200,202,204,203,205,203,204,206,208,207,209,207,208,210,212,211,213,211,212,214,216,215,217,215,216,218,220,219,221,219,220,222,224,223,225,223,224,226,228,227,229,227,228,230,232,231,233,231,232,234,236,235,237,235,236,238,240,239,241,239,240,242,244,243,245,243,244,246,248,247,249,247,248,250,252,251,253,251,252,254,256,255,257,255,256,258,260,259,261,259,260,262,264,263,265,263,264,266,268,267,269,267,268,270,272,271,273,271,272,274,276,275,277,275,276,278,280,279,281,279,280,282,284,283,285,283,284,286,288,287,289,287,288,290,292,291,293,291,292,294,296,295,297,295,296,298,300,299,301,299,300,302,304,303,305,303,304,306,308,307,309,307,308,310,312,311,313,311,312,314,316,315,317,315,316,318,320,319,321,319,320,322,324,323,325,323,324,326,328,327,329,327,328,330,332,331,333,331,332,334,336,335,337,335,336,338,340,339,341,339,340,342,344,343,345,343,344,346,348,347,349,347,348,350,352,351,353,351,352,354,356,355,357,355,356,358,360,359,361,359,360,362,359,361,363,362,361,364,362,363,365,364,363,366,365,363,367,365,366,368,367,366,369,367,368,370,369,368,371,369,370,372,371,370,373,371,372,374,373,372,375,374,372,376,374,375,377,376,375,378,376,377,379,378,377,380,378,379,381,380,379,382,381,379,383,381,382,384,386,385,387,385,386,388,389,90,93,90,389,390,392,391,393,391,392,394,396,395,397,395,396,398,400,399,401,399,400,402,404,403,405,403,404,406,408,407,409,407,408,410,409,408,411,409,410,412,411,410,413,411,412,414,411,413,415,414,413,416,414,415,417,416,415,418,416,417,419,418,417,420,418,419,421,420,419,422,420,421,423,420,422,424,423,422,425,423,424,426,425,424,427,425,426,428,427,426,429,427,428,430,427,429,431,430,429,432,434,433,435,433,434,436,438,437,439,437,438,440,442,441,443,441,442,33,35,444,445,444,35,446,448,447,449,447,448,450,449,448,451,449,450,452,451,450,453,451,452,454,451,453,455,454,453,456,454,455,457,456,455,458,456,457,459,458,457,460,458,459,461,460,459,462,460,461,463,460,462,464,463,462,465,463,464,466,465,464,467,465,466,468,467,466,469,467,468,470,467,469,471,470,469,472,474,473,475,473,474,476,478,477,479,477,478,480,482,481,483,481,482,484,486,485,487,485,486,488,490,489,491,489,490,492,494,493,495,493,494,496,498,497,499,497,498,36,37,500,501,500,37,502,503,94,95,94,503,504,506,505,507,505,506,508,510,509,511,509,510,512,514,513,515,513,514,516,518,517,519,517,518,520,522,521,523,521,522,524,526,525,527,525,526,528,525,527,529,528,527,530,528,529,531,530,529,532,531,529,533,531,532,534,533,532,535,533,534,536,535,534,537,535,536,538,537,536,539,537,538,540,539,538,541,540,538,542,540,541,543,542,541,544,542,543,545,544,543,546,544,545,547,546,545,548,547,545,549,547,548
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *360 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 11274, "Material::stone", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5843138,0.6862745,0.7568628
			P: "DiffuseColor", "Color", "", "A",0.5843138,0.6862745,0.7568628
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh chest, Model::RootNode
	C: "OO",5588022771613580682,0

	;Geometry::, Model::Mesh chest
	C: "OO",4906214903502137703,5588022771613580682

	;Material::wood, Model::Mesh chest
	C: "OO",19362,5588022771613580682

	;Material::stone, Model::Mesh chest
	C: "OO",11274,5588022771613580682

	;Model::Mesh lid, Model::USING PARENT
	C: "OO",4814720672468519837,5588022771613580682

	;Geometry::, Model::Mesh lid
	C: "OO",5682268634820913793,4814720672468519837

	;Material::wood, Model::Mesh lid
	C: "OO",19362,4814720672468519837

	;Material::stone, Model::Mesh lid
	C: "OO",11274,4814720672468519837

}
