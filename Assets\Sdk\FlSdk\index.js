import './publish-sdk';
export const SDK_MODULE_NAME = 'Platform';

window.flsdk = {
    _send: null,
    FlSdkInit() {
        /**
        * appid 为平台ID
        * version 版本号 (首包可设置1.0.1为初始版本)
        * gameName 游戏名
        * videoAdUnitID 视频id(无请求接口时默认)
        * isOpenLog 输出日志开关
        */
        let params = {
            "appId": "wx292540f88785ad37",
            "version": "1.0.0",
            "gameName": "连连看王者",
            "videoAdUnitID": "adunit-43d59155a0fcbf22"
        };
        window.gameSdk.sdk.setOpenAnalysisLog(false);
        /** 初始化sdk */
        window.gameSdk.sdk.init({
            ...params, callback(err, res) {
                if (!err) {
                    console.log(err, res);
                    var abTest = res.sdkConfig.abTest;
                    window.flsdk.ActiveReport();
                    window.flsdk.Send2Unity("OnFlSdkInit", abTest);
                    // 获取后台配置
                    window.gameSdk.sdk.getBackStageConfig({
                        callback(err, res) {
                            console.log("获取配置====", res);
                        }
                    })
                }
            }
        });
    },
    GetAbTest() {
        return "";
    },
    Send2Unity(method, str = '') {
        if (!this._send) {
            this._send = GameGlobal.Module.SendMessage;
        }
        this._send(SDK_MODULE_NAME, method, str);
    },
    ActiveReport() {
        window.gameSdk.sdk.activeReport();
        console.log("[flsdk] activeReport");
    },
    ShowRewardVideoAd(positionTag) {
        console.log("[flsdk] showRewardVideoAd");
        window.gameSdk.sdk.showRewardVideoAd({
            positionTag:positionTag,
            callback: (err, res) => {
                if (!err) {
                    window.flsdk.Send2Unity("OnRewardVideoAdSuccess");
                    console.log("视频观看获取奖励成功1!")
                } else {
                    window.flsdk.Send2Unity("OnRewardVideoAdFail", res.code);
                    // 根据游戏内容需求是否需要使用
                    if (res.code == 1001) {
                        console.log("视频获取显示失败");
                    }else{
                        console.log("未完整观看视频");
                    }
                }
            }
        });
    },
    ShareAppMessage(positionTag) {
        console.log("[flsdk] shareAppMessage:" + positionTag);
        window.gameSdk.sdk.shareAppMessage({
            positionTag: positionTag,
            callback: (err, res) => {
                if (!err) {
                    console.log("分享成功");
                    window.flsdk.Send2Unity("OnShareSuccess");
                } else {
                    console.log("分享失败");
                    window.flsdk.Send2Unity("OnShareFail");
                }
            }
        });
    },
    SendEvent(eventName, data, opt) {
        var reportData = JSON.parse(data);
        if (opt == "") {
            window.gameSdk.sdk.sendEvent(eventName, reportData);
        } else {
            window.gameSdk.sdk.sendEvent(eventName, reportData, opt);
        }
        // console.log("=======report=======",eventName, reportData);
    },
    SetCurrentLevel(level) {
        window.gameSdk.sdk.setCurrentLevel(level);
    },
    VideoExposure(count) {
        window.gameSdk.sdk.videoExposure(count);
    },
    /**
     * 添加公共基础埋点字段(所有埋点事件都会传送该字段集,重复调用该方法,参数字段名一样,会新值覆盖旧值)
     * @param {Object} params - 包含基础字段的对象
     * @param {number} params.max_chapter_level - 章节关卡，上报具体关卡数。其他模式上报：0
     * @param {number} params.current_stamina_value - 当前剩余体力值
     * @param {number} params.prop_a_remaining_quantity - 道具一剩余数量（提示道具）
     * @param {number} params.prop_b_remaining_quantity - 道具二剩余数量（刷新道具）
     * @param {number} params.prop_c_remaining_quantity - 道具三剩余数量（移除道具）
     */
    AddEventBaseParams(data) {
        try {
            const params = JSON.parse(data);
            window.gameSdk.sdk.addEventBaseParams(params);
            // console.log("[flsdk] AddEventBaseParams:", params);
        } catch (error) {
            // console.error("[flsdk] AddEventBaseParams error:", error);
        }
    }
}

