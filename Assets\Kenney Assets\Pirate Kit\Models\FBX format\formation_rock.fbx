; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 785
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/formation_rock.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/formation_rock.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4954269913661939513, "Model::formation_rock", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",56.95,1.985256E-13,-106.6883
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4893865541086961890, "Geometry::", "Mesh" {
		Vertices: *792 {
			a: -3.150675,8.452499,4.921279,-2.937849,6.272,4.921279,-4.613175,8.452499,7.454404,-3.089082,6.272,5.183224,-3.196893,4.450736,5.677851,-4.417881,4.450736,7.792664,-5.075796,12.2475,4.523478,-4.620512,8.452499,4.523478,-5.691273,12.2475,5.589515,-5.463631,8.452499,5.983803,-5.463631,8.452499,3.063154,-4.620512,8.452499,4.523478,-5.691273,12.2475,3.457442,-5.075796,12.2475,4.523478,-5.463631,8.452499,5.983803,-7.149868,8.452499,5.983803,-5.691273,12.2475,5.589515,-6.922226,12.2475,5.589515,-7.149868,8.452499,3.063154,-5.463631,8.452499,3.063154,-6.922226,12.2475,3.457442,-5.691273,12.2475,3.457442,-9.825676,4.808896E-30,4.921279,-9.000675,8.452499,4.921279,-9.585956,1.68961E-15,5.336487,-7.538175,8.452499,7.454404,-8.464574,3.4425,6.696804,-7.782674,3.4425,7.877888,-7.992987,8.452499,4.523478,-7.537703,12.2475,4.523478,-7.149868,8.452499,5.983803,-6.922226,12.2475,5.589515,-7.149868,8.452499,3.063154,-6.922226,12.2475,3.457442,-7.992987,8.452499,4.523478,-7.537703,12.2475,4.523478,-5.573949,7.219114E-15,0.4893443,-3.747691,7.219114E-15,0,-4.432283,2.45,1.553725,-4.041172,2.45,1.448927,-3.747691,7.219114E-15,0,-1.274385,7.219114E-15,1.92547,-4.041172,2.45,1.448927,-2.437929,2.45,2.697053,-1.274385,7.219114E-15,1.92547,-0.9969097,7.219114E-15,2.961024,-2.437929,2.45,2.697053,-2.325867,2.45,3.115274,-7.028566,3.4425,7.877888,-6.349999,2.041224,7.996335,-7.40117,3.4425,8.887643,-5.92652,3.609557E-15,9.699979,-6.284924,0,10.67125,-5.573949,7.219114E-15,0.4893443,-4.432283,2.45,1.553725,-5.803885,5.03514E-15,1.673685,-4.49578,2.45,1.880777,-5.806408,4.450736,7.792664,-5.011737,4.450736,9.169075,-6.349999,2.041224,7.996335,-5.92652,3.609557E-15,9.699979,-5.622388,7.219114E-15,10.22675,-3.089082,6.272,5.183224,-2.450828,6.272,6.027172,-3.196893,4.450736,5.677851,-2.64204,4.450736,6.411519,-6.349999,2.041224,7.996335,-7.028566,3.4425,7.877888,-5.806408,4.450736,7.792664,-7.538175,8.452499,7.454404,-7.782674,3.4425,7.877888,-4.613175,8.452499,7.454404,-4.417881,4.450736,7.792664,-2.261414,4.450736,6.411519,-1.5783,2.726762,6.775333,-1.754938,4.450736,7.288761,-0.5336378,7.219114E-15,7.288761,-0.8886638,7.219114E-15,6.673837,-4.32024,2.45,1.880777,-3.750383,2.45,2.8678,-4.613175,8.452499,2.388155,-3.657755,6.272,3.674367,-3.150675,8.452499,4.921279,-2.937849,6.272,4.921279,-1.323597,6.272,4.545349,0,7.219114E-15,4.85077,-2.045919,6.272,6.027172,-0.8886638,7.219114E-15,6.673837,-1.5783,2.726762,6.775333,-10.28895,0,6.493614,-8.537134,3.4425,6.816237,-9.58152,0,10.34417,-8.170596,3.4425,8.8113,-2.229887,7.219114E-15,10.22675,-5.622388,7.219114E-15,10.22675,-2.840538,4.450736,9.169075,-5.011737,4.450736,9.169075,-6.284924,0,10.67125,-9.58152,0,10.34417,-7.40117,3.4425,8.887643,-8.170596,3.4425,8.8113,-9.58152,0,10.34417,-9.585956,1.68961E-15,5.336487,-10.28895,0,6.493614,-9.373505,7.219114E-15,0.1134806,-6.923506,7.219114E-15,0.1134806,-6.284924,0,10.67125,-6.230329,3.609557E-15,1.673685,-5.92652,3.609557E-15,9.699979,-5.803885,5.03514E-15,1.673685,-5.622388,7.219114E-15,10.22675,-5.573949,7.219114E-15,0.4893443,-3.747691,7.219114E-15,0,-2.229887,7.219114E-15,10.22675,-1.274385,7.219114E-15,1.92547,-0.9969097,7.219114E-15,2.961024,-0.8886638,7.219114E-15,6.673837,-0.5336378,7.219114E-15,7.288761,-9.74757,2.161376E-15,4.785997,-9.825676,4.808896E-30,4.921279,0,7.219114E-15,3.249021,0,7.219114E-15,4.85077,-10.59851,7.219114E-15,2.870711,-1.754938,4.450736,7.288761,-0.5336378,7.219114E-15,7.288761,-2.840538,4.450736,9.169075,-2.229887,7.219114E-15,10.22675,-9.546828,2.4,2.870711,-9.281147,5.1,2.870711,-9.051373,2.4,3.985882,-8.786627,5.1,3.983778,-9.373505,7.219114E-15,0.1134806,-6.923506,7.219114E-15,0.1134806,-9.128506,2.4,0.6649266,-7.168506,2.4,0.6649266,-8.847667,2.4,1.297039,-7.449345,2.4,1.297039,-8.714827,5.1,1.596036,-7.582185,5.1,1.596036,-8.847667,2.4,1.297039,-8.714827,5.1,1.596036,-9.546828,2.4,2.870711,-9.281147,5.1,2.870711,-9.373505,7.219114E-15,0.1134806,-9.128506,2.4,0.6649266,-10.59851,7.219114E-15,2.870711,-10.10851,2.4,2.870711,-1.5783,2.726762,6.775333,-2.261414,4.450736,6.411519,-2.045919,6.272,6.027172,-2.64204,4.450736,6.411519,-2.450828,6.272,6.027172,0,7.219114E-15,3.249021,0,7.219114E-15,4.85077,-1.323597,6.272,4.24437,-1.323597,6.272,4.545349,-6.923506,7.219114E-15,0.1134806,-6.230329,3.609557E-15,1.673685,-7.168506,2.4,0.6649266,-6.630197,2.4,1.876551,-7.191875,2.4,1.876551,-6.630197,2.4,1.876551,-7.356159,5.1,2.104776,-4.49578,2.45,1.880777,-4.32024,2.45,1.880777,-4.613175,8.452499,2.388155,-7.701784,5.1,2.104776,-7.538175,8.452499,2.388155,-5.803885,5.03514E-15,1.673685,-6.230329,3.609557E-15,1.673685,-7.701784,5.1,2.104776,-7.538175,8.452499,2.388155,-8.786627,5.1,3.983778,-9.000675,8.452499,4.921279,-9.051373,2.4,3.985882,-9.36879,2.4,4.535664,-9.74757,2.161376E-15,4.785997,-9.825676,4.808896E-30,4.921279,-3.750383,2.45,2.8678,-3.182505,2.45,2.8678,-3.657755,6.272,3.674367,-3.296675,6.272,3.674367,-9.585956,1.68961E-15,5.336487,-8.464574,3.4425,6.696804,-10.28895,0,6.493614,-8.537134,3.4425,6.816237,-10.59851,7.219114E-15,2.870711,-10.10851,2.4,2.870711,-9.74757,2.161376E-15,4.785997,-9.36879,2.4,4.535664,-7.449345,2.4,1.297039,-7.191875,2.4,1.876551,-7.582185,5.1,1.596036,-7.356159,5.1,2.104776,-3.182505,2.45,2.8678,-2.325867,2.45,3.115274,-3.296675,6.272,3.674367,-1.323597,6.272,4.24437,0,7.219114E-15,3.249021,-0.9969097,7.219114E-15,2.961024,-3.150675,8.452499,4.921279,-4.613175,8.452499,7.454404,-4.613175,8.452499,2.388155,-4.620512,8.452499,4.523478,-5.463631,8.452499,3.063154,-7.538175,8.452499,2.388155,-7.149868,8.452499,3.063154,-7.992987,8.452499,4.523478,-9.000675,8.452499,4.921279,-5.463631,8.452499,5.983803,-7.538175,8.452499,7.454404,-7.149868,8.452499,5.983803,-5.075796,12.2475,4.523478,-5.691273,12.2475,5.589515,-5.691273,12.2475,3.457442,-6.922226,12.2475,3.457442,-6.922226,12.2475,5.589515,-7.537703,12.2475,4.523478,-2.325867,2.45,3.115274,-3.182505,2.45,2.8678,-2.437929,2.45,2.697053,-4.041172,2.45,1.448927,-3.750383,2.45,2.8678,-4.32024,2.45,1.880777,-4.432283,2.45,1.553725,-4.49578,2.45,1.880777,-1.754938,4.450736,7.288761,-2.840538,4.450736,9.169075,-2.261414,4.450736,6.411519,-2.64204,4.450736,6.411519,-3.196893,4.450736,5.677851,-5.011737,4.450736,9.169075,-4.417881,4.450736,7.792664,-5.806408,4.450736,7.792664,-6.630197,2.4,1.876551,-7.191875,2.4,1.876551,-7.168506,2.4,0.6649266,-7.449345,2.4,1.297039,-9.128506,2.4,0.6649266,-8.847667,2.4,1.297039,-9.546828,2.4,2.870711,-10.10851,2.4,2.870711,-9.36879,2.4,4.535664,-9.051373,2.4,3.985882,-7.356159,5.1,2.104776,-7.701784,5.1,2.104776,-7.582185,5.1,1.596036,-8.714827,5.1,1.596036,-8.786627,5.1,3.983778,-9.281147,5.1,2.870711,-7.028566,3.4425,7.877888,-7.40117,3.4425,8.887643,-7.782674,3.4425,7.877888,-8.170596,3.4425,8.8113,-8.464574,3.4425,6.696804,-8.537134,3.4425,6.816237,-1.323597,6.272,4.24437,-1.323597,6.272,4.545349,-3.296675,6.272,3.674367,-2.045919,6.272,6.027172,-2.450828,6.272,6.027172,-2.937849,6.272,4.921279,-3.657755,6.272,3.674367,-3.089082,6.272,5.183224
		} 
		PolygonVertexIndex: *516 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,4,-3,6,8,-8,9,7,-9,10,12,-12,13,11,-13,14,16,-16,17,15,-17,18,20,-20,21,19,-21,22,24,-24,25,23,-25,26,25,-25,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,51,-51,53,55,-55,56,54,-56,57,59,-59,60,58,-60,61,58,-61,62,64,-64,65,63,-65,66,68,-68,68,69,-68,70,67,-70,71,69,-69,72,71,-69,73,75,-75,75,76,-75,77,74,-77,78,80,-80,81,79,-81,82,81,-81,83,81,-83,84,86,-86,87,85,-87,88,87,-87,89,91,-91,92,90,-92,93,95,-95,96,94,-96,97,99,-99,100,98,-100,101,103,-103,102,104,-102,104,105,-102,101,105,-107,105,107,-107,106,107,-109,107,109,-109,108,109,-111,109,111,-111,111,112,-111,110,112,-114,112,114,-114,114,115,-114,115,116,-114,117,113,-117,118,104,-103,102,119,-119,115,120,-117,121,116,-121,122,104,-119,123,125,-125,126,124,-126,127,129,-129,130,128,-130,131,133,-133,134,132,-134,135,137,-137,138,136,-138,139,141,-141,142,140,-142,143,145,-145,146,144,-146,147,149,-149,150,148,-150,151,150,-150,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,163,-163,165,164,-163,166,165,-163,167,165,-167,163,168,-162,169,161,-169,170,172,-172,173,171,-173,174,173,-173,175,173,-175,176,173,-176,177,173,-177,178,180,-180,181,179,-181,182,184,-184,185,183,-185,186,188,-188,189,187,-189,190,192,-192,193,191,-193,194,196,-196,196,197,-196,197,198,-196,199,195,-199,200,202,-202,203,201,-203,204,203,-203,205,204,-203,206,204,-206,207,206,-206,208,207,-206,203,209,-202,210,201,-210,208,210,-208,211,210,-210,207,210,-212,212,214,-214,215,213,-215,216,213,-216,217,216,-216,218,220,-220,221,219,-221,222,219,-222,223,222,-222,224,223,-222,225,223,-225,226,228,-228,229,227,-229,230,227,-230,231,227,-231,232,231,-231,233,231,-233,234,236,-236,237,235,-237,238,237,-237,239,237,-239,240,239,-239,241,240,-239,241,242,-241,243,240,-243,244,246,-246,247,245,-247,248,245,-248,249,248,-248,250,252,-252,253,251,-253,254,253,-253,255,253,-255,256,258,-258,259,257,-259,260,259,-259,261,260,-259,262,261,-259,261,263,-261
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1548 {
				a: 0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.862948,0.0842274,0.4982232,0.8613888,0.1033404,0.497323,0.8613888,0.1033404,0.497323,0.8613888,0.1033404,0.497323,0.8613888,0.1033404,0.497323,0.8613888,0.1033404,0.497323,0.8613888,0.1033404,0.497323,0.8613888,0.1033404,-0.497323,0.8613888,0.1033404,-0.497323,0.8613888,0.1033404,-0.497323,0.8613888,0.1033404,-0.497323,0.8613888,0.1033404,-0.497323,0.8613888,0.1033404,-0.497323,0,0.1033404,0.9946461,0,0.1033404,0.9946461,0,0.1033404,0.9946461,0,0.1033404,0.9946461,0,0.1033404,0.9946461,0,0.1033404,0.9946461,0,0.1033404,-0.9946461,0,0.1033404,-0.9946461,0,0.1033404,-0.9946461,0,0.1033404,-0.9946461,0,0.1033404,-0.9946461,0,0.1033404,-0.9946461,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.862948,0.0842274,0.4982232,-0.8613888,0.1033404,0.497323,-0.8613888,0.1033404,0.497323,-0.8613888,0.1033404,0.497323,-0.8613888,0.1033404,0.497323,-0.8613888,0.1033404,0.497323,-0.8613888,0.1033404,0.497323,-0.8613888,0.1033404,-0.497323,-0.8613888,0.1033404,-0.497323,-0.8613888,0.1033404,-0.497323,-0.8613888,0.1033404,-0.497323,-0.8613888,0.1033404,-0.497323,-0.8613888,0.1033404,-0.497323,-0.227713,0.475315,-0.8498367,-0.227713,0.475315,-0.8498367,-0.227713,0.475315,-0.8498367,-0.227713,0.475315,-0.8498367,-0.227713,0.475315,-0.8498367,-0.227713,0.475315,-0.8498367,0.5404674,0.4753151,-0.6942412,0.5404674,0.4753151,-0.6942412,0.5404674,0.4753151,-0.6942412,0.5404674,0.4753151,-0.6942412,0.5404674,0.4753151,-0.6942412,0.5404674,0.4753151,-0.6942412,0.8498367,0.475315,-0.227713,0.8498367,0.475315,-0.227713,0.8498367,0.475315,-0.227713,0.8498367,0.475315,-0.227713,0.8498367,0.475315,-0.227713,0.8498367,0.475315,-0.227713,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,0.8445987,0.4353404,0.3116599,-0.8636886,0.4753151,-0.1676831,-0.8636886,0.4753151,-0.1676831,-0.8636886,0.4753151,-0.1676831,-0.8636886,0.4753151,-0.1676831,-0.8636886,0.4753151,-0.1676831,-0.8636886,0.4753151,-0.1676831,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.8425611,0.2312022,0.4864528,-0.7804039,0.2064849,0.590198,-0.7804039,0.2064849,0.590198,-0.7804039,0.2064849,0.590198,-0.7804039,0.2064849,0.590198,-0.7804039,0.2064849,0.590198,-0.7804039,0.2064849,0.590198,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0,0.08422741,0.9964465,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.8425611,0.2312022,-0.4864528,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.862948,0.0842274,-0.4982232,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,0.879521,0.2064849,0.428727,-0.8854463,0.4353404,0.1626764,-0.8854463,0.4353404,0.1626764,-0.8854463,0.4353404,0.1626764,-0.8854463,0.4353404,0.1626764,-0.8854463,0.4353404,0.1626764,-0.8854463,0.4353404,0.1626764,0,0.2312022,0.9729057,0,0.2312022,0.9729057,0,0.2312022,0.9729057,0,0.2312022,0.9729057,0,0.2312022,0.9729057,0,0.2312022,0.9729057,-0.08888759,0.4353404,0.895867,-0.08888759,0.4353404,0.895867,-0.08888759,0.4353404,0.895867,-0.08888759,0.4353404,0.895867,-0.08888759,0.4353404,0.895867,-0.08888759,0.4353404,0.895867,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.8425611,0.2312022,0.4864528,0.8425611,0.2312022,0.4864528,0.8425611,0.2312022,0.4864528,0.8425611,0.2312022,0.4864528,0.8425611,0.2312022,0.4864528,0.8425611,0.2312022,0.4864528,-0.9101924,0.08956335,0.4043862,-0.9101924,0.08956335,0.4043862,-0.9101924,0.08956335,0.4043862,-0.9101924,0.08956335,0.4043862,-0.9101924,0.08956335,0.4043862,-0.9101924,0.08956335,0.4043862,0,0.223934,-0.9746042,0,0.223934,-0.9746042,0,0.223934,-0.9746042,0,0.223934,-0.9746042,0,0.223934,-0.9746042,0,0.223934,-0.9746042,0,0.1100671,-0.9939242,0,0.1100671,-0.9939242,0,0.1100671,-0.9939242,0,0.1100671,-0.9939242,0,0.1100671,-0.9939242,0,0.1100671,-0.9939242,-0.9101924,0.08956335,-0.4043862,-0.9101924,0.08956335,-0.4043862,-0.9101924,0.08956335,-0.4043862,-0.9101924,0.08956335,-0.4043862,-0.9101924,0.08956335,-0.4043862,-0.9101924,0.08956335,-0.4043862,-0.8983618,0.1834155,-0.39913,-0.8983618,0.1834155,-0.39913,-0.8983618,0.1834155,-0.39913,-0.8983618,0.1834155,-0.39913,-0.8983618,0.1834155,-0.39913,-0.8983618,0.1834155,-0.39913,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0,0.2064849,0.9784498,0.9784498,0.2064849,0,0.9784498,0.2064849,0,0.9784498,0.2064849,0,0.9784498,0.2064849,0,0.9784498,0.2064849,0,0.9784498,0.2064849,0,0.8983618,0.1834155,-0.39913,0.8983618,0.1834155,-0.39913,0.8983618,0.1834155,-0.39913,0.8983618,0.1834155,-0.39913,0.8983618,0.1834155,-0.39913,0.8983618,0.1834155,-0.39913,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,0,0.08422741,-0.9964465,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,-0.862948,0.0842274,-0.4982232,0,0.2064849,-0.9784498,0,0.2064849,-0.9784498,0.1370749,0.2084543,-0.9683787,0.1370749,0.2084543,-0.9683787,0.1370749,0.2084543,-0.9683787,0,0.2064849,-0.9784498,-0.7694012,0.4353404,-0.4674404,-0.7694012,0.4353404,-0.4674404,-0.7694012,0.4353404,-0.4674404,-0.7694012,0.4353404,-0.4674404,-0.7694012,0.4353404,-0.4674404,-0.7694012,0.4353404,-0.4674404,-0.8983618,0.1834155,0.39913,-0.8983618,0.1834155,0.39913,-0.8983618,0.1834155,0.39913,-0.8983618,0.1834155,0.39913,-0.8983618,0.1834155,0.39913,-0.8983618,0.1834155,0.39913,0.9101924,0.08956335,-0.4043862,0.9101924,0.08956335,-0.4043862,0.9101924,0.08956335,-0.4043862,0.9101924,0.08956335,-0.4043862,0.9101924,0.08956335,-0.4043862,0.9101924,0.08956335,-0.4043862,0.1370749,0.2084543,-0.9683787,0.1370749,0.2084543,-0.9683787,0.2715598,0.2064849,-0.9400102,0.1370749,0.2084543,-0.9683787,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0.2715598,0.2064849,-0.9400102,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *528 {
				a: -45.96292,66.49631,-45.12502,49.26579,-68.99442,66.49631,-47.50665,49.26579,-51.30402,34.87399,-70.53217,34.87399,-50.82949,97.65716,-49.03703,67.61442,-60.52204,97.65716,-62.31449,67.61442,0.6224123,71.29519,-12.65505,71.29519,-1.170045,101.3379,-10.86259,101.3379,-43.02071,61.32975,-56.29818,61.32975,-44.81317,91.37248,-54.50572,91.37248,56.29818,68.69128,43.02071,68.69128,54.50572,98.73402,44.81317,98.73402,-5.125073,-7.275341,-1.877042,59.51712,-1.349954,-7.275341,21.15445,59.51712,12.34107,19.92762,23.07964,19.92762,-0.6224123,58.72584,1.170045,88.76858,12.65505,58.72584,10.86259,88.76858,49.03703,62.40661,50.82949,92.44935,62.31449,62.40661,60.52204,92.44935,43.39112,-3.630261,28.50387,-3.630261,36.87709,18.29631,33.68884,18.29631,23.28513,8.61627,-1.395451,8.61627,18.10016,30.54284,2.101747,30.54284,-12.04745,6.472182,-20.48906,6.472182,-15.54465,28.39875,-18.9539,28.39875,-77.35401,37.65759,-76.3793,25.40158,-85.82887,37.65759,-87.80999,7.548378,-95.96191,7.548378,12.1473,-20.12983,18.66133,1.796737,21.64695,-20.12983,21.28464,1.796737,30.27906,17.84808,42.79357,17.84808,29.52779,-1.652815,42.81235,-18.17305,47.60183,-18.17305,17.88011,39.23242,26.21176,39.23242,20.47444,24.5759,27.71737,24.5759,-50,10.71229,-55.34304,21.7853,-45.71975,29.75247,-59.35571,61.3748,-61.2809,21.7853,-36.32421,61.3748,-34.78646,29.75247,-34.81757,43.49701,-39.98788,29.54438,-42.79357,43.49701,-47.60183,7.475885,-42.01087,7.475885,4.183615,22.32781,-4.790519,22.32781,1.877042,69.76014,-10.65526,52.52962,-21.15445,69.76014,-21.99235,52.52962,-36.73811,47.01783,-34.33322,-3.455713,-49.71841,47.01783,-50.30273,-3.455713,-53.40046,18.48774,35.6498,-38.71091,40.64085,-8.601693,66.47658,-38.71091,56.61293,-8.601693,-17.55817,-18.6177,-44.27077,-18.6177,-22.36644,17.40343,-39.4625,17.40343,-40.94953,-38.52815,-67.03444,-38.52815,-51.08257,-8.418941,-57.17079,-8.418941,-75.44505,81.45014,-75.47997,42.01958,-81.01539,51.13082,-73.80713,0.8935481,-54.51579,0.8935481,-49.48759,84.02563,-49.0577,13.17862,-46.66551,76.37778,-45.69989,13.17862,-44.27077,80.5256,-43.88936,3.853105,-29.50938,-2.410138E-29,-17.55817,80.5256,-10.03453,15.16118,-7.849683,23.31515,-6.997353,52.5499,-4.201872,57.39182,-76.75252,37.68502,-77.36752,38.75023,2.871853E-29,25.58284,2.871853E-29,38.19503,-83.4528,22.60402,-56.61197,30.2279,-51.80371,-5.793229,-73.70804,30.2279,-78.5163,-5.793229,-9.864101,11.84698,-9.014721,33.19261,-0.2555995,11.84698,0.5756508,33.19261,73.80713,0.2000958,54.51579,0.2000958,71.878,19.59016,56.44493,19.59016,69.66667,19.90692,58.65626,19.90692,68.62068,41.29673,59.70225,41.29673,37.61913,13.49094,39.34597,34.83657,51.17815,13.49094,50.32877,34.83657,30.7836,-12.30479,33.96843,6.918967,54.54036,-12.30479,52.97383,6.918967,-12.42756,9.992091,-17.80641,23.86566,-16.1096,38.52218,-20.80347,23.86566,-19.29786,38.52218,-25.58284,5.530487E-14,-38.19503,5.515028E-14,-33.42023,50.47354,-35.79015,50.47354,21.31781,9.20432,7.874827,9.20432,18.13298,28.42808,7.693419,28.42808,56.62894,20.07503,52.20628,20.07503,57.92251,41.41069,35.39984,20.47013,34.01764,20.47013,36.32421,67.90246,60.64397,41.41069,59.35571,67.90246,45.69989,1.110001,49.0577,1.110001,44.67466,36.28918,45.96292,62.78095,61.75879,36.28918,68.99442,62.78095,62.81544,14.95352,67.81413,14.95352,71.01244,-4.011509,72.24245,-4.011509,29.53057,23.53826,25.05909,23.53826,28.80122,54.29558,25.95807,54.29558,75.10259,-18.58486,79.6721,11.52435,85.76353,-18.58486,80.77248,11.52435,-13.22631,-15.67144,-11.65978,3.552325,3.276126,-15.67144,2.685737,3.552325,14.48228,23.99402,9.489098,23.99402,12.75544,45.33965,8.372054,45.33965,17.80744,24.79117,10.78644,24.79117,16.90846,55.54849,0.7371067,55.54849,-7.100284,5.074944,1.070394,5.074944,24.80847,38.75023,36.32421,58.69609,36.32421,18.80437,36.38198,35.61794,43.02071,24.11932,59.35571,18.80437,56.29818,24.11932,62.9369,35.61794,70.87146,38.75023,43.02071,47.11656,59.35571,58.69609,56.29818,47.11656,39.9669,35.61794,44.81317,44.01193,44.81317,27.22395,54.50572,27.22395,54.50572,44.01193,59.35199,35.61794,18.31392,24.52972,25.05909,22.5811,19.19629,21.23664,31.82025,11.40887,29.53057,22.5811,34.01764,14.80927,34.89987,12.23405,35.39984,14.80927,13.81841,57.39182,22.36644,72.19744,17.80641,50.4844,20.80347,50.4844,25.17238,44.70749,39.4625,72.19744,34.78646,61.35956,45.71975,61.35956,52.20628,14.77599,56.62894,14.77599,56.44493,5.235643,58.65626,10.2129,71.878,5.235643,69.66667,10.2129,75.17188,22.60402,79.59454,22.60402,73.77,35.71389,71.27065,31.3849,57.92251,16.57304,60.64397,16.57304,59.70225,12.56721,68.62068,12.56721,69.18604,31.36833,73.0799,22.60402,55.34304,62.03062,58.27692,69.98144,61.2809,62.03062,64.3354,69.38032,66.65018,52.73074,67.22153,53.67116,10.42203,33.42023,10.42203,35.79015,25.95807,28.93202,16.1096,47.45805,19.29786,47.45805,23.13267,38.75023,28.80122,28.93202,24.32348,40.81279
				}
			UVIndex: *516 {
				a: 0,2,1,3,1,2,4,3,2,5,4,2,6,8,7,9,7,8,10,12,11,13,11,12,14,16,15,17,15,16,18,20,19,21,19,20,22,24,23,25,23,24,26,25,24,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,51,50,53,55,54,56,54,55,57,59,58,60,58,59,61,58,60,62,64,63,65,63,64,66,68,67,68,69,67,70,67,69,71,69,68,72,71,68,73,75,74,75,76,74,77,74,76,78,80,79,81,79,80,82,81,80,83,81,82,84,86,85,87,85,86,88,87,86,89,91,90,92,90,91,93,95,94,96,94,95,97,99,98,100,98,99,101,103,102,102,104,101,104,105,101,101,105,106,105,107,106,106,107,108,107,109,108,108,109,110,109,111,110,111,112,110,110,112,113,112,114,113,114,115,113,115,116,113,117,113,116,118,104,102,102,119,118,115,120,116,121,116,120,122,104,118,123,125,124,126,124,125,127,129,128,130,128,129,131,133,132,134,132,133,135,137,136,138,136,137,139,141,140,142,140,141,143,145,144,146,144,145,147,149,148,150,148,149,151,150,149,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,163,162,165,164,162,166,165,162,167,165,166,163,168,161,169,161,168,170,172,171,173,171,172,174,173,172,175,173,174,176,173,175,177,173,176,178,180,179,181,179,180,182,184,183,185,183,184,186,188,187,189,187,188,190,192,191,193,191,192,194,196,195,196,197,195,197,198,195,199,195,198,200,202,201,203,201,202,204,203,202,205,204,202,206,204,205,207,206,205,208,207,205,203,209,201,210,201,209,208,210,207,211,210,209,207,210,211,212,214,213,215,213,214,216,213,215,217,216,215,218,220,219,221,219,220,222,219,221,223,222,221,224,223,221,225,223,224,226,228,227,229,227,228,230,227,229,231,227,230,232,231,230,233,231,232,234,236,235,237,235,236,238,237,236,239,237,238,240,239,238,241,240,238,241,242,240,243,240,242,244,246,245,247,245,246,248,245,247,249,248,247,250,252,251,253,251,252,254,253,252,255,253,254,256,258,257,259,257,258,260,259,258,261,260,258,262,261,258,261,263,260
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *172 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 11698, "Material::sand", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8470588,0.7607843,0.6078432
			P: "DiffuseColor", "Color", "", "A",0.8470588,0.7607843,0.6078432
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh formation_rock, Model::RootNode
	C: "OO",4954269913661939513,0

	;Geometry::, Model::Mesh formation_rock
	C: "OO",4893865541086961890,4954269913661939513

	;Material::wood, Model::Mesh formation_rock
	C: "OO",19362,4954269913661939513

	;Material::sand, Model::Mesh formation_rock
	C: "OO",11698,4954269913661939513

}
