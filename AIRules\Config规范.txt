# Config生成规范

config所在路径：Assets\_MyGame\Scripts\Config

1.创建Info：
Info功能名:比如InfoItem,InfoGate
info对应的数据在(../Excels/json/功能名.json)，如果找不到提醒用户先生成表格json文件


using System;
using LitJson;
[Serializable]
public class Info功能名 : ConfigInfoBase
{
    //根据读取的json数据编写属性
    //public int id;

    public override object GetKey(int index)
    {
        //json数据
        //return this.id;
    }

    public override void Parse(JsonData json)
    {
        //根据读取的json数据赋值
        //通过Assets\WGame\Runtime\Utils\JsonUtil.cs读取json数据
        //id = JsonUtil.ToInt(json, "id");
        //name = JsonUtil.ToString(json, "name");
        //desc = JsonUtil.ToString(json, "desc");
    }
}

2.创建Config：
Config功能名：比如ConfigItem,ConfigGate

public class Config功能名 : ConfigBase
{
    public static 对应的Info GetData(int id)
    {
        var info = ConfigManager.GetConfig<Config功能名>().GetData<对应的Info>(id);
        return info;
    }
}

3.TableSO需要新增Config
路径：Assets\_MyGame\Scripts\Config\TableSO.cs
需要修改字段，GetDatas，tableNames，GenerateSO

4.ConfigLoader需要新增Config
路径：Assets\_MyGame\Scripts\Config\ConfigLoader.cs
需要修改tableNames内容