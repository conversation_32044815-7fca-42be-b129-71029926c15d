文件和类命名:

创建新的 C# 文件，文件名应为 Panel名称Panel.cs (例如, AddDesktopPanel.cs, BuyItemPanel.cs)。

类名应为 Panel名称Panel，并继承自一个基类 Panel (例如, public class AddDesktopPanel : Panel)。

命名空间和位置:

Panel 文件通常放置在 Assets\_MyGame\Scripts\UI 文件夹下，并且通常根据 UI 区域进一步组织到子文件夹中 (例如, UI\Battle, UI\Lobby, UI\Tips)。

构造函数:

每个 Panel 类必须有一个构造函数：public Panel名称Panel()。

在构造函数内部，设置以下属性：

packName: FairyGUI 包的名称，包含此 Panel 的 UI 组件 (例如, "AddDesktop", "Battle", "Common", "CityGuide", "DailyReward", "GameRule", "GetReward", "HardTips", "InviteFriends", "Loading", "Lobby", "Rank", "Tips", "TipsMsg", "ChangeTheme", "ChallengeRank")。

compName: FairyGUI 包中组件的名称。这通常与 Panel名称Panel 类名匹配 (例如, "AddDesktopPanel", "BuyItemPanel")。

modal: 设置为 true 如果 Panel 是模态的，这意味着它会阻止与后面 UI 元素的交互。设置为 false 如果是非模态的。通常，需要集中交互的 Panel (例如对话框、设置) 是模态的。

extraPacks: (可选) 如果 Panel 使用来自其他 FairyGUI 包的 UI 组件，则将其指定为字符串数组 (例如, extraPacks = new string[] { "ChangeTheme" };)。

inQueue: (可选) 布尔值，控制 Panel 是否应在队列中管理以进行显示 (可能用于 Panel 堆叠或顺序管理)。 true 表明它属于显示队列的一部分。

isClickClose: (可选) 布尔值，指示点击 Panel 外部是否应关闭它。 false 通常用于模态对话框，在这些对话框中，首选显式按钮点击。

public class AddDesktopPanel : Panel
{
    public AddDesktopPanel()
    {
        packName = "AddDesktop";
        compName = "AddDesktopPanel";
        modal = true;
    }
}

初始化和 UI 元素访问:

DoInitialize() 方法:

重写 protected override void DoInitialize() 方法。这是获取 UI 元素引用并在 Panel 的 UI 组件创建并作为 contentPane 可用后执行初始设置的地方。

使用 contentPane.GetChild("元素名称").as[组件类型] 来访问在 FairyGUI 组件中定义的 UI 元素。将 [组件类型] 替换为适当的类型 (例如, TextField, Loader, Button, GList, GProgressBar 等)。

protected override void DoInitialize()
{
    txtTitle = contentPane.GetChild("txtTitle").asTextField;
    icon = contentPane.GetChild("icon").asLoader;
    txtDesc = contentPane.GetChild("txtDesc").asTextField;
    btnFree = contentPane.GetChild("btnFree").asButton;
    txtCount = contentPane.GetChild("txtCount").asTextField;
    // ... 其他 UI 元素的获取
}

事件处理:

OnMouseClick() 方法:

重写 protected override void OnMouseClick(string targetName) 方法来处理 UI 元素的鼠标点击事件。

targetName 参数是点击的 UI 元素的名称 (在 FairyGUI 编辑器中设置)。

使用 switch (targetName) 语句来根据点击的元素名称执行不同的操作 (例如，关闭 Panel, 执行购买操作, 切换 Handler 等)。

protected override void OnMouseClick(string targetName)
{
    switch (targetName)
    {
        case "btnClose":
            Hide();
            break;
        case "btnFree":
            // ... 处理 btnFree 的点击事件
            break;
        // ... 其他按钮的 case
    }
}

其他事件处理 (可选):

根据 Panel 的需要，可以添加其他事件处理方法，例如 OnChanged (用于 Controller 或 Slider 的值改变事件), OnShow, OnHide, OnRemovedFromStage 等。 这些方法通常也是 protected override 的。

数据设置和 Panel 功能:

SetData() 方法 (常用):

许多 Panel 都有一个 public void SetData(...) 方法，用于从外部设置 Panel 显示的数据。

SetData() 方法的参数类型和数量取决于 Panel 的具体功能和需要显示的数据。

通常在 SetData() 方法中，会根据传入的数据更新 Panel 中 UI 元素的显示内容 (例如，文本、图标、列表数据等)。

public void SetData(int itemId, int count = 1)
{
    this.itemId = itemId;
    this.itemCount = count;
    var infoItem = ConfigItem.GetData(itemId);
    icon.url = infoItem.IconUrl;
    txtTitle.text = infoItem.name;
    // ... 其他 UI 元素的更新
}

Panel 的显示和隐藏:

显示 Panel:

静态方法 Panel.Create<T>(Action<T> onCreate = null) 用于创建并显示 Panel。 T 是 Panel 的具体类型 (例如, AddDesktopPanel, BuyItemPanel)。

onCreate 参数是一个可选的 Action 委托，在 Panel 创建完成后会被立即调用。 你可以在 onCreate 委托中对新创建的 Panel 实例进行进一步的设置或数据传递 (例如, 调用 SetData() 方法)。

Panel.Create<T>() 方法会自动处理 Panel 的创建、添加到显示列表、以及可能的入场动画。

示例:

Panel.Create((BuyItemPanel panel) => // 使用 lambda 表达式作为 onCreate 委托
{
    panel.SetData(itemId, itemCount); // 调用 SetData 方法传递数据
    panel.OnBuySuccess = (buyType) => // 注册 OnBuySuccess 事件
    {
        Debug.Log("购买成功，购买类型: " + buyType);
        // ... 其他购买成功后的逻辑
    };
});

Panel.Create<SettingPanel>((panel) => { // 泛型指定 Panel 类型，并使用 lambda 表达式
    panel.SetData(SettingPanel.SettingViewMode.Setting); // 设置 SettingPanel 的数据
});

Panel.Create<ChangeThemePanel>(); // 创建并显示 ChangeThemePanel，无需额外设置


使用 Hide() 方法隐藏 Panel。Hide() 方法可以接受一个可选的 closeType 参数，用于区分不同的关闭类型 (例如, SettingPanel.CloseType_Restart, ResultFailPanel.CloseType_Restart)。

通常在按钮的点击事件处理中调用 Hide() 方法来关闭 Panel。

其他规范和最佳实践:

资源加载: 使用 AssetBundleManager.LoadPrefab 或 UIPackage.CreateObject 等方法加载资源 (例如 Prefab, FairyGUI 组件)。

定时器: 使用 Timers.inst 来创建定时器，执行延迟操作或循环操作。 注意在 Panel 隐藏或销毁时移除定时器以避免内存泄漏。

消息通知: 使用 NotifyMgr 进行消息通知，实现 Panel 之间的通信和数据更新。

单例模式 (LoadingPanel): 对于全局唯一的 Panel (例如 LoadingPanel)，可以使用单例模式来管理其创建和访问。

代码注释: 代码中应添加必要的注释，解释代码的功能和逻辑，提高代码可读性。