; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 38
		Millisecond: 464
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/sword_scimitar.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/sword_scimitar.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4852091248792892883, "Model::sword_scimitar", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-22.6816,1,-85.76152
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,-90,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4814144075667229461, "Geometry::", "Mesh" {
		Vertices: *2226 {
			a: 0.7907779,5.256827,0.1608125,0.7607008,5.136855,0.1743943,0.6938731,5.489392,0.1104326,0.8919743,5.482022,0.1344049,0.8347403,5.37242,-0.1246318,0.8347403,5.37242,0.1474204,0.8919743,5.482022,-0.1116163,0.8812748,4.420189,0.2493943,-0.1175308,4.420855,0.1476421,0.1134849,4.806329,0.1347038,-0.3051876,4.012219,-0.1382252,-0.11753,4.420857,-0.1249501,-0.3051885,4.012216,0.1609077,-0.1175308,4.420855,0.1476421,0.6607943,3.850685,0.2493943,-0.3051885,4.012216,0.1609077,-0.1175308,4.420855,0.1476421,0.7658386,4.13747,0.2493943,0.6607943,3.850685,0.2493943,-0.1175308,4.420855,0.1476421,0.482438,3.26648,-0.2266057,0.5662845,3.560223,-0.2266057,-0.4472076,3.585379,-0.1516603,0.2557456,2.066745,0.2493943,0.2266314,1.76266,0.2493943,-0.5850816,2.248226,0.2140036,0.7449291,5.014179,-0.1651875,0.7449291,5.014179,0.1879761,0.7607008,5.136855,-0.1516057,0.7607008,5.136855,0.1743943,0.743683,4.890513,0.2013682,0.7569798,4.767585,-0.1915951,0.7569798,4.767585,0.2143837,0.743683,4.890513,-0.1785796,-0.5418648,3.145523,-0.1650923,0.4093689,2.969856,-0.2266057,-0.4472076,3.585379,-0.1516603,0.7569798,4.767585,0.2143837,0.7846341,4.647109,-0.2040522,0.7846341,4.647109,0.2268408,0.7569798,4.767585,-0.1915951,0.7569798,4.767585,0.2143837,0.1134849,4.806329,0.1347038,0.3850525,5.163956,0.1222503,0.743683,4.890513,0.2013682,0.7569798,4.767585,0.2143837,0.3850525,5.163956,0.1222503,1.134457,5.760116,-0.07660569,1.134457,5.760116,0.09939431,1.036195,5.778682,-0.07660569,1.036195,5.778682,0.09939431,0.2557456,2.066745,0.2493943,-0.5850816,2.248226,0.2140036,0.2959456,2.36958,0.2493943,0.01703053,1,0.2461176,-0.3061083,1,0.2478244,-0.4327427,1.363801,0.2383235,0.7449291,5.014179,-0.1651875,0.743683,4.890513,-0.1785796,0.7449291,5.014179,0.1879761,0.743683,4.890513,0.2013682,-0.3051876,4.012219,-0.1382252,0.6607943,3.850685,-0.2266057,-0.11753,4.420857,-0.1249501,0.7607008,5.136855,0.1743943,0.7449291,5.014179,0.1879761,0.3850525,5.163956,0.1222503,-0.4472076,3.585379,-0.1516603,-0.3051876,4.012219,-0.1382252,-0.4472085,3.585376,0.1743397,-0.3051885,4.012216,0.1609077,0.7846341,4.647109,-0.2040522,0.8262595,4.530769,-0.215777,0.7846341,4.647109,0.2268408,0.8262595,4.530769,0.2385656,0.2266314,1.76266,0.2493943,-0.5331153,1.801688,0.2264787,-0.5850816,2.248226,0.2140036,0.8262595,4.530769,0.2385656,0.8812748,4.420189,0.2493943,0.1134849,4.806329,0.1347038,1.042886,5.677233,-0.08743443,0.9616807,5.584102,-0.09915921,1.042886,5.677233,0.110223,0.9616807,5.584102,0.1219478,-0.5331148,1.80169,-0.2037497,0.2266314,1.76266,-0.2266057,-0.5850809,2.248228,-0.1912962,-0.4472076,3.585379,-0.1516603,0.5662845,3.560223,-0.2266057,-0.3051876,4.012219,-0.1382252,0.7607008,5.136855,0.1743943,0.3850525,5.163956,0.1222503,0.6938731,5.489392,0.1104326,0.5662845,3.560223,0.2493943,0.482438,3.26648,0.2493943,-0.4472085,3.585376,0.1743397,0.743683,4.890513,-0.1785796,0.7449291,5.014179,-0.1651875,0.385053,5.163958,-0.09952132,0.7607008,5.136855,-0.1516057,0.7607008,5.136855,0.1743943,0.7907779,5.256827,-0.1380239,0.7907779,5.256827,0.1608125,1.134457,5.760116,0.09939431,1.134457,5.760116,-0.07660569,1.042886,5.677233,-0.08743443,1.042886,5.677233,0.110223,0.8919743,5.482022,0.1344049,0.8347403,5.37242,0.1474204,0.6938731,5.489392,0.1104326,0.7449291,5.014179,0.1879761,0.743683,4.890513,0.2013682,0.3850525,5.163956,0.1222503,0.7569798,4.767585,0.2143837,0.7846341,4.647109,0.2268408,0.1134849,4.806329,0.1347038,0.2086425,1.45774,-0.2266057,0.2018034,1.152398,-0.2266057,0.2086425,1.45774,0.2493943,0.2018034,1.152398,0.2493943,0.8919743,5.482022,0.1344049,1.036195,5.778682,0.09939431,0.9616807,5.584102,0.1219478,1.042886,5.677233,0.110223,1.134457,5.760116,0.09939431,0.6607943,3.850685,-0.2266057,0.7658386,4.13747,-0.2266057,-0.11753,4.420857,-0.1249501,0.7846341,4.647109,-0.2040522,0.7569798,4.767585,-0.1915951,0.1134856,4.806332,-0.1119964,-0.5850809,2.248228,-0.1912962,0.2557456,2.066745,-0.2266057,0.2959456,2.36958,-0.2266057,0.4093689,2.969856,-0.2266057,0.482438,3.26648,-0.2266057,-0.4472076,3.585379,-0.1516603,0.5662845,3.560223,-0.2266057,0.482438,3.26648,-0.2266057,0.5662845,3.560223,0.2493943,0.482438,3.26648,0.2493943,0.4093689,2.969856,-0.2266057,0.3471766,2.670753,-0.2266057,0.4093689,2.969856,0.2493943,0.3471766,2.670753,0.2493943,0.8262595,4.530769,-0.215777,0.7846341,4.647109,-0.2040522,0.1134856,4.806332,-0.1119964,0.9616807,5.584102,-0.09915921,0.8919743,5.482022,-0.1116163,0.9616807,5.584102,0.1219478,0.8919743,5.482022,0.1344049,-0.11753,4.420857,-0.1249501,0.8812748,4.420189,-0.2266057,0.1134856,4.806332,-0.1119964,0.7569798,4.767585,-0.1915951,0.743683,4.890513,-0.1785796,0.385053,5.163958,-0.09952132,0.385053,5.163958,-0.09952132,0.7607008,5.136855,-0.1516057,0.6938733,5.489393,-0.08767646,0.2557456,2.066745,-0.2266057,0.2266314,1.76266,-0.2266057,0.2557456,2.066745,0.2493943,0.2266314,1.76266,0.2493943,0.7658386,4.13747,-0.2266057,0.8812748,4.420189,-0.2266057,-0.11753,4.420857,-0.1249501,0.3471766,2.670753,0.2493943,-0.5880103,2.697991,0.2010499,-0.5418657,3.14552,0.1877748,0.8919743,5.482022,0.1344049,0.6938731,5.489392,0.1104326,1.036195,5.778682,0.09939431,-0.11753,4.420857,-0.1249501,0.1134856,4.806332,-0.1119964,-0.1175308,4.420855,0.1476421,0.1134849,4.806329,0.1347038,-0.5418648,3.145523,-0.1650923,-0.4472076,3.585379,-0.1516603,-0.5418657,3.14552,0.1877748,-0.4472085,3.585376,0.1743397,-0.5880095,2.697994,-0.1783579,-0.5418648,3.145523,-0.1650923,-0.5880103,2.697991,0.2010499,-0.5418657,3.14552,0.1877748,0.8347403,5.37242,-0.1246318,0.8919743,5.482022,-0.1116163,0.6938733,5.489393,-0.08767646,0.4093689,2.969856,0.2493943,0.3471766,2.670753,0.2493943,-0.5418657,3.14552,0.1877748,0.1134856,4.806332,-0.1119964,0.385053,5.163958,-0.09952132,0.1134849,4.806329,0.1347038,0.3850525,5.163956,0.1222503,0.8812748,4.420189,0.2493943,0.7658386,4.13747,0.2493943,-0.1175308,4.420855,0.1476421,-0.5880095,2.697994,-0.1783579,0.3471766,2.670753,-0.2266057,-0.5418648,3.145523,-0.1650923,0.2018034,1.152398,-0.2266057,0.2039596,1,-0.2266057,0.2018034,1.152398,0.2493943,0.2039596,1,0.2493943,0.3471766,2.670753,-0.2266057,0.2959456,2.36958,-0.2266057,0.3471766,2.670753,0.2493943,0.2959456,2.36958,0.2493943,0.482438,3.26648,-0.2266057,0.4093689,2.969856,-0.2266057,0.482438,3.26648,0.2493943,0.4093689,2.969856,0.2493943,0.482438,3.26648,0.2493943,0.4093689,2.969856,0.2493943,-0.4472085,3.585376,0.1743397,0.2959456,2.36958,-0.2266057,0.3471766,2.670753,-0.2266057,-0.5880095,2.697994,-0.1783579,-0.5850809,2.248228,-0.1912962,0.2959456,2.36958,-0.2266057,-0.5880095,2.697994,-0.1783579,0.2959456,2.36958,0.2493943,-0.5850816,2.248226,0.2140036,-0.5880103,2.697991,0.2010499,0.1134856,4.806332,-0.1119964,0.7569798,4.767585,-0.1915951,0.385053,5.163958,-0.09952132,0.7907779,5.256827,-0.1380239,0.7907779,5.256827,0.1608125,0.8347403,5.37242,-0.1246318,0.8347403,5.37242,0.1474204,0.7449291,5.014179,-0.1651875,0.7607008,5.136855,-0.1516057,0.385053,5.163958,-0.09952132,0.2086425,1.45774,0.2493943,-0.4327427,1.363801,0.2383235,-0.5331153,1.801688,0.2264787,0.8262595,4.530769,-0.215777,0.8812748,4.420189,-0.2266057,0.8262595,4.530769,0.2385656,0.8812748,4.420189,0.2493943,0.7846341,4.647109,0.2268408,0.8262595,4.530769,0.2385656,0.1134849,4.806329,0.1347038,0.5662845,3.560223,0.2493943,-0.4472085,3.585376,0.1743397,-0.3051885,4.012216,0.1609077,0.2266314,1.76266,-0.2266057,0.2086425,1.45774,-0.2266057,0.2266314,1.76266,0.2493943,0.2086425,1.45774,0.2493943,0.2086425,1.45774,-0.2266057,0.2266314,1.76266,-0.2266057,-0.5331148,1.80169,-0.2037497,0.5662845,3.560223,-0.2266057,0.6607943,3.850685,-0.2266057,-0.3051876,4.012219,-0.1382252,0.3471766,2.670753,-0.2266057,0.4093689,2.969856,-0.2266057,-0.5418648,3.145523,-0.1650923,0.6607943,3.850685,-0.2266057,0.5662845,3.560223,-0.2266057,0.6607943,3.850685,0.2493943,0.5662845,3.560223,0.2493943,0.2959456,2.36958,-0.2266057,0.2557456,2.066745,-0.2266057,0.2959456,2.36958,0.2493943,0.2557456,2.066745,0.2493943,0.7607008,5.136855,-0.1516057,0.7907779,5.256827,-0.1380239,0.6938733,5.489393,-0.08767646,0.2266314,1.76266,0.2493943,0.2086425,1.45774,0.2493943,-0.5331153,1.801688,0.2264787,0.4093689,2.969856,0.2493943,-0.5418657,3.14552,0.1877748,-0.4472085,3.585376,0.1743397,0.6607943,3.850685,-0.2266057,0.6607943,3.850685,0.2493943,0.7658386,4.13747,-0.2266057,0.7658386,4.13747,0.2493943,0.7907779,5.256827,-0.1380239,0.8347403,5.37242,-0.1246318,0.6938733,5.489393,-0.08767646,0.01703096,1,-0.2233386,0.2039596,1,-0.2266057,-0.4327424,1.363802,-0.2155674,0.2018034,1.152398,-0.2266057,0.2086425,1.45774,-0.2266057,0.2039596,1,0.2493943,0.01703053,1,0.2461176,0.2018034,1.152398,0.2493943,-0.4327427,1.363801,0.2383235,0.2086425,1.45774,0.2493943,1.036195,5.778682,-0.07660569,1.036195,5.778682,0.09939431,0.6938733,5.489393,-0.08767646,0.6938731,5.489392,0.1104326,-0.3061082,1,-0.2250404,-0.4327424,1.363802,-0.2155674,-0.3061083,1,0.2478244,-0.4327427,1.363801,0.2383235,-0.3061082,1,-0.2250404,0.01703096,1,-0.2233386,-0.4327424,1.363802,-0.2155674,-0.5331148,1.80169,-0.2037497,-0.5850809,2.248228,-0.1912962,-0.5331153,1.801688,0.2264787,-0.5850816,2.248226,0.2140036,0.8347403,5.37242,0.1474204,0.7907779,5.256827,0.1608125,0.6938731,5.489392,0.1104326,0.8812748,4.420189,-0.2266057,0.8262595,4.530769,-0.215777,0.1134856,4.806332,-0.1119964,0.3471766,2.670753,0.2493943,0.2959456,2.36958,0.2493943,-0.5880103,2.697991,0.2010499,0.2266314,1.76266,-0.2266057,0.2557456,2.066745,-0.2266057,-0.5850809,2.248228,-0.1912962,0.6607943,3.850685,0.2493943,0.5662845,3.560223,0.2493943,-0.3051885,4.012216,0.1609077,0.385053,5.163958,-0.09952132,0.6938733,5.489393,-0.08767646,0.3850525,5.163956,0.1222503,0.6938731,5.489392,0.1104326,0.8812748,4.420189,-0.2266057,0.7658386,4.13747,-0.2266057,0.8812748,4.420189,0.2493943,0.7658386,4.13747,0.2493943,0.8919743,5.482022,-0.1116163,0.9616807,5.584102,-0.09915921,1.036195,5.778682,-0.07660569,1.042886,5.677233,-0.08743443,1.134457,5.760116,-0.07660569,-0.5850809,2.248228,-0.1912962,-0.5880095,2.697994,-0.1783579,-0.5850816,2.248226,0.2140036,-0.5880103,2.697991,0.2010499,-0.4327424,1.363802,-0.2155674,0.2086425,1.45774,-0.2266057,-0.5331148,1.80169,-0.2037497,-0.4327424,1.363802,-0.2155674,-0.5331148,1.80169,-0.2037497,-0.4327427,1.363801,0.2383235,-0.5331153,1.801688,0.2264787,0.6938733,5.489393,-0.08767646,0.8919743,5.482022,-0.1116163,1.036195,5.778682,-0.07660569,0.2039596,1,0.2493943,0.01703053,1,0.2461176,0.2039596,1,-0.2266057,0.01703096,1,-0.2233386,-0.3061082,1,-0.2250404,-0.3061083,1,0.2478244,0.476314,1,0.275,0.476314,1,-0.275,0.626314,1,0.275,0.626314,1,-0.275,1.082867E-14,1,-0.55,-0.476314,1,-0.275,0,1,0.55,-0.476314,1,0.275,-0.6253631,1,-0.275,-0.6253631,1,0.275,-0.7775536,0.9483383,-0.275,-0.7775536,0.9483383,0.275,-0.8021399,0.9267766,-0.275,-0.8021399,0.9267766,0.275,-0.8418695,0.875,-0.275,-0.8237015,0.9021903,-0.275,-0.8418695,0.875,0.275,-0.8237015,0.9021903,0.275,0.776314,0.65,0.275,0.626314,0.65,0.275,0.776314,0.85,0.275,0.626314,0.75,0.275,0.476314,0.75,0.275,0.476314,1,0.275,0.7750307,0.869579,0.275,0.7712029,0.8888228,0.275,0.7648959,0.9074025,0.275,0.7562178,0.925,0.275,0.745317,0.9413142,0.275,0.73238,0.956066,0.275,0.7176281,0.969003,0.275,0.701314,0.9799038,0.275,0.6837165,0.9885819,0.275,0.6651369,0.9948888,0.275,0.6458929,0.9987167,0.275,0.626314,1,0.275,0.745317,0.9413142,-0.275,0.7562178,0.925,-0.275,0.745317,0.9413142,0.275,0.7562178,0.925,0.275,0.7176281,0.969003,-0.275,0.7176281,0.969003,0.275,0.701314,0.9799038,-0.275,0.701314,0.9799038,0.275,0.73238,0.956066,-0.275,0.73238,0.956066,0.275,0.7176281,0.969003,-0.275,0.7176281,0.969003,0.275,0.7712029,0.8888228,-0.275,0.7750307,0.869579,-0.275,0.7712029,0.8888228,0.275,0.7750307,0.869579,0.275,0.7648959,0.9074025,-0.275,0.7712029,0.8888228,-0.275,0.7648959,0.9074025,0.275,0.7712029,0.8888228,0.275,0.776314,0.65,-0.275,0.626314,0.65,-0.275,0.776314,0.65,0.275,0.626314,0.65,0.275,0.701314,0.9799038,-0.275,0.701314,0.9799038,0.275,0.6837165,0.9885819,-0.275,0.6837165,0.9885819,0.275,0.626314,0.65,-0.275,0.626314,0.75,-0.275,0.626314,0.65,0.275,0.626314,0.75,0.275,0.7562178,0.925,-0.275,0.7648959,0.9074025,-0.275,0.7562178,0.925,0.275,0.7648959,0.9074025,0.275,0.7750307,0.869579,-0.275,0.776314,0.85,-0.275,0.7750307,0.869579,0.275,0.776314,0.85,0.275,0.73238,0.956066,-0.275,0.745317,0.9413142,-0.275,0.73238,0.956066,0.275,0.745317,0.9413142,0.275,0.6837165,0.9885819,-0.275,0.6837165,0.9885819,0.275,0.6651369,0.9948888,-0.275,0.6651369,0.9948888,0.275,0.6651369,0.9948888,-0.275,0.6651369,0.9948888,0.275,0.6458929,0.9987167,-0.275,0.6458929,0.9987167,0.275,0.6458929,0.9987167,-0.275,0.6458929,0.9987167,0.275,0.626314,1,-0.275,0.626314,1,0.275,-0.8753632,0.75,-0.275,-0.8732244,0.7826316,-0.275,-0.8753632,0.75,0.275,-0.8732244,0.7826316,0.275,0.476314,-0.75,0.275,0.3021023,-0.75,-0.1744188,0.476314,-0.75,-0.275,1.082867E-14,-0.75,-0.55,0.3021023,-0.75,0.1744188,0,-0.75,-0.3488377,-0.476314,-0.75,-0.275,-0.3021023,-0.75,-0.1744188,0,-0.75,0.55,3.609557E-15,-0.75,0.3488377,-0.3021023,-0.75,0.1744188,-0.476314,-0.75,0.275,-0.6253631,-0.75,-0.275,-0.6253631,-0.75,0.275,-0.476314,0.75,0.275,-0.6253631,0.75,0.275,-0.476314,1,0.275,-0.8753632,0.75,0.275,-0.8732244,0.7826316,0.275,-0.8668446,0.8147048,0.275,-0.856333,0.8456708,0.275,-0.8418695,0.875,0.275,-0.8237015,0.9021903,0.275,-0.8021399,0.9267766,0.275,-0.7775536,0.9483383,0.275,-0.7503632,0.9665064,0.275,-0.721034,0.9809698,0.275,-0.6900679,0.9914815,0.275,-0.6579947,0.9978611,0.275,-0.6253631,1,0.275,-0.8753632,-0.75,0.275,-0.6253631,-0.75,0.275,-0.476314,-0.75,0.275,-0.8732244,-0.7826316,0.275,-0.8668446,-0.8147048,0.275,-0.856333,-0.8456708,0.275,-0.8418695,-0.875,0.275,-0.8237015,-0.9021903,0.275,-0.8021399,-0.9267766,0.275,-0.7775536,-0.9483383,0.275,-0.7503632,-0.9665064,0.275,-0.721034,-0.9809698,0.275,-0.6900679,-0.9914815,0.275,-0.6579947,-0.9978611,0.275,-0.6253631,-1,0.275,-0.476314,-1,0.275,0,0.75,0.55,-0.476314,0.75,0.275,0,1,0.55,-0.476314,1,0.275,-0.8753632,0.75,-0.275,-0.8753632,0.75,0.275,-0.8753632,-0.75,-0.275,-0.8753632,-0.75,0.275,-0.476314,0.75,-0.275,1.082867E-14,0.75,-0.55,-0.476314,1,-0.275,1.082867E-14,1,-0.55,-0.8668446,0.8147048,-0.275,-0.856333,0.8456708,-0.275,-0.8668446,0.8147048,0.275,-0.856333,0.8456708,0.275,-0.8237015,0.9021903,-0.275,-0.8021399,0.9267766,-0.275,-0.8237015,0.9021903,0.275,-0.8021399,0.9267766,0.275,0.776314,0.85,-0.275,0.776314,0.65,-0.275,0.776314,0.85,0.275,0.776314,0.65,0.275,-0.856333,0.8456708,-0.275,-0.8418695,0.875,-0.275,-0.856333,0.8456708,0.275,-0.8418695,0.875,0.275,0,-1,0.55,-0.476314,-1,0.275,0,-0.75,0.55,-0.476314,-0.75,0.275,0.476314,0.75,0.275,0.3021023,0.75,0.1744188,0,0.75,0.55,3.609557E-15,0.75,0.3488377,-0.3021023,0.75,0.1744188,-0.476314,0.75,0.275,-0.3021023,0.75,-0.1744188,-0.476314,0.75,-0.275,-0.6253631,0.75,-0.275,-0.6253631,0.75,0.275,0.3021023,0.75,-0.1744188,0.476314,0.75,-0.275,0.626314,0.75,0.275,0.626314,0.75,-0.275,1.082867E-14,0.75,-0.55,0,0.75,-0.3488377,0.476314,0.75,0.275,0,0.75,0.55,0.476314,1,0.275,0,1,0.55,-0.476314,-1,-0.275,1.082867E-14,-1,-0.55,-0.476314,-0.75,-0.275,1.082867E-14,-0.75,-0.55,-0.721034,0.9809698,-0.275,-0.721034,0.9809698,0.275,-0.7503632,0.9665064,-0.275,-0.7503632,0.9665064,0.275,-0.7503632,0.9665064,-0.275,-0.7503632,0.9665064,0.275,-0.7775536,0.9483383,-0.275,-0.7775536,0.9483383,0.275,0.476314,-0.75,-0.275,0.476314,-1,-0.275,0.476314,-0.75,0.275,0.476314,-1,0.275,1.082867E-14,-1,-0.55,0.476314,-1,-0.275,1.082867E-14,-0.75,-0.55,0.476314,-0.75,-0.275,1.082867E-14,0.75,-0.55,0.476314,0.75,-0.275,1.082867E-14,1,-0.55,0.476314,1,-0.275,0.476314,-1,0.275,0,-1,0.55,0.476314,-0.75,0.275,0,-0.75,0.55,-0.6253631,0.75,-0.275,-0.6253631,-0.75,-0.275,-0.6253631,0.75,0.275,-0.6253631,-0.75,0.275,-0.8753632,-0.75,-0.275,-0.6253631,-0.75,-0.275,-0.8753632,0.75,-0.275,-0.6253631,0.75,-0.275,-0.8732244,0.7826316,-0.275,-0.476314,0.75,-0.275,-0.476314,1,-0.275,-0.8668446,0.8147048,-0.275,-0.856333,0.8456708,-0.275,-0.8418695,0.875,-0.275,-0.8237015,0.9021903,-0.275,-0.8021399,0.9267766,-0.275,-0.7775536,0.9483383,-0.275,-0.7503632,0.9665064,-0.275,-0.721034,0.9809698,-0.275,-0.6900679,0.9914815,-0.275,-0.6579947,0.9978611,-0.275,-0.6253631,1,-0.275,-0.6253631,-1,-0.275,-0.476314,-1,-0.275,-0.6579947,-0.9978611,-0.275,-0.476314,-0.75,-0.275,-0.6900679,-0.9914815,-0.275,-0.721034,-0.9809698,-0.275,-0.7503632,-0.9665064,-0.275,-0.7775536,-0.9483383,-0.275,-0.8021399,-0.9267766,-0.275,-0.8237015,-0.9021903,-0.275,-0.8418695,-0.875,-0.275,-0.856333,-0.8456708,-0.275,-0.8668446,-0.8147048,-0.275,-0.8732244,-0.7826316,-0.275,0.476314,0.75,-0.275,0.626314,0.75,-0.275,0.476314,1,-0.275,0.776314,0.85,-0.275,0.7750307,0.869579,-0.275,0.7712029,0.8888228,-0.275,0.7648959,0.9074025,-0.275,0.7562178,0.925,-0.275,0.745317,0.9413142,-0.275,0.73238,0.956066,-0.275,0.7176281,0.969003,-0.275,0.701314,0.9799038,-0.275,0.6837165,0.9885819,-0.275,0.6651369,0.9948888,-0.275,0.6458929,0.9987167,-0.275,0.626314,1,-0.275,0.776314,0.65,-0.275,0.626314,0.65,-0.275,-0.6900679,0.9914815,-0.275,-0.6900679,0.9914815,0.275,-0.721034,0.9809698,-0.275,-0.721034,0.9809698,0.275,-0.8732244,0.7826316,-0.275,-0.8668446,0.8147048,-0.275,-0.8732244,0.7826316,0.275,-0.8668446,0.8147048,0.275,-0.8732244,-0.7826316,-0.275,-0.8732244,-0.7826316,0.275,-0.8668446,-0.8147048,-0.275,-0.8668446,-0.8147048,0.275,-0.8237015,-0.9021903,-0.275,-0.8237015,-0.9021903,0.275,-0.8021399,-0.9267766,-0.275,-0.8021399,-0.9267766,0.275,-0.721034,-0.9809698,-0.275,-0.7503632,-0.9665064,-0.275,-0.721034,-0.9809698,0.275,-0.7503632,-0.9665064,0.275,-0.6253631,1,-0.275,-0.6253631,1,0.275,-0.6579947,0.9978611,-0.275,-0.6579947,0.9978611,0.275,-0.6579947,0.9978611,-0.275,-0.6579947,0.9978611,0.275,-0.6900679,0.9914815,-0.275,-0.6900679,0.9914815,0.275,-0.6579947,-0.9978611,-0.275,-0.6900679,-0.9914815,-0.275,-0.6579947,-0.9978611,0.275,-0.6900679,-0.9914815,0.275,-0.7503632,-0.9665064,-0.275,-0.7775536,-0.9483383,-0.275,-0.7503632,-0.9665064,0.275,-0.7775536,-0.9483383,0.275,-0.6253631,-1,-0.275,-0.6579947,-0.9978611,-0.275,-0.6253631,-1,0.275,-0.6579947,-0.9978611,0.275,-0.856333,-0.8456708,-0.275,-0.856333,-0.8456708,0.275,-0.8418695,-0.875,-0.275,-0.8418695,-0.875,0.275,-0.8732244,-0.7826316,-0.275,-0.8753632,-0.75,-0.275,-0.8732244,-0.7826316,0.275,-0.8753632,-0.75,0.275,-0.6900679,-0.9914815,-0.275,-0.721034,-0.9809698,-0.275,-0.6900679,-0.9914815,0.275,-0.721034,-0.9809698,0.275,-0.7775536,-0.9483383,-0.275,-0.8021399,-0.9267766,-0.275,-0.7775536,-0.9483383,0.275,-0.8021399,-0.9267766,0.275,-0.8237015,-0.9021903,-0.275,-0.8418695,-0.875,-0.275,-0.8237015,-0.9021903,0.275,-0.8418695,-0.875,0.275,-0.856333,-0.8456708,-0.275,-0.8668446,-0.8147048,-0.275,-0.856333,-0.8456708,0.275,-0.8668446,-0.8147048,0.275,0,-0.75,-0.3488377,0.3021023,-0.75,-0.1744188,0,0.75,-0.3488377,0.3021023,0.75,-0.1744188,0.3021023,0.75,-0.1744188,0.3021023,-0.75,-0.1744188,0.3021023,0.75,0.1744188,0.3021023,-0.75,0.1744188,3.609557E-15,-0.75,0.3488377,-0.3021023,-0.75,0.1744188,3.609557E-15,0.75,0.3488377,-0.3021023,0.75,0.1744188,-0.3021023,-0.75,-0.1744188,0,-0.75,-0.3488377,-0.3021023,0.75,-0.1744188,0,0.75,-0.3488377,0.3021023,-0.75,0.1744188,3.609557E-15,-0.75,0.3488377,0.3021023,0.75,0.1744188,3.609557E-15,0.75,0.3488377,-0.3021023,0.75,-0.1744188,-0.3021023,0.75,0.1744188,-0.3021023,-0.75,-0.1744188,-0.3021023,-0.75,0.1744188,0.476314,-1,-0.275,1.082867E-14,-1,-0.55,0.476314,-1,0.275,0,-1,0.55,0.476314,-1,0.275,1.082867E-14,-1,-0.55,-0.476314,-1,-0.275,0,-1,0.55,1.082867E-14,-1,-0.55,-0.476314,-1,0.275,0,-1,0.55,-0.476314,-1,-0.275,-0.6253631,-1,-0.275,-0.476314,-1,0.275,-0.476314,-1,-0.275,-0.6253631,-1,0.275,-0.476314,-1,0.275,-0.6253631,-1,-0.275
		} 
		PolygonVertexIndex: *1224 {
			a: 0,2,-2,3,5,-5,6,3,-5,7,9,-9,10,12,-12,13,11,-13,14,16,-16,17,19,-19,20,22,-22,23,25,-25,26,28,-28,29,27,-29,30,32,-32,33,30,-32,34,36,-36,37,39,-39,40,37,-39,41,43,-43,44,46,-46,47,49,-49,50,48,-50,51,53,-53,54,56,-56,57,59,-59,59,60,-59,61,63,-63,64,66,-66,67,69,-69,70,68,-70,71,73,-73,73,74,-73,75,77,-77,78,80,-80,81,83,-83,83,84,-83,85,87,-87,88,90,-90,91,93,-93,94,96,-96,97,99,-99,100,102,-102,103,101,-103,104,106,-106,104,107,-107,108,110,-110,111,113,-113,114,116,-116,117,119,-119,120,118,-120,121,123,-123,124,122,-124,125,122,-125,126,128,-128,129,131,-131,132,134,-134,135,137,-137,138,140,-140,141,139,-141,142,144,-144,145,143,-145,146,148,-148,149,151,-151,151,152,-151,153,155,-155,156,158,-158,159,161,-161,162,164,-164,165,163,-165,166,168,-168,169,171,-171,172,174,-174,175,177,-177,178,176,-178,179,181,-181,182,180,-182,183,185,-185,186,184,-186,187,189,-189,190,192,-192,193,195,-195,196,194,-196,197,199,-199,200,202,-202,203,205,-205,206,204,-206,207,209,-209,210,208,-210,211,213,-213,214,212,-214,215,217,-217,218,220,-220,221,223,-223,224,226,-226,227,229,-229,230,232,-232,233,231,-233,234,236,-236,237,239,-239,240,242,-242,242,243,-242,244,246,-246,247,249,-249,250,252,-252,253,251,-253,254,256,-256,257,259,-259,260,262,-262,263,265,-265,266,264,-266,267,269,-269,270,268,-270,271,273,-273,274,276,-276,277,279,-279,280,282,-282,283,281,-283,284,286,-286,287,289,-289,290,288,-290,291,290,-290,292,294,-294,295,293,-295,296,295,-295,297,299,-299,300,298,-300,301,303,-303,304,302,-304,305,307,-307,308,310,-310,311,309,-311,312,314,-314,315,317,-317,318,320,-320,321,323,-323,324,326,-326,327,329,-329,330,328,-330,331,333,-333,334,332,-334,335,337,-337,338,336,-338,339,338,-338,340,342,-342,343,341,-343,344,346,-346,347,349,-349,350,348,-350,351,353,-353,354,356,-356,357,355,-357,358,355,-358,359,355,-359,354,360,-357,356,360,-362,360,362,-362,363,361,-363,364,356,-362,357,356,-365,358,357,-365,365,358,-365,359,358,-366,360,354,-367,355,366,-355,359,366,-356,367,366,-360,365,367,-360,368,367,-366,369,367,-369,370,372,-372,373,371,-373,374,376,-376,377,375,-377,378,380,-380,381,379,-381,382,381,-381,383,382,-381,384,383,-381,385,383,-385,386,383,-386,387,383,-387,388,383,-388,389,383,-389,390,383,-390,391,383,-391,392,383,-392,393,383,-393,394,383,-394,395,383,-395,396,398,-398,399,397,-399,400,402,-402,403,401,-403,404,406,-406,407,405,-407,408,410,-410,411,409,-411,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,422,-422,423,421,-423,424,426,-426,427,425,-427,428,430,-430,431,429,-431,432,434,-434,435,433,-435,436,438,-438,439,437,-439,440,442,-442,443,441,-443,444,446,-446,447,445,-447,448,450,-450,451,449,-451,452,454,-454,455,453,-455,456,458,-458,459,457,-459,460,456,-458,461,457,-460,462,461,-460,463,461,-463,456,460,-465,465,464,-461,466,464,-466,467,464,-467,463,467,-467,462,467,-464,468,467,-463,469,467,-469,470,472,-472,473,471,-473,474,473,-473,475,474,-473,476,475,-473,477,476,-473,478,477,-473,479,478,-473,480,479,-473,481,480,-473,482,481,-473,483,482,-473,484,483,-473,485,484,-473,473,486,-472,471,486,-488,487,486,-489,486,489,-489,489,490,-489,490,491,-489,491,492,-489,492,493,-489,493,494,-489,494,495,-489,495,496,-489,496,497,-489,497,498,-489,498,499,-489,499,500,-489,501,488,-501,502,504,-504,505,503,-505,506,508,-508,509,507,-509,510,512,-512,513,511,-513,514,516,-516,517,515,-517,518,520,-520,521,519,-521,522,524,-524,525,523,-525,526,528,-528,529,527,-529,530,532,-532,533,531,-533,534,536,-536,537,535,-537,538,537,-537,539,538,-537,540,538,-540,541,540,-540,542,541,-540,543,542,-540,535,544,-535,544,545,-535,534,545,-547,547,546,-546,548,545,-545,549,548,-545,541,548,-550,540,541,-550,550,552,-552,553,551,-553,554,556,-556,557,555,-557,558,560,-560,561,559,-561,562,564,-564,565,563,-565,566,568,-568,569,567,-569,570,572,-572,573,571,-573,574,576,-576,577,575,-577,578,580,-580,581,579,-581,582,584,-584,585,583,-585,586,588,-588,589,587,-589,590,589,-589,591,589,-591,592,591,-591,593,592,-591,594,592,-594,595,592,-595,596,592,-596,597,592,-597,598,592,-598,599,592,-599,600,592,-600,601,592,-601,602,592,-602,603,592,-603,604,606,-606,607,605,-607,608,607,-607,609,607,-609,610,607,-610,611,607,-611,612,607,-612,613,607,-613,614,607,-614,615,607,-615,616,607,-616,617,607,-617,586,607,-618,587,607,-587,618,620,-620,621,619,-621,622,621,-621,623,622,-621,624,623,-621,625,624,-621,626,625,-621,627,626,-621,628,627,-621,629,628,-621,630,629,-621,631,630,-621,632,631,-621,633,632,-621,621,634,-620,635,619,-635,636,638,-638,639,637,-639,640,642,-642,643,641,-643,644,646,-646,647,645,-647,648,650,-650,651,649,-651,652,654,-654,655,653,-655,656,658,-658,659,657,-659,660,662,-662,663,661,-663,664,666,-666,667,665,-667,668,670,-670,671,669,-671,672,674,-674,675,673,-675,676,678,-678,679,677,-679,680,682,-682,683,681,-683,684,686,-686,687,685,-687,688,690,-690,691,689,-691,692,694,-694,695,693,-695,696,698,-698,699,697,-699,700,702,-702,703,701,-703,704,706,-706,707,705,-707,708,710,-710,711,709,-711,712,714,-714,715,713,-715,716,718,-718,719,717,-719,720,722,-722,723,721,-723,724,726,-726,727,729,-729,730,732,-732,733,735,-735,736,738,-738,739,741,-741
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *3672 {
				a: -0.1450541,0.1513725,0.9777759,-0.1205893,0.1631189,0.9792091,-0.1296989,0.1398812,0.9816372,0.8595845,-0.5109936,0,0.913762,-0.40625,0,0.913762,-0.40625,0,0.8595845,-0.5109936,0,0.8595845,-0.5109936,0,0.913762,-0.40625,0,-0.1098881,0.04910272,0.9927303,-0.1083649,0.09808587,0.9892604,-0.09149544,0.07125883,0.9932527,-0.9305744,0.3661029,1.15984E-06,-0.9305745,0.3661026,1.159837E-06,-0.8850536,0.4654891,2.289354E-06,-0.8850535,0.4654893,2.289356E-06,-0.8850536,0.4654891,2.289354E-06,-0.9305745,0.3661026,1.159837E-06,-0.09199212,0.03791127,0.9950377,-0.09149544,0.07125883,0.9932527,-0.07694601,0.05841475,0.9953226,-0.1018251,0.0395024,0.9940177,-0.09149544,0.07125883,0.9932527,-0.09199212,0.03791127,0.9950377,-0.07368667,0.01951973,-0.9970903,-0.06657352,0.04348737,-0.9968334,-0.07893134,0.02900715,-0.9964579,-0.04105293,0.004639918,0.9991462,-0.03879597,0.02200049,0.999005,-0.03383211,0.006038873,0.9994093,0.9978954,-0.06484497,0,0.9833667,-0.1816315,0,0.9978954,-0.06484497,0,0.9833667,-0.1816315,0,0.9978954,-0.06484497,0,0.9833667,-0.1816315,0,0.9986046,0.05280953,0,0.985491,0.1697274,0,0.985491,0.1697274,0,0.9986046,0.05280953,0,0.9986046,0.05280953,0,0.985491,0.1697274,0,-0.05479449,0.03657502,-0.9978275,-0.06657352,0.04348737,-0.9968334,-0.06587337,0.01903286,-0.9976464,0.985491,0.1697274,0,0.9587318,0.2843121,0,0.9587318,0.2843121,0,0.985491,0.1697274,0,0.985491,0.1697274,0,0.9587318,0.2843121,0,-0.1236665,0.08845036,0.988374,-0.1227316,0.1286377,0.9840677,-0.1083649,0.09808587,0.9892604,-0.1458376,0.0936887,0.9848623,-0.1227316,0.1286377,0.9840677,-0.1236665,0.08845036,0.988374,0.1856666,0.9826128,0,0.1856666,0.9826128,0,0.1856666,0.9826128,0,0.1856666,0.9826128,0,0.1856666,0.9826128,0,0.1856666,0.9826128,0,-0.04105293,0.004639918,0.9991462,-0.04593971,0.01043868,0.9988897,-0.03879597,0.02200049,0.999005,-0.01243847,0.006147102,0.9999038,-0.01492802,0.01731666,0.9997386,0.005279834,0.02794273,0.9995956,0.9978954,-0.06484497,0,0.9978954,-0.06484497,0,0.9986046,0.05280953,0,0.9978954,-0.06484497,0,0.9986046,0.05280953,0,0.9986046,0.05280953,0,-0.076841,0.05838774,-0.9953323,-0.09139615,0.07123432,-0.9932635,-0.09189168,0.03787839,-0.9950482,-0.1296989,0.1398812,0.9816372,-0.1227316,0.1286377,0.9840677,-0.1320024,0.1148844,0.9845694,-0.9649493,0.2624366,0,-0.9649495,0.2624359,0,-0.9305744,0.3661029,1.15984E-06,-0.9305745,0.3661026,1.159837E-06,-0.9305744,0.3661029,1.15984E-06,-0.9649495,0.2624359,0,0.9587318,0.2843121,0,0.9587318,0.2843121,0,0.9186839,0.3949934,0,0.9587318,0.2843121,0,0.9186839,0.3949934,0,0.9186839,0.3949934,0,-0.03383211,0.006038873,0.9994093,-0.03879597,0.02200049,0.999005,-0.02651872,0.02101384,0.9994274,-0.1298113,0.03767736,0.9908226,-0.1083649,0.09808587,0.9892604,-0.1098881,0.04910272,0.9927303,0.7160205,-0.6980792,0,0.7160205,-0.6980792,0,0.7933787,-0.6087285,0,0.7160205,-0.6980792,0,0.7933787,-0.6087285,0,0.7933787,-0.6087285,0,-0.02644764,0.02097619,-0.99943,-0.03870609,0.02197023,-0.999009,-0.03374844,0.006027409,-0.9994122,-0.06657352,0.04348737,-0.9968334,-0.076841,0.05838774,-0.9953323,-0.07893134,0.02900715,-0.9964579,-0.1296989,0.1398812,0.9816372,-0.1205893,0.1631189,0.9792091,-0.1227316,0.1286377,0.9840677,-0.07903603,0.02903845,0.9964488,-0.06667946,0.04351596,0.9968251,-0.07379346,0.01954801,0.9970819,-0.1456668,0.09370434,-0.9848861,-0.1225933,0.1286054,-0.9840891,-0.1318454,0.1148781,-0.9845912,0.9833667,-0.1816315,0,0.9552018,-0.2959555,0,0.9833667,-0.1816315,0,0.9552018,-0.2959555,0,0.9833667,-0.1816315,0,0.9552018,-0.2959555,0,0.6710603,-0.7414027,0,0.7160205,-0.6980792,0,0.6710603,-0.7414027,0,0.6710603,-0.7414027,0,0.7160205,-0.6980792,0,0.7160205,-0.6980792,0,-0.1061745,0.168933,0.9798922,-0.1205893,0.1631189,0.9792091,-0.1189009,0.1663425,0.9788733,-0.1320024,0.1148844,0.9845694,-0.1227316,0.1286377,0.9840677,-0.1458376,0.0936887,0.9848623,-0.1236665,0.08845036,0.988374,-0.1083649,0.09808587,0.9892604,-0.1211743,0.06249572,0.9906619,0.9991735,-0.04064952,0,0.9991735,-0.04064952,0,0.9999915,-0.004123661,0,0.9999915,-0.004123661,0,0.9999915,-0.004123661,0,0.9991735,-0.04064952,0,-0.1061745,0.168933,0.9798922,0.02030088,0.1074394,0.9940044,-0.004541478,0.1193789,0.9928384,0.02030088,0.1074394,0.9940044,-0.004541478,0.1193789,0.9928384,0.02030088,0.1074394,0.9940044,0.02030088,0.1074394,0.9940044,-0.004541478,0.1193789,0.9928384,0.02030088,0.1074394,0.9940044,-0.09189168,0.03787839,-0.9950482,-0.09139615,0.07123432,-0.9932635,-0.1017294,0.03946529,-0.9940289,-0.1210463,0.0625374,-0.990675,-0.1082569,0.09807251,-0.9892736,-0.1235323,0.08846467,-0.9883895,-0.03870609,0.02197023,-0.999009,-0.04584179,0.0104208,-0.9988944,-0.04095897,0.004629296,-0.9991502,-0.06587337,0.01903286,-0.9976464,-0.06657352,0.04348737,-0.9968334,-0.07368667,0.01951973,-0.9970903,0.9564204,-0.2919935,0,0.9564204,-0.2919935,0,0.9664446,-0.2568748,0,0.9664446,-0.2568748,0,0.9664446,-0.2568748,0,0.9564204,-0.2919935,0,0.9751791,-0.2214174,0,0.9751791,-0.2214174,0,0.9826128,-0.1856666,0,0.9826128,-0.1856666,0,0.9826128,-0.1856666,0,0.9751791,-0.2214174,0,-0.1296759,0.03774148,-0.9908379,-0.1082569,0.09807251,-0.9892736,-0.1210463,0.0625374,-0.990675,0.7933787,-0.6087285,0,0.7933787,-0.6087285,0,0.8595845,-0.5109936,0,0.7933787,-0.6087285,0,0.8595845,-0.5109936,0,0.8595845,-0.5109936,0,-0.09139615,0.07123432,-0.9932635,-0.1082569,0.09807251,-0.9892736,-0.109781,0.04909897,-0.9927424,-0.1235323,0.08846467,-0.9883895,-0.1225933,0.1286054,-0.9840891,-0.1456668,0.09370434,-0.9848861,-0.1225933,0.1286054,-0.9840891,-0.1204313,0.163054,-0.9792393,-0.1295416,0.1398443,-0.9816632,0.9935416,-0.1134684,0,0.9935416,-0.1134684,0,0.9970223,-0.07711334,0,0.9970223,-0.07711334,0,0.9970223,-0.07711334,0,0.9935416,-0.1134684,0,-0.1017294,0.03946529,-0.9940289,-0.09139615,0.07123432,-0.9932635,-0.109781,0.04909897,-0.9927424,-0.05566363,0.01422235,0.9983482,-0.05489871,0.03660454,0.9978207,-0.04806263,0.02949044,0.998409,-0.1061745,0.168933,0.9798922,-0.004541478,0.1193789,0.9928384,-0.1205893,0.1631189,0.9792091,-0.8850536,0.4654891,2.289354E-06,-0.8850535,0.4654893,2.289356E-06,-0.8288772,0.5594306,3.134412E-06,-0.8288767,0.5594313,3.134416E-06,-0.8288772,0.5594306,3.134412E-06,-0.8850535,0.4654893,2.289356E-06,-0.987806,0.1556893,-9.598481E-07,-0.9878063,0.1556881,-9.598568E-07,-0.9649493,0.2624366,0,-0.9649495,0.2624359,0,-0.9649493,0.2624366,0,-0.9878063,0.1556881,-9.598568E-07,-0.9988908,0.04708663,-1.622581E-06,-0.9988909,0.04708515,-1.622588E-06,-0.987806,0.1556893,-9.598481E-07,-0.9878063,0.1556881,-9.598568E-07,-0.987806,0.1556893,-9.598481E-07,-0.9988909,0.04708515,-1.622588E-06,-0.118738,0.1662752,-0.9789044,-0.1204313,0.163054,-0.9792393,-0.1060263,0.1688623,-0.9799203,-0.0659802,0.01905766,0.9976389,-0.05489871,0.03660454,0.9978207,-0.05566363,0.01422235,0.9983482,-0.8288772,0.5594306,3.134412E-06,-0.8288767,0.5594313,3.134416E-06,-0.7626536,0.6468072,3.334883E-06,-0.7626528,0.6468082,3.33488E-06,-0.7626536,0.6468072,3.334883E-06,-0.8288767,0.5594313,3.134416E-06,-0.1098881,0.04910272,0.9927303,-0.09149544,0.07125883,0.9932527,-0.1018251,0.0395024,0.9940177,-0.04796276,0.02945965,-0.9984146,-0.05479449,0.03657502,-0.9978275,-0.05555949,0.014201,-0.9983544,0.9999915,-0.004123661,0,0.9999915,-0.004123661,0,0.9999,0.01414656,0,0.9999,0.01414656,0,0.9999,0.01414656,0,0.9999915,-0.004123661,0,0.9826128,-0.1856666,0,0.9826128,-0.1856666,0,0.9887362,-0.1496683,0,0.9887362,-0.1496683,0,0.9887362,-0.1496683,0,0.9826128,-0.1856666,0,0.9664446,-0.2568748,0,0.9664446,-0.2568748,0,0.9751791,-0.2214174,0,0.9751791,-0.2214174,0,0.9751791,-0.2214174,0,0.9664446,-0.2568748,0,-0.07379346,0.01954801,0.9970819,-0.06667946,0.04351596,0.9968251,-0.0659802,0.01905766,0.9976389,-0.04584179,0.0104208,-0.9988944,-0.04796276,0.02945965,-0.9984146,-0.05555949,0.014201,-0.9983544,-0.03870609,0.02197023,-0.999009,-0.04796276,0.02945965,-0.9984146,-0.04584179,0.0104208,-0.9988944,-0.04593971,0.01043868,0.9988897,-0.04806263,0.02949044,0.998409,-0.03879597,0.02200049,0.999005,-0.1082569,0.09807251,-0.9892736,-0.1225933,0.1286054,-0.9840891,-0.1235323,0.08846467,-0.9883895,0.9552018,-0.2959555,0,0.913762,-0.40625,0,0.9552018,-0.2959555,0,0.913762,-0.40625,0,0.9552018,-0.2959555,0,0.913762,-0.40625,0,-0.1318454,0.1148781,-0.9845912,-0.1225933,0.1286054,-0.9840891,-0.1295416,0.1398443,-0.9816632,-0.02270351,0.004793727,0.9997308,-0.02651872,0.02101384,0.9994274,-0.01492802,0.01731666,0.9997386,0.9186839,0.3949934,0,0.9186839,0.3949934,0,0.8953153,0.445433,0,0.9186839,0.3949934,0,0.8953153,0.445433,0,0.8953153,0.445433,0,-0.1211743,0.06249572,0.9906619,-0.1083649,0.09808587,0.9892604,-0.1298113,0.03767736,0.9908226,-0.07903603,0.02903845,0.9964488,-0.07694601,0.05841475,0.9953226,-0.06667946,0.04351596,0.9968251,0.9970223,-0.07711334,0,0.9970223,-0.07711334,0,0.9991735,-0.04064952,0,0.9991735,-0.04064952,0,0.9991735,-0.04064952,0,0.9970223,-0.07711334,0,-0.02264107,0.004782954,-0.9997322,-0.02644764,0.02097619,-0.99943,-0.03374844,0.006027409,-0.9994122,-0.07893134,0.02900715,-0.9964579,-0.076841,0.05838774,-0.9953323,-0.09189168,0.03787839,-0.9950482,-0.05555949,0.014201,-0.9983544,-0.05479449,0.03657502,-0.9978275,-0.06587337,0.01903286,-0.9976464,0.9451184,-0.3267283,0,0.9451184,-0.3267283,0,0.9564204,-0.2919935,0,0.9564204,-0.2919935,0,0.9564204,-0.2919935,0,0.9451184,-0.3267283,0,0.9887362,-0.1496683,0,0.9887362,-0.1496683,0,0.9935416,-0.1134684,0,0.9935416,-0.1134684,0,0.9935416,-0.1134684,0,0.9887362,-0.1496683,0,-0.1295416,0.1398443,-0.9816632,-0.1204313,0.163054,-0.9792393,-0.1448618,0.1513232,-0.977812,-0.03383211,0.006038873,0.9994093,-0.02651872,0.02101384,0.9994274,-0.02270351,0.004793727,0.9997308,-0.0659802,0.01905766,0.9976389,-0.06667946,0.04351596,0.9968251,-0.05489871,0.03660454,0.9978207,0.9451184,-0.3267283,0,0.9325525,-0.3610343,0,0.9451184,-0.3267283,0,0.9325525,-0.3610343,0,0.9451184,-0.3267283,0,0.9325525,-0.3610343,0,-0.1448618,0.1513232,-0.977812,-0.1204313,0.163054,-0.9792393,-0.118738,0.1662752,-0.9789044,-0.01240194,0.006129039,-0.9999043,-0.01488552,0.01727498,-0.9997399,-0.01728308,0.0001216918,-0.9998506,-0.01728308,0.0001216918,-0.9998506,-0.01728308,0.0001216918,-0.9998506,-0.01488552,0.01727498,-0.9997399,-0.02264107,0.004782954,-0.9997322,-0.01728308,0.0001216918,-0.9998506,-0.01488552,0.01727498,-0.9997399,-0.01733398,0.0001220503,0.9998498,-0.01733398,0.0001220503,0.9998498,-0.01243847,0.006147102,0.9999038,-0.01492802,0.01731666,0.9997386,-0.01243847,0.006147102,0.9999038,-0.01733398,0.0001220503,0.9998498,-0.02270351,0.004793727,0.9997308,-0.01492802,0.01731666,0.9997386,-0.01733398,0.0001220503,0.9998498,-0.6454648,0.76379,1.586373E-06,-0.6871101,0.7265533,2.415486E-06,-0.6454648,0.76379,1.586373E-06,-0.6871086,0.7265548,2.415456E-06,-0.6454648,0.76379,1.586373E-06,-0.6871101,0.7265533,2.415486E-06,-0.9444205,-0.3287398,-6.808302E-07,-0.9444205,-0.3287398,-6.808302E-07,-0.9607683,-0.2773526,-1.080395E-06,-0.9607675,-0.277355,-1.080377E-06,-0.9607683,-0.2773526,-1.080395E-06,-0.9444205,-0.3287398,-6.808302E-07,0.005264323,0.02786064,-0.999598,-0.01488552,0.01727498,-0.9997399,-0.01240194,0.006129039,-0.9999043,-0.9853242,-0.170693,-1.698236E-06,-0.9853238,-0.1706951,-1.698228E-06,-0.9980683,-0.06212639,-1.88644E-06,-0.9980682,-0.06212822,-1.88644E-06,-0.9980683,-0.06212639,-1.88644E-06,-0.9853238,-0.1706951,-1.698228E-06,-0.1189009,0.1663425,0.9788733,-0.1205893,0.1631189,0.9792091,-0.1450541,0.1513725,0.9777759,-0.109781,0.04909897,-0.9927424,-0.1082569,0.09807251,-0.9892736,-0.1296759,0.03774148,-0.9908379,-0.05566363,0.01422235,0.9983482,-0.04806263,0.02949044,0.998409,-0.04593971,0.01043868,0.9988897,-0.03374844,0.006027409,-0.9994122,-0.03870609,0.02197023,-0.999009,-0.04095897,0.004629296,-0.9991502,-0.09199212,0.03791127,0.9950377,-0.07694601,0.05841475,0.9953226,-0.07903603,0.02903845,0.9964488,-0.7626536,0.6468072,3.334883E-06,-0.7626528,0.6468082,3.33488E-06,-0.6871101,0.7265533,2.415486E-06,-0.6871086,0.7265548,2.415456E-06,-0.6871101,0.7265533,2.415486E-06,-0.7626528,0.6468082,3.33488E-06,0.9258009,-0.3780115,0,0.9258009,-0.3780115,0,0.9325525,-0.3610343,0,0.9325525,-0.3610343,0,0.9325525,-0.3610343,0,0.9258009,-0.3780115,0,-0.1060263,0.1688623,-0.9799203,-0.004511694,0.1193646,-0.9928403,0.02030088,0.1074394,-0.9940044,0.02030088,0.1074394,-0.9940044,0.02030088,0.1074394,-0.9940044,-0.004511694,0.1193646,-0.9928403,0.02030088,0.1074394,-0.9940044,0.02030088,0.1074394,-0.9940044,-0.004511694,0.1193646,-0.9928403,-0.9980683,-0.06212639,-1.88644E-06,-0.9980682,-0.06212822,-1.88644E-06,-0.9988908,0.04708663,-1.622581E-06,-0.9988909,0.04708515,-1.622588E-06,-0.9988908,0.04708663,-1.622581E-06,-0.9980682,-0.06212822,-1.88644E-06,-0.01488552,0.01727498,-0.9997399,-0.02644764,0.02097619,-0.99943,-0.02264107,0.004782954,-0.9997322,-0.9607683,-0.2773526,-1.080395E-06,-0.9607675,-0.277355,-1.080377E-06,-0.9853242,-0.170693,-1.698236E-06,-0.9853238,-0.1706951,-1.698228E-06,-0.9853242,-0.170693,-1.698236E-06,-0.9607675,-0.277355,-1.080377E-06,-0.1204313,0.163054,-0.9792393,-0.004511694,0.1193646,-0.9928403,-0.1060263,0.1688623,-0.9799203,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.6087614,0.7933533,0,-0.7071068,0.7071068,0,-0.6087614,0.7933533,0,-0.7071068,0.7071068,0,-0.6087614,0.7933533,0,-0.7071068,0.7071068,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.7933533,0.6087614,0,-0.7933533,0.6087614,0,-0.7933533,0.6087614,0,-0.8660254,0.5,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7933533,0.6087614,0,0.7933533,0.6087614,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.7933533,0.6087614,0,0.6087614,0.7933533,0,0.5,0.8660254,0,0.6087614,0.7933533,0,0.5,0.8660254,0,0.6087614,0.7933533,0,0.5,0.8660254,0,0.7071068,0.7071068,0,0.6087614,0.7933533,0,0.7071068,0.7071068,0,0.6087614,0.7933533,0,0.7071068,0.7071068,0,0.6087614,0.7933533,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9914449,0.1305262,0,0.9914449,0.1305262,0,0.9914449,0.1305262,0,0.9659258,0.258819,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9659258,0.258819,0,0.9238795,0.3826834,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.5,0.8660254,0,0.3826834,0.9238795,0,0.5,0.8660254,0,0.3826834,0.9238795,0,0.5,0.8660254,0,0.3826834,0.9238795,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.8660254,0.5,0,0.8660254,0.5,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.8660254,0.5,0,0.9914449,0.1305262,0,0.9914449,0.1305262,0,0.9978589,0.06540313,0,0.9978589,0.06540313,0,0.9978589,0.06540313,0,0.9914449,0.1305262,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7933533,0.6087614,0,0.7933533,0.6087614,0,0.7933533,0.6087614,0,0.7071068,0.7071068,0,0.3826834,0.9238795,0,0.258819,0.9659258,0,0.3826834,0.9238795,0,0.258819,0.9659258,0,0.3826834,0.9238795,0,0.258819,0.9659258,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.258819,0.9659258,0,0.1305262,0.9914449,0,0.1305262,0.9914449,0,0.06540313,0.9978589,0,0.1305262,0.9914449,0,0.06540313,0.9978589,0,0.1305262,0.9914449,0,0.06540313,0.9978589,0,-0.9978589,0.06540313,0,-0.9978589,0.06540313,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9978589,0.06540313,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9659258,0.258819,0,-0.7933533,0.6087614,0,-0.7933533,0.6087614,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7933533,0.6087614,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.9238795,0.3826834,0,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.3826834,0.9238795,0,-0.5,0.8660254,0,-0.3826834,0.9238795,0,-0.5,0.8660254,0,-0.3826834,0.9238795,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.6087614,0.7933533,0,-0.5,0.8660254,0,-0.6087614,0.7933533,0,-0.5,0.8660254,0,-0.6087614,0.7933533,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.258819,0.9659258,0,-0.3826834,0.9238795,0,-0.258819,0.9659258,0,-0.3826834,0.9238795,0,-0.258819,0.9659258,0,-0.3826834,0.9238795,0,-0.9914449,0.1305262,0,-0.9914449,0.1305262,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9914449,0.1305262,0,-0.9914449,-0.1305262,0,-0.9659258,-0.258819,0,-0.9914449,-0.1305262,0,-0.9659258,-0.258819,0,-0.9914449,-0.1305262,0,-0.9659258,-0.258819,0,-0.7933533,-0.6087614,0,-0.7071068,-0.7071068,0,-0.7933533,-0.6087614,0,-0.7071068,-0.7071068,0,-0.7933533,-0.6087614,0,-0.7071068,-0.7071068,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.3826834,-0.9238795,0,-0.06540313,0.9978589,0,-0.1305262,0.9914449,0,-0.06540313,0.9978589,0,-0.1305262,0.9914449,0,-0.06540313,0.9978589,0,-0.1305262,0.9914449,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,0.9914449,0,-0.258819,0.9659258,0,-0.1305262,-0.9914449,0,-0.1305262,-0.9914449,0,-0.258819,-0.9659258,0,-0.258819,-0.9659258,0,-0.258819,-0.9659258,0,-0.1305262,-0.9914449,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.6087614,-0.7933533,0,-0.6087614,-0.7933533,0,-0.6087614,-0.7933533,0,-0.5,-0.8660254,0,-0.06540313,-0.9978589,0,-0.06540313,-0.9978589,0,-0.1305262,-0.9914449,0,-0.1305262,-0.9914449,0,-0.1305262,-0.9914449,0,-0.06540313,-0.9978589,0,-0.9238795,-0.3826834,0,-0.8660254,-0.5,0,-0.9238795,-0.3826834,0,-0.8660254,-0.5,0,-0.9238795,-0.3826834,0,-0.8660254,-0.5,0,-0.9914449,-0.1305262,0,-0.9914449,-0.1305262,0,-0.9978589,-0.06540313,0,-0.9978589,-0.06540313,0,-0.9978589,-0.06540313,0,-0.9914449,-0.1305262,0,-0.258819,-0.9659258,0,-0.258819,-0.9659258,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.258819,-0.9659258,0,-0.6087614,-0.7933533,0,-0.6087614,-0.7933533,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.6087614,-0.7933533,0,-0.7933533,-0.6087614,0,-0.7933533,-0.6087614,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.7933533,-0.6087614,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.9238795,-0.3826834,0,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1484 {
				a: 6.347063,40.88855,6.129405,39.93328,5.532285,42.74033,-1.058306,41.51373,0.9813527,40.54014,-1.160791,40.54014,0.8788685,41.51373,7.102386,34.5344,-0.8029299,34.53967,0.9964182,37.58831,-1.088392,27.70686,-0.9838611,31.24755,1.266985,27.70683,1.162534,31.24752,5.343176,30.14228,-2.294328,31.4172,-0.8297502,34.64248,6.19979,32.50545,5.377027,30.2457,-0.8013653,34.7384,-3.91919,25.68332,-4.577626,27.99677,3.424415,28.19489,2.092947,16.26604,1.863896,13.87165,-4.533599,17.69504,1.300689,39.90737,-1.480127,39.90737,1.193746,40.88127,-1.373183,40.88127,-1.585576,37.65493,1.508623,36.68134,-1.688061,36.68134,1.406139,37.65493,4.186112,24.6797,-3.31933,23.29524,3.447973,28.14629,-1.688061,35.25499,1.60671,34.2817,-1.786148,34.2817,1.508623,35.25499,6.115675,37.14202,1.010227,37.44938,3.122875,40.28635,6.027853,38.29822,6.146802,37.3265,3.142355,40.4597,0.6031944,-0.35646,-0.7826324,-0.35646,0.6031944,0.4309416,-0.7826324,0.4309416,2.092346,16.26314,-4.534198,17.69215,2.408617,18.6477,0.1238609,7.816771,-2.420575,7.816771,-3.417287,10.68246,1.300689,39.53882,1.406139,38.56503,-1.480127,39.53882,-1.585576,38.56503,2.308703,31.42958,-5.328718,30.15464,0.8441085,34.65485,6.116593,40.0546,6.007184,39.08097,3.130494,40.2697,-1.194178,25.67578,-1.088389,29.21787,1.372751,25.67575,1.266989,29.21785,1.60671,32.37129,1.699031,31.39836,-1.786148,32.37129,-1.878469,31.39836,1.840482,13.82809,-4.144491,14.13549,-4.556341,17.6526,6.697379,35.62677,7.138046,34.75563,1.026116,37.79758,0.6884601,39.0898,0.7807811,38.11687,-0.867898,39.0898,-0.960219,38.11687,4.149789,14.13999,-1.835166,13.83257,4.561626,17.65709,3.425654,28.1079,-4.57638,27.90952,2.317976,31.47401,6.115324,39.87103,3.129288,40.08701,5.529665,42.68049,4.59094,27.99297,3.932509,25.67953,-3.411172,28.19106,-5.992334,38.2179,-5.987761,39.19738,-3.110156,40.38368,1.193746,40.69009,-1.373183,40.69009,1.086802,41.66399,-1.26624,41.66399,-0.7826324,37.05885,0.6031944,37.05885,0.6884601,36.08633,-0.867898,36.08633,7.098186,42.45827,6.662088,41.58179,5.526988,42.51719,6.012194,39.17828,6.016785,38.19881,3.134501,40.36457,6.118419,37.36047,6.346251,36.40914,1.012886,37.66641,1.784297,11.51217,1.784297,9.107309,-1.963735,11.51217,-1.963735,9.107309,7.000346,42.78659,8.141336,45.13609,7.551103,43.59504,8.192267,44.33262,8.914886,44.98904,-5.358509,30.25247,-6.181279,32.51221,0.8197833,34.74518,-6.324895,36.42239,-6.097072,37.37371,-0.9916227,37.67968,4.541661,17.69316,-2.084851,16.26413,-2.401123,18.6487,-3.346781,23.35267,-3.920545,25.68868,3.423105,28.20012,1.784297,28.18047,1.784297,25.77515,-1.963735,28.18047,-1.963735,25.77515,1.784297,23.5512,1.784297,21.14569,-1.963735,23.5512,-1.963735,21.14569,-6.665399,35.56973,-6.328782,36.48723,-0.9951326,37.74292,0.7807811,40.58114,0.8788685,39.60785,-0.960219,40.58114,-1.058306,39.60785,0.8211244,34.55643,-7.084109,34.55114,-0.9782377,37.60506,-6.119653,37.34194,-6.000719,38.31366,-3.11533,40.47515,-3.106603,40.11471,-6.09257,39.89872,-5.507011,42.70816,1.784297,16.39143,1.784297,13.98611,-1.963735,16.39143,-1.963735,13.98611,-6.179869,32.50261,-7.084146,34.73065,0.8210867,34.73592,2.829562,20.95332,-4.543942,21.16792,-4.186356,24.69391,7.09823,42.49388,5.52703,42.55276,8.195077,44.86431,-0.9838624,29.3827,-0.8818584,32.92127,1.162533,29.38268,1.060663,32.92125,-1.299937,23.31593,-1.194174,26.85865,1.478544,23.3159,1.372755,26.85862,-1.404387,20.65713,-1.299933,24.19966,1.583076,20.65711,1.478548,24.19964,-6.641662,41.61333,-7.077784,42.48979,-5.506619,42.54873,3.33939,23.35996,2.850636,21.00462,-4.166324,24.74326,-0.8818575,30.68056,-0.7836193,34.21638,1.060664,30.68053,0.9626135,34.21636,7.10243,34.7232,6.198162,32.49516,-0.8028861,34.72845,4.553147,21.17425,-2.820312,20.95963,4.195544,24.70024,1.784297,9.050615,1.784297,7.850509,-1.963735,9.050615,-1.963735,7.850509,1.784297,21.19018,1.784297,18.78467,-1.963735,21.19018,-1.963735,18.78467,1.784297,25.88236,1.784297,23.47691,-1.963735,25.88236,-1.963735,23.47691,3.934027,25.68537,3.360268,23.34935,-3.409698,28.19678,-2.418696,18.6429,-2.821559,21.01444,4.551904,21.22894,4.536271,17.64687,-2.406462,18.60278,4.563786,21.18976,2.414529,18.5976,-4.528234,17.64167,-4.555765,21.18457,-0.9894639,37.47113,-6.094829,37.16376,-3.102133,40.30808,1.086802,40.90221,-1.26624,40.90221,0.9813527,41.876,-1.160791,41.876,-5.984035,39.10326,-6.093463,40.07688,-3.107432,40.29199,1.682822,11.43231,-3.368181,10.69245,-4.160264,14.14124,1.699031,29.0427,1.784297,28.07019,-1.878469,29.0427,-1.963735,28.07019,6.350953,36.47746,6.687576,35.55996,1.01721,37.73312,4.589542,27.89955,-3.412563,28.0979,-2.304902,31.46402,1.784297,13.96022,1.784297,11.55509,-1.963735,13.96022,-1.963735,11.55509,-1.695608,11.47518,-1.837189,13.87613,4.147771,14.18345,-4.596488,27.98303,-5.337873,30.27103,2.299968,31.54346,-2.839292,21.00698,-3.328048,23.36231,4.177605,24.74564,1.784297,30.44238,1.784297,28.03726,-1.963735,30.44238,-1.963735,28.03726,1.784297,18.8025,1.784297,16.39706,-1.963735,18.8025,-1.963735,16.39706,-6.101873,39.95974,-6.319559,40.915,-5.504879,42.76677,1.842718,13.87581,1.701137,11.47485,-4.14226,14.18311,3.329688,23.28753,-4.175814,24.67197,-3.437692,28.13856,1.784297,30.26013,-1.963735,30.26013,1.784297,32.665,-1.963735,32.665,-6.313692,40.80801,-6.643917,41.73015,-5.507014,42.66328,-0.1644756,7.873802,-1.636579,7.873802,3.377575,10.73838,-1.619604,9.073789,-1.673447,11.47805,1.639779,7.873779,0.1676706,7.873779,1.622804,9.073766,-3.374382,10.73835,1.676647,11.47803,-0.6031744,35.60135,0.7826524,35.60135,-0.6903524,32.07231,0.8695611,32.0723,-1.77197,8.228745,-1.697379,11.26191,1.951375,8.228744,1.876566,11.2619,2.419599,7.821945,-0.1248389,7.821945,3.416312,10.68764,-1.604321,14.57667,-1.50626,18.11646,1.783305,14.57666,1.685077,18.11644,6.666452,41.70133,6.336253,40.77918,5.529489,42.63446,-7.114176,34.76101,-6.673514,35.63215,-1.002349,37.80299,2.830946,21.01284,2.428085,18.6413,-4.542562,21.22732,-1.856341,13.87238,-2.085393,16.26676,4.541121,17.69578,5.35354,30.26594,4.612162,27.97794,-2.284384,31.53834,-0.783619,31.58178,-0.6903417,35.11438,0.9626138,31.58177,0.8695719,35.11437,1.784297,34.84525,1.784297,32.4407,-1.963735,34.84525,-1.963735,32.4407,-7.00401,42.80586,-7.554767,43.61432,-8.145,45.15536,-8.195931,44.3519,-8.91855,45.00832,-1.50626,17.73221,-1.404385,21.27374,1.685076,17.73219,1.583078,21.27372,3.371959,10.69656,-1.679037,11.43641,4.164032,14.14535,-1.697376,11.22844,-1.604322,14.7658,1.876569,11.22843,1.783303,14.76578,-5.50663,42.58343,-7.077797,42.52454,-8.174708,44.89494,-1.605981,1.963735,-0.1340987,1.937934,-1.605981,-1.784297,-0.134102,-1.758572,2.410301,-1.771971,2.410301,1.951373,-3.750504,2.165354,-3.750504,-2.165354,-4.931606,2.165354,-4.931606,-2.165354,-8.526513E-14,-4.330709,3.750504,-2.165354,0,4.330709,3.750504,2.165354,4.924119,-2.165354,4.924119,2.165354,-2.165354,0.3203718,2.165354,0.3203718,-2.165354,0.06287917,2.165354,0.06287917,-2.165354,2.045813,-2.165354,2.303306,2.165354,2.045813,2.165354,2.303306,6.112709,5.11811,4.931606,5.11811,6.112709,6.692914,4.931606,5.905512,3.750504,5.905512,3.750504,7.874016,6.102604,6.847078,6.072464,6.998605,6.022802,7.144902,5.954471,7.283464,5.868638,7.411923,5.766772,7.528079,5.650616,7.629945,5.522157,7.715778,5.383595,7.78411,5.237298,7.833771,5.085771,7.863911,4.931606,7.874016,2.165354,2.902348,2.165354,2.747853,-2.165354,2.902348,-2.165354,2.747853,2.165354,-0.4593449,-2.165354,-0.4593449,2.165354,-0.3048494,-2.165354,-0.3048494,2.165354,0.6279189,-2.165354,0.6279189,2.165354,0.7824144,-2.165354,0.7824144,2.165354,5.67945,2.165354,5.524954,-2.165354,5.67945,-2.165354,5.524954,2.165354,4.829756,2.165354,4.675261,-2.165354,4.829756,-2.165354,4.675261,6.112709,-2.165354,4.931606,-2.165354,6.112709,2.165354,4.931606,2.165354,2.165354,-1.540071,-2.165354,-1.540071,2.165354,-1.385575,-2.165354,-1.385575,-2.165354,5.11811,-2.165354,5.905512,2.165354,5.11811,2.165354,5.905512,2.165354,3.898746,2.165354,3.74425,-2.165354,3.898746,-2.165354,3.74425,2.165354,6.433289,2.165354,6.278793,-2.165354,6.433289,-2.165354,6.278793,2.165354,1.857613,2.165354,1.703117,-2.165354,1.857613,-2.165354,1.703117,2.165354,-2.595768,-2.165354,-2.595768,2.165354,-2.441272,-2.165354,-2.441272,2.165354,-3.608372,-2.165354,-3.608372,2.165354,-3.453876,-2.165354,-3.453876,2.165354,-4.560557,-2.165354,-4.560557,2.165354,-4.406062,-2.165354,-4.406062,-2.165354,5.442069,-2.165354,5.699561,2.165354,5.442069,2.165354,5.699561,-3.750504,2.165354,-2.378758,-1.373377,-3.750504,-2.165354,-8.526513E-14,-4.330709,-2.378758,1.373377,0,-2.746753,3.750504,-2.165354,2.378758,-1.373377,0,4.330709,-2.842171E-14,2.746753,2.378758,1.373377,3.750504,2.165354,4.924119,-2.165354,4.924119,2.165354,-3.750504,5.905512,-4.924119,5.905512,-3.750504,7.874016,-6.892623,5.905512,-6.875782,6.162453,-6.825548,6.414998,-6.74278,6.658826,-6.628894,6.889764,-6.485838,7.103861,-6.316062,7.297454,-6.122469,7.467231,-5.908371,7.610286,-5.677433,7.724172,-5.433606,7.806941,-5.181061,7.857175,-4.924119,7.874016,-6.892623,-5.905512,-4.924119,-5.905512,-3.750504,-5.905512,-6.875782,-6.162453,-6.825548,-6.414998,-6.74278,-6.658826,-6.628894,-6.889764,-6.485838,-7.103861,-6.316062,-7.297454,-6.122469,-7.467231,-5.908371,-7.610286,-5.677433,-7.724172,-5.433606,-7.806941,-5.181061,-7.857175,-4.924119,-7.874016,-3.750504,-7.874016,2.165354,5.905512,-2.165354,5.905512,2.165354,7.874016,-2.165354,7.874016,-2.165354,5.905512,2.165354,5.905512,-2.165354,-5.905512,2.165354,-5.905512,2.165354,5.905512,-2.165354,5.905512,2.165354,7.874016,-2.165354,7.874016,-2.165354,3.880554,-2.165354,4.138047,2.165354,3.880554,2.165354,4.138047,-2.165354,1.064555,-2.165354,1.322048,2.165354,1.064555,2.165354,1.322048,2.165354,6.692914,2.165354,5.11811,-2.165354,6.692914,-2.165354,5.11811,-2.165354,2.989864,-2.165354,3.247357,2.165354,2.989864,2.165354,3.247357,2.165354,-7.874016,-2.165354,-7.874016,2.165354,-5.905512,-2.165354,-5.905512,3.750504,2.165354,2.378758,1.373377,0,4.330709,2.842171E-14,2.746753,-2.378758,1.373377,-3.750504,2.165354,-2.378758,-1.373377,-3.750504,-2.165354,-4.924119,-2.165354,-4.924119,2.165354,2.378758,-1.373377,3.750504,-2.165354,4.931606,2.165354,4.931606,-2.165354,8.526513E-14,-4.330709,0,-2.746753,2.165354,5.905512,-2.165354,5.905512,2.165354,7.874016,-2.165354,7.874016,2.165354,-7.874016,-2.165354,-7.874016,2.165354,-5.905512,-2.165354,-5.905512,-2.165354,-1.675621,2.165354,-1.675621,-2.165354,-1.933114,2.165354,-1.933114,-2.165354,-0.6845828,2.165354,-0.6845828,-2.165354,-0.9420754,2.165354,-0.9420754,2.165354,-5.905512,2.165354,-7.874016,-2.165354,-5.905512,-2.165354,-7.874016,2.165354,-7.874016,-2.165354,-7.874016,2.165354,-5.905512,-2.165354,-5.905512,2.165354,5.905512,-2.165354,5.905512,2.165354,7.874016,-2.165354,7.874016,2.165354,-7.874016,-2.165354,-7.874016,2.165354,-5.905512,-2.165354,-5.905512,2.165354,5.905512,2.165354,-5.905512,-2.165354,5.905512,-2.165354,-5.905512,6.892623,-5.905512,4.924119,-5.905512,6.892623,5.905512,4.924119,5.905512,6.875782,6.162453,3.750504,5.905512,3.750504,7.874016,6.825548,6.414998,6.74278,6.658826,6.628894,6.889764,6.485838,7.103861,6.316062,7.297454,6.122469,7.467231,5.908371,7.610286,5.677433,7.724172,5.433606,7.806941,5.181061,7.857175,4.924119,7.874016,4.924119,-7.874016,3.750504,-7.874016,5.181061,-7.857175,3.750504,-5.905512,5.433606,-7.806941,5.677433,-7.724172,5.908371,-7.610286,6.122469,-7.467231,6.316062,-7.297454,6.485838,-7.103861,6.628894,-6.889764,6.74278,-6.658826,6.825548,-6.414998,6.875782,-6.162453,-3.750504,5.905512,-4.931606,5.905512,-3.750504,7.874016,-6.112709,6.692914,-6.102604,6.847078,-6.072464,6.998605,-6.022802,7.144902,-5.954471,7.283464,-5.868638,7.411923,-5.766772,7.528079,-5.650616,7.629945,-5.522157,7.715778,-5.383595,7.78411,-5.237298,7.833771,-5.085771,7.863911,-4.931606,7.874016,-6.112709,5.11811,-4.931606,5.11811,-2.165354,-2.635786,2.165354,-2.635786,-2.165354,-2.893279,2.165354,-2.893279,-2.165354,4.702645,-2.165354,4.960137,2.165354,4.702645,2.165354,4.960137,-2.165354,-4.702645,2.165354,-4.702645,-2.165354,-4.960137,2.165354,-4.960137,-2.165354,-1.064555,2.165354,-1.064555,-2.165354,-1.322048,2.165354,-1.322048,-2.165354,1.675621,-2.165354,1.933114,2.165354,1.675621,2.165354,1.933114,-2.165354,-4.398591,2.165354,-4.398591,-2.165354,-4.656084,2.165354,-4.656084,-2.165354,-3.548649,2.165354,-3.548649,-2.165354,-3.806142,2.165354,-3.806142,-2.165354,3.548649,-2.165354,3.806142,2.165354,3.548649,2.165354,3.806142,-2.165354,0.6845828,-2.165354,0.9420754,2.165354,0.6845828,2.165354,0.9420754,-2.165354,4.398591,-2.165354,4.656084,2.165354,4.398591,2.165354,4.656084,-2.165354,-2.989864,2.165354,-2.989864,-2.165354,-3.247357,2.165354,-3.247357,-2.165354,-5.699561,-2.165354,-5.442069,2.165354,-5.699561,2.165354,-5.442069,-2.165354,2.635786,-2.165354,2.893279,2.165354,2.635786,2.165354,2.893279,-2.165354,-0.3203718,-2.165354,-0.06287917,2.165354,-0.3203718,2.165354,-0.06287917,-2.165354,-2.303306,-2.165354,-2.045813,2.165354,-2.303306,2.165354,-2.045813,-2.165354,-4.138047,-2.165354,-3.880554,2.165354,-4.138047,2.165354,-3.880554,1.373377,-5.905512,-1.373377,-5.905512,1.373377,5.905512,-1.373377,5.905512,1.373377,5.905512,1.373377,-5.905512,-1.373377,5.905512,-1.373377,-5.905512,1.373377,-5.905512,-1.373377,-5.905512,1.373377,5.905512,-1.373377,5.905512,1.373377,-5.905512,-1.373377,-5.905512,1.373377,5.905512,-1.373377,5.905512,1.373377,-5.905512,-1.373377,-5.905512,1.373377,5.905512,-1.373377,5.905512,-1.373377,5.905512,1.373377,5.905512,-1.373377,-5.905512,1.373377,-5.905512,2.165354,4.960137,2.165354,4.702645,-2.165354,4.960137,2.165354,4.960137,2.165354,4.702645,-2.165354,4.960137,2.165354,4.960137,2.165354,4.702645,-2.165354,4.960137,2.165354,4.960137,2.165354,4.702645,-2.165354,4.960137,2.165354,4.960137,2.165354,4.702645,-2.165354,4.960137,2.165354,4.960137,2.165354,4.702645,-2.165354,4.960137
				}
			UVIndex: *1224 {
				a: 0,2,1,3,5,4,6,3,4,7,9,8,10,12,11,13,11,12,14,16,15,17,19,18,20,22,21,23,25,24,26,28,27,29,27,28,30,32,31,33,30,31,34,36,35,37,39,38,40,37,38,41,43,42,44,46,45,47,49,48,50,48,49,51,53,52,54,56,55,57,59,58,59,60,58,61,63,62,64,66,65,67,69,68,70,68,69,71,73,72,73,74,72,75,77,76,78,80,79,81,83,82,83,84,82,85,87,86,88,90,89,91,93,92,94,96,95,97,99,98,100,102,101,103,101,102,104,106,105,104,107,106,108,110,109,111,113,112,114,116,115,117,119,118,120,118,119,121,123,122,124,122,123,125,122,124,126,128,127,129,131,130,132,134,133,135,137,136,138,140,139,141,139,140,142,144,143,145,143,144,146,148,147,149,151,150,151,152,150,153,155,154,156,158,157,159,161,160,162,164,163,165,163,164,166,168,167,169,171,170,172,174,173,175,177,176,178,176,177,179,181,180,182,180,181,183,185,184,186,184,185,187,189,188,190,192,191,193,195,194,196,194,195,197,199,198,200,202,201,203,205,204,206,204,205,207,209,208,210,208,209,211,213,212,214,212,213,215,217,216,218,220,219,221,223,222,224,226,225,227,229,228,230,232,231,233,231,232,234,236,235,237,239,238,240,242,241,242,243,241,244,246,245,247,249,248,250,252,251,253,251,252,254,256,255,257,259,258,260,262,261,263,265,264,266,264,265,267,269,268,270,268,269,271,273,272,274,276,275,277,279,278,280,282,281,283,281,282,284,286,285,287,289,288,290,288,289,291,290,289,292,294,293,295,293,294,296,295,294,297,299,298,300,298,299,301,303,302,304,302,303,305,307,306,308,310,309,311,309,310,312,314,313,315,317,316,318,320,319,321,323,322,324,326,325,327,329,328,330,328,329,331,333,332,334,332,333,335,337,336,338,336,337,339,338,337,340,342,341,343,341,342,344,346,345,347,349,348,350,348,349,351,353,352,354,356,355,357,355,356,358,355,357,359,355,358,354,360,356,356,360,361,360,362,361,363,361,362,364,356,361,357,356,364,358,357,364,365,358,364,359,358,365,360,354,366,355,366,354,359,366,355,367,366,359,365,367,359,368,367,365,369,367,368,370,372,371,373,371,372,374,376,375,377,375,376,378,380,379,381,379,380,382,381,380,383,382,380,384,383,380,385,383,384,386,383,385,387,383,386,388,383,387,389,383,388,390,383,389,391,383,390,392,383,391,393,383,392,394,383,393,395,383,394,396,398,397,399,397,398,400,402,401,403,401,402,404,406,405,407,405,406,408,410,409,411,409,410,412,414,413,415,413,414,416,418,417,419,417,418,420,422,421,423,421,422,424,426,425,427,425,426,428,430,429,431,429,430,432,434,433,435,433,434,436,438,437,439,437,438,440,442,441,443,441,442,444,446,445,447,445,446,448,450,449,451,449,450,452,454,453,455,453,454,456,458,457,459,457,458,460,456,457,461,457,459,462,461,459,463,461,462,456,460,464,465,464,460,466,464,465,467,464,466,463,467,466,462,467,463,468,467,462,469,467,468,470,472,471,473,471,472,474,473,472,475,474,472,476,475,472,477,476,472,478,477,472,479,478,472,480,479,472,481,480,472,482,481,472,483,482,472,484,483,472,485,484,472,473,486,471,471,486,487,487,486,488,486,489,488,489,490,488,490,491,488,491,492,488,492,493,488,493,494,488,494,495,488,495,496,488,496,497,488,497,498,488,498,499,488,499,500,488,501,488,500,502,504,503,505,503,504,506,508,507,509,507,508,510,512,511,513,511,512,514,516,515,517,515,516,518,520,519,521,519,520,522,524,523,525,523,524,526,528,527,529,527,528,530,532,531,533,531,532,534,536,535,537,535,536,538,537,536,539,538,536,540,538,539,541,540,539,542,541,539,543,542,539,535,544,534,544,545,534,534,545,546,547,546,545,548,545,544,549,548,544,541,548,549,540,541,549,550,552,551,553,551,552,554,556,555,557,555,556,558,560,559,561,559,560,562,564,563,565,563,564,566,568,567,569,567,568,570,572,571,573,571,572,574,576,575,577,575,576,578,580,579,581,579,580,582,584,583,585,583,584,586,588,587,589,587,588,590,589,588,591,589,590,592,591,590,593,592,590,594,592,593,595,592,594,596,592,595,597,592,596,598,592,597,599,592,598,600,592,599,601,592,600,602,592,601,603,592,602,604,606,605,607,605,606,608,607,606,609,607,608,610,607,609,611,607,610,612,607,611,613,607,612,614,607,613,615,607,614,616,607,615,617,607,616,586,607,617,587,607,586,618,620,619,621,619,620,622,621,620,623,622,620,624,623,620,625,624,620,626,625,620,627,626,620,628,627,620,629,628,620,630,629,620,631,630,620,632,631,620,633,632,620,621,634,619,635,619,634,636,638,637,639,637,638,640,642,641,643,641,642,644,646,645,647,645,646,648,650,649,651,649,650,652,654,653,655,653,654,656,658,657,659,657,658,660,662,661,663,661,662,664,666,665,667,665,666,668,670,669,671,669,670,672,674,673,675,673,674,676,678,677,679,677,678,680,682,681,683,681,682,684,686,685,687,685,686,688,690,689,691,689,690,692,694,693,695,693,694,696,698,697,699,697,698,700,702,701,703,701,702,704,706,705,707,705,706,708,710,709,711,709,710,712,714,713,715,713,714,716,718,717,719,717,718,720,722,721,723,721,722,724,726,725,727,729,728,730,732,731,733,735,734,736,738,737,739,741,740
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *408 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 11274, "Material::stone", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5843138,0.6862745,0.7568628
			P: "DiffuseColor", "Color", "", "A",0.5843138,0.6862745,0.7568628
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 16426, "Material::textile", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8196079,0.7529412,0.6705883
			P: "DiffuseColor", "Color", "", "A",0.8196079,0.7529412,0.6705883
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 10732, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh sword_scimitar, Model::RootNode
	C: "OO",4852091248792892883,0

	;Geometry::, Model::Mesh sword_scimitar
	C: "OO",4814144075667229461,4852091248792892883

	;Material::stone, Model::Mesh sword_scimitar
	C: "OO",11274,4852091248792892883

	;Material::textile, Model::Mesh sword_scimitar
	C: "OO",16426,4852091248792892883

	;Material::wood, Model::Mesh sword_scimitar
	C: "OO",19362,4852091248792892883

	;Material::_defaultMat, Model::Mesh sword_scimitar
	C: "OO",10732,4852091248792892883

}
