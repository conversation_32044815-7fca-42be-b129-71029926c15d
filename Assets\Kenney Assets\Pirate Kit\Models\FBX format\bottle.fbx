; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 280
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/bottle.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/bottle.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5707257312424549967, "Model::bottle", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-38.38158,7.219114E-15,-64.08734
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",0.71,0.71,0.71
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5713575312939446887, "Geometry::", "Mesh" {
		Vertices: *792 {
			a: 0.625,3.590141,-1.225,0.625,2.887324,-1.225,1.225,3.590141,-0.625,1.225,2.887324,-0.625,-1.225,2.887324,-0.625,-0.625,2.887324,-1.225,-1.225,3.590141,-0.625,-0.625,3.590141,-1.225,-0.625,2.887324,1.225,-1.225,2.887324,0.625,-0.625,3.590141,1.225,-1.225,3.590141,0.625,1.225,3.590141,0.625,1.225,2.887324,0.625,0.625,3.590141,1.225,0.625,2.887324,1.225,-0.2242825,4.267606,-0.4395937,0.2242825,4.267606,-0.4395937,-0.2242825,5.34014,-0.4395937,0.2242825,5.34014,-0.4395937,-0.2242825,4.267606,0.4395937,-0.4395937,4.267606,0.2242825,-0.2242825,5.34014,0.4395937,-0.4395937,5.34014,0.2242825,0.2242825,4.267606,-0.4395937,0.4395937,4.267606,-0.2242825,0.2242825,5.34014,-0.4395937,0.4395937,5.34014,-0.2242825,-0.4395937,4.267606,-0.2242825,-0.2242825,4.267606,-0.4395937,-0.4395937,5.34014,-0.2242825,-0.2242825,5.34014,-0.4395937,0.4395937,4.267606,0.2242825,0.2242825,4.267606,0.4395937,0.4395937,5.34014,0.2242825,0.2242825,5.34014,0.4395937,0.4395937,5.34014,-0.2242825,0.4395937,4.267606,-0.2242825,0.4395937,5.34014,0.2242825,0.4395937,4.267606,0.2242825,0.2242825,4.267606,0.4395937,-0.2242825,4.267606,0.4395937,0.2242825,5.34014,0.4395937,-0.2242825,5.34014,0.4395937,-0.4395937,4.267606,-0.2242825,-0.4395937,5.34014,-0.2242825,-0.4395937,4.267606,0.2242825,-0.4395937,5.34014,0.2242825,1.225,3.590141,-0.625,0.4395937,4.267606,-0.2242825,0.625,3.590141,-1.225,0.2242825,4.267606,-0.4395937,-0.2242825,4.267606,0.4395937,-0.625,3.590141,1.225,-0.4395937,4.267606,0.2242825,-1.225,3.590141,0.625,0.625,3.590141,1.225,-0.625,3.590141,1.225,0.2242825,4.267606,0.4395937,-0.2242825,4.267606,0.4395937,0.625,3.590141,-1.225,0.2242825,4.267606,-0.4395937,-0.625,3.590141,-1.225,-0.2242825,4.267606,-0.4395937,-0.4395937,4.267606,-0.2242825,-0.4395937,4.267606,0.2242825,-1.225,3.590141,-0.625,-1.225,3.590141,0.625,1.225,3.590141,0.625,0.625,3.590141,1.225,0.4395937,4.267606,0.2242825,0.2242825,4.267606,0.4395937,-0.2242825,4.267606,-0.4395937,-0.4395937,4.267606,-0.2242825,-0.625,3.590141,-1.225,-1.225,3.590141,-0.625,1.225,3.590141,-0.625,1.225,3.590141,0.625,0.4395937,4.267606,-0.2242825,0.4395937,4.267606,0.2242825,-0.2803531,5.590141,-0.5494921,0.2803531,5.590141,-0.5494921,-0.2803531,5.940141,-0.5494921,0.2803531,5.940141,-0.5494921,0.2242825,5.34014,-0.4395937,0.4395937,5.34014,-0.2242825,0.2803531,5.590141,-0.5494921,0.5494921,5.590141,-0.2803531,0.5494921,5.940141,-0.2803531,0.5494921,5.940141,0.2803531,0.2803531,5.940141,-0.5494921,0.2803531,5.940141,0.5494921,-0.2803531,5.940141,-0.5494921,-0.2803531,5.940141,0.5494921,-0.5494921,5.940141,-0.2803531,-0.5494921,5.940141,0.2803531,-0.4395937,5.34014,-0.2242825,-0.2242825,5.34014,-0.4395937,-0.5494921,5.590141,-0.2803531,-0.2803531,5.590141,-0.5494921,0.5494921,5.590141,-0.2803531,0.4395937,5.34014,-0.2242825,0.5494921,5.590141,0.2803531,0.4395937,5.34014,0.2242825,-0.2242825,5.34014,-0.4395937,0.2242825,5.34014,-0.4395937,-0.2803531,5.590141,-0.5494921,0.2803531,5.590141,-0.5494921,0.5494921,5.590141,0.2803531,0.2803531,5.590141,0.5494921,0.5494921,5.940141,0.2803531,0.2803531,5.940141,0.5494921,0.5494921,5.940141,-0.2803531,0.5494921,5.590141,-0.2803531,0.5494921,5.940141,0.2803531,0.5494921,5.590141,0.2803531,-0.5494921,5.590141,-0.2803531,-0.2803531,5.590141,-0.5494921,-0.5494921,5.940141,-0.2803531,-0.2803531,5.940141,-0.5494921,-0.2803531,5.590141,0.5494921,-0.5494921,5.590141,0.2803531,-0.2803531,5.940141,0.5494921,-0.5494921,5.940141,0.2803531,0.2803531,5.590141,0.5494921,-0.2803531,5.590141,0.5494921,0.2803531,5.940141,0.5494921,-0.2803531,5.940141,0.5494921,0.2803531,5.590141,-0.5494921,0.5494921,5.590141,-0.2803531,0.2803531,5.940141,-0.5494921,0.5494921,5.940141,-0.2803531,-0.5494921,5.590141,-0.2803531,-0.5494921,5.940141,-0.2803531,-0.5494921,5.590141,0.2803531,-0.5494921,5.940141,0.2803531,-0.2242825,5.34014,0.4395937,-0.4395937,5.34014,0.2242825,-0.2803531,5.590141,0.5494921,-0.5494921,5.590141,0.2803531,-0.5494921,5.590141,-0.2803531,-0.5494921,5.590141,0.2803531,-0.4395937,5.34014,-0.2242825,-0.4395937,5.34014,0.2242825,-0.625,2.887324,-1.225,0.625,2.887324,-1.225,-0.625,3.590141,-1.225,0.625,3.590141,-1.225,1.225,1.127987E-16,-0.625,1.225,1.127987E-16,0.625,0.625,1.127987E-16,-1.225,0.625,1.127987E-16,1.225,-0.625,-1.255084E-16,-1.225,-0.625,3.511057E-16,1.225,-1.225,-1.255084E-16,-0.625,-1.225,3.511057E-16,0.625,0.2242825,5.34014,0.4395937,-0.2242825,5.34014,0.4395937,0.2803531,5.590141,0.5494921,-0.2803531,5.590141,0.5494921,0.4395937,5.34014,0.2242825,0.2242825,5.34014,0.4395937,0.5494921,5.590141,0.2803531,0.2803531,5.590141,0.5494921,-1.225,2.887324,-0.625,-1.225,3.590141,-0.625,-1.225,2.887324,0.625,-1.225,3.590141,0.625,1.225,3.590141,-0.625,1.225,2.887324,-0.625,1.225,3.590141,0.625,1.225,2.887324,0.625,0.625,2.887324,1.225,-0.625,2.887324,1.225,0.625,3.590141,1.225,-0.625,3.590141,1.225,-0.625,-1.255084E-16,-1.225,0.625,1.127987E-16,-1.225,-0.625,0.7746479,-1.225,0.625,0.7746479,-1.225,-1.225,-1.255084E-16,-0.625,-0.625,-1.255084E-16,-1.225,-1.225,0.7746479,-0.625,-0.625,0.7746479,-1.225,-1.225,-1.255084E-16,-0.625,-1.225,0.7746479,-0.625,-1.225,3.511057E-16,0.625,-1.225,0.7746479,0.625,-0.625,3.511057E-16,1.225,-1.225,3.511057E-16,0.625,-0.625,0.7746479,1.225,-1.225,0.7746479,0.625,0.625,1.127987E-16,1.225,-0.625,3.511057E-16,1.225,0.625,0.7746479,1.225,-0.625,0.7746479,1.225,1.225,0.7746479,0.625,1.225,1.127987E-16,0.625,0.625,0.7746479,1.225,0.625,1.127987E-16,1.225,1.225,0.7746479,-0.625,1.225,1.127987E-16,-0.625,1.225,0.7746479,0.625,1.225,1.127987E-16,0.625,0.625,0.7746479,-1.225,0.625,1.127987E-16,-1.225,1.225,0.7746479,-0.625,1.225,1.127987E-16,-0.625,0.625,1.127987E-16,-1.225,1.225,1.127987E-16,0.625,1.225,1.127987E-16,-0.625,0.625,1.127987E-16,1.225,-0.625,-1.255084E-16,-1.225,-0.625,3.511057E-16,1.225,-1.225,-1.255084E-16,-0.625,-1.225,3.511057E-16,0.625,-0.625,2.887324,-1.225,0.625,0.7746479,-1.225,-0.625,0.7746479,-1.225,0.625,2.887324,-1.225,0.625,1.127987E-16,-1.225,-0.625,-1.255084E-16,-1.225,1.225,2.887324,-0.625,0.625,0.7746479,-1.225,0.625,2.887324,-1.225,1.225,0.7746479,-0.625,0.625,1.127987E-16,-1.225,1.225,1.127987E-16,-0.625,1.225,2.887324,0.625,1.225,0.7746479,-0.625,1.225,2.887324,-0.625,1.225,0.7746479,0.625,1.225,1.127987E-16,-0.625,1.225,1.127987E-16,0.625,0.625,2.887324,1.225,1.225,0.7746479,0.625,1.225,2.887324,0.625,0.625,0.7746479,1.225,1.225,1.127987E-16,0.625,0.625,1.127987E-16,1.225,0.625,2.887324,1.225,-0.625,0.7746479,1.225,0.625,0.7746479,1.225,-0.625,2.887324,1.225,-0.625,3.511057E-16,1.225,0.625,1.127987E-16,1.225,-0.625,2.887324,1.225,-1.225,0.7746479,0.625,-0.625,0.7746479,1.225,-1.225,2.887324,0.625,-1.225,3.511057E-16,0.625,-0.625,3.511057E-16,1.225,-1.225,0.7746479,0.625,-1.225,2.887324,-0.625,-1.225,0.7746479,-0.625,-1.225,2.887324,0.625,-1.225,3.511057E-16,0.625,-1.225,-1.255084E-16,-0.625,-1.225,2.887324,-0.625,-0.625,0.7746479,-1.225,-1.225,0.7746479,-0.625,-0.625,2.887324,-1.225,-0.625,-1.255084E-16,-1.225,-1.225,-1.255084E-16,-0.625
		} 
		PolygonVertexIndex: *486 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,91,-91,93,91,-93,94,93,-93,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,151,-151,153,151,-153,154,153,-153,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,210,-210,209,211,-209,211,212,-209,211,213,-213,213,214,-213,213,215,-215,216,218,-218,217,219,-217,220,217,-219,218,221,-221,222,224,-224,223,225,-223,225,223,-227,226,227,-226,228,230,-230,229,231,-229,231,229,-233,232,233,-232,234,236,-236,235,237,-235,237,235,-239,238,239,-238,240,242,-242,241,243,-241,244,241,-243,242,245,-245,246,248,-248,247,249,-247,250,247,-249,248,251,-251,252,254,-254,253,255,-253,254,252,-257,256,257,-255,258,260,-260,259,261,-259,262,259,-261,260,263,-263,178,144,-180,145,179,-145,1,3,-205,206,204,-4,169,171,-201,202,200,-172,13,15,-197,198,196,-16,194,172,-196,173,195,-173,190,8,-192,9,191,-9,185,187,-165,166,164,-188,182,4,-184,5,183,-5
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1458 {
				a: 0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.4443173,0.777923,-0.4443173,0.4443173,0.777923,-0.4443173,0.4443173,0.777923,-0.4443173,0.4443173,0.777923,-0.4443173,0.4443173,0.777923,-0.4443173,0.4443173,0.777923,-0.4443173,-0.4443173,0.777923,0.4443173,-0.4443173,0.777923,0.4443173,-0.4443173,0.777923,0.4443173,-0.4443173,0.777923,0.4443173,-0.4443173,0.777923,0.4443173,-0.4443173,0.777923,0.4443173,0,0.7572237,0.6531555,0,0.7572237,0.6531555,0,0.7572237,0.6531555,0,0.7572237,0.6531555,0,0.7572237,0.6531555,0,0.7572237,0.6531555,0,0.7572237,-0.6531555,0,0.7572237,-0.6531555,0,0.7572237,-0.6531555,0,0.7572237,-0.6531555,0,0.7572237,-0.6531555,0,0.7572237,-0.6531555,-0.6531555,0.7572237,0,-0.6531555,0.7572237,0,-0.6531555,0.7572237,0,-0.6531555,0.7572237,0,-0.6531555,0.7572237,0,-0.6531555,0.7572237,0,0.4443173,0.777923,0.4443173,0.4443173,0.777923,0.4443173,0.4443173,0.777923,0.4443173,0.4443173,0.777923,0.4443173,0.4443173,0.777923,0.4443173,0.4443173,0.777923,0.4443173,-0.4443173,0.777923,-0.4443173,-0.4443173,0.777923,-0.4443173,-0.4443173,0.777923,-0.4443173,-0.4443173,0.777923,-0.4443173,-0.4443173,0.777923,-0.4443173,-0.4443173,0.777923,-0.4443173,0.6531555,0.7572237,0,0.6531555,0.7572237,0,0.6531555,0.7572237,0,0.6531555,0.7572237,0,0.6531555,0.7572237,0,0.6531555,0.7572237,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0.6400884,-0.4249395,-0.6400884,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,-0.6400884,-0.4249395,-0.6400884,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0.9154521,-0.402427,0,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0,-0.402427,-0.9154521,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.6400884,-0.4249395,0.6400884,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,-0.9154521,-0.402427,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0,-0.402427,0.9154521,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,0.6400884,-0.4249395,0.6400884,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *528 {
				a: 3.340662,28.26883,3.340662,22.73483,-3.340662,28.26883,-3.340662,22.73483,3.340662,22.73483,-3.340662,22.73483,3.340662,28.26883,-3.340662,28.26883,3.340662,22.73483,-3.340662,22.73483,3.340662,28.26883,-3.340662,28.26883,3.340662,28.26883,3.340662,22.73483,-3.340662,28.26883,-3.340662,22.73483,1.766004,33.6032,-1.766004,33.6032,1.766004,42.04835,-1.766004,42.04835,1.198803,33.6032,-1.198803,33.6032,1.198803,42.04835,-1.198803,42.04835,1.198803,33.6032,-1.198803,33.6032,1.198803,42.04835,-1.198803,42.04835,1.198803,33.6032,-1.198803,33.6032,1.198803,42.04835,-1.198803,42.04835,1.198803,33.6032,-1.198803,33.6032,1.198803,42.04835,-1.198803,42.04835,1.766004,42.04835,1.766004,33.6032,-1.766004,42.04835,-1.766004,33.6032,1.766004,33.6032,-1.766004,33.6032,1.766004,42.04835,-1.766004,42.04835,-1.766004,33.6032,-1.766004,42.04835,1.766004,33.6032,1.766004,42.04835,-3.340662,9.750092,-1.198803,18.23945,3.340662,9.750092,1.198803,18.23945,1.198803,18.23945,3.340662,9.750092,-1.198803,18.23945,-3.340662,9.750092,4.92126,11.16001,-4.92126,11.16001,1.766004,19.32708,-1.766004,19.32708,-4.92126,11.16001,-1.766004,19.32708,4.92126,11.16001,1.766004,19.32708,-1.766004,19.32708,1.766004,19.32708,-4.92126,11.16001,4.92126,11.16001,3.340662,9.750092,-3.340662,9.750092,1.198803,18.23945,-1.198803,18.23945,-1.198803,18.23945,1.198803,18.23945,-3.340662,9.750092,3.340662,9.750092,4.92126,11.16001,-4.92126,11.16001,1.766004,19.32708,-1.766004,19.32708,2.207505,44.01686,-2.207505,44.01686,2.207505,46.77276,-2.207505,46.77276,1.198803,39.63379,-1.198803,39.63379,1.498504,41.8084,-1.498504,41.8084,-4.32671,-2.207505,-4.32671,2.207505,-2.207505,-4.32671,-2.207505,4.32671,2.207505,-4.32671,2.207505,4.32671,4.32671,-2.207505,4.32671,2.207505,1.198803,39.63379,-1.198803,39.63379,1.498504,41.8084,-1.498504,41.8084,2.207505,42.03651,1.766004,39.8862,-2.207505,42.03651,-1.766004,39.8862,1.766004,39.8862,-1.766004,39.8862,2.207505,42.03651,-2.207505,42.03651,1.498504,44.01686,-1.498504,44.01686,1.498504,46.77276,-1.498504,46.77276,2.207505,46.77276,2.207505,44.01686,-2.207505,46.77276,-2.207505,44.01686,1.498504,44.01686,-1.498504,44.01686,1.498504,46.77276,-1.498504,46.77276,1.498504,44.01686,-1.498504,44.01686,1.498504,46.77276,-1.498504,46.77276,2.207505,44.01686,-2.207505,44.01686,2.207505,46.77276,-2.207505,46.77276,1.498504,44.01686,-1.498504,44.01686,1.498504,46.77276,-1.498504,46.77276,-2.207505,44.01686,-2.207505,46.77276,2.207505,44.01686,2.207505,46.77276,1.198803,39.63379,-1.198803,39.63379,1.498504,41.8084,-1.498504,41.8084,-2.207505,42.03651,2.207505,42.03651,-1.766004,39.8862,1.766004,39.8862,4.92126,22.73483,-4.92126,22.73483,4.92126,28.26883,-4.92126,28.26883,-9.645669,-4.92126,-9.645669,4.92126,-4.92126,-9.645669,-4.92126,9.645669,4.92126,-9.645669,4.92126,9.645669,9.645669,-4.92126,9.645669,4.92126,1.766004,39.8862,-1.766004,39.8862,2.207505,42.03651,-2.207505,42.03651,1.198803,39.63379,-1.198803,39.63379,1.498504,41.8084,-1.498504,41.8084,-4.92126,22.73483,-4.92126,28.26883,4.92126,22.73483,4.92126,28.26883,4.92126,28.26883,4.92126,22.73483,-4.92126,28.26883,-4.92126,22.73483,4.92126,22.73483,-4.92126,22.73483,4.92126,28.26883,-4.92126,28.26883,4.92126,-9.882548E-16,-4.92126,8.881784E-16,4.92126,6.09959,-4.92126,6.09959,3.340662,-9.882548E-16,-3.340662,-9.882548E-16,3.340662,6.09959,-3.340662,6.09959,-4.92126,-9.882548E-16,-4.92126,6.09959,4.92126,2.764612E-15,4.92126,6.09959,3.340662,2.764612E-15,-3.340662,2.764612E-15,3.340662,6.09959,-3.340662,6.09959,4.92126,8.881784E-16,-4.92126,2.764612E-15,4.92126,6.09959,-4.92126,6.09959,3.340662,6.09959,3.340662,8.881784E-16,-3.340662,6.09959,-3.340662,8.881784E-16,4.92126,6.09959,4.92126,8.881784E-16,-4.92126,6.09959,-4.92126,8.881784E-16,3.340662,6.09959,3.340662,7.157176E-16,-3.340662,6.09959,-3.340662,7.157176E-16,-4.92126,-9.645669,-9.645669,4.92126,-9.645669,-4.92126,-4.92126,9.645669,4.92126,-9.645669,4.92126,9.645669,9.645669,-4.92126,9.645669,4.92126,4.92126,22.73483,-4.92126,6.09959,4.92126,6.09959,-4.92126,22.73483,-4.92126,8.881784E-16,4.92126,-9.882548E-16,-3.340662,22.73483,3.340662,6.09959,3.340662,22.73483,-3.340662,6.09959,3.340662,7.157176E-16,-3.340662,7.157176E-16,-4.92126,22.73483,4.92126,6.09959,4.92126,22.73483,-4.92126,6.09959,4.92126,8.881784E-16,-4.92126,8.881784E-16,-3.340662,22.73483,3.340662,6.09959,3.340662,22.73483,-3.340662,6.09959,3.340662,8.881784E-16,-3.340662,8.881784E-16,4.92126,22.73483,-4.92126,6.09959,4.92126,6.09959,-4.92126,22.73483,-4.92126,2.764612E-15,4.92126,8.881784E-16,3.340662,22.73483,-3.340662,6.09959,3.340662,6.09959,-3.340662,22.73483,-3.340662,2.764612E-15,3.340662,2.764612E-15,4.92126,6.09959,-4.92126,22.73483,-4.92126,6.09959,4.92126,22.73483,4.92126,2.764612E-15,-4.92126,-9.882548E-16,3.340662,22.73483,-3.340662,6.09959,3.340662,6.09959,-3.340662,22.73483,-3.340662,-9.882548E-16,3.340662,-9.882548E-16
				}
			UVIndex: *486 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,91,90,93,91,92,94,93,92,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,151,150,153,151,152,154,153,152,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166,168,170,169,171,169,170,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182,184,186,185,187,185,186,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202,204,206,205,207,205,206,208,210,209,209,211,208,211,212,208,211,213,212,213,214,212,213,215,214,216,218,217,217,219,216,220,217,218,218,221,220,222,224,223,223,225,222,225,223,226,226,227,225,228,230,229,229,231,228,231,229,232,232,233,231,234,236,235,235,237,234,237,235,238,238,239,237,240,242,241,241,243,240,244,241,242,242,245,244,246,248,247,247,249,246,250,247,248,248,251,250,252,254,253,253,255,252,254,252,256,256,257,254,258,260,259,259,261,258,262,259,260,260,263,262,178,144,179,145,179,144,1,3,204,206,204,3,169,171,200,202,200,171,13,15,196,198,196,15,194,172,195,173,195,172,190,8,191,9,191,8,185,187,164,166,164,187,182,4,183,5,183,4
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *162 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 23412, "Material::leaves", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.227451,0.5568628,0.3215686
			P: "DiffuseColor", "Color", "", "A",0.227451,0.5568628,0.3215686
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 16426, "Material::textile", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8196079,0.7529412,0.6705883
			P: "DiffuseColor", "Color", "", "A",0.8196079,0.7529412,0.6705883
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh bottle, Model::RootNode
	C: "OO",5707257312424549967,0

	;Geometry::, Model::Mesh bottle
	C: "OO",5713575312939446887,5707257312424549967

	;Material::leaves, Model::Mesh bottle
	C: "OO",23412,5707257312424549967

	;Material::wood, Model::Mesh bottle
	C: "OO",19362,5707257312424549967

	;Material::textile, Model::Mesh bottle
	C: "OO",16426,5707257312424549967

}
