%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1677285531, guid: 7f4c70b32b599d7499d469724322ee36, type: 3}
  m_Name: StarkBuilderSetting
  m_EditorClassIdentifier: 
  tag: 0
  wasmResourceUrl: http://
  webGLOutputDir: E:/ProjectsUnity/Lianliankan3D-fengling-douyin/Release/dygame
  useByteAudioAPI: 0
  wasmMemorySize: 256
  isWebGL2: 0
  needCompress: 1
  symbolMode: 1
  profiling: 0
  clearStreamingAssets: 1
  buildOptions: 0
  wasmSubFramework: 0
  optimizeWebGLMemoryInBackground: 1
  assetBundleFSEnabled: 1
  assetBundleBufferCapacity: 128
  assetBundleBufferTTL: 5
  preloadFiles: 
  CDN: 
  preloadDataListUrl: 
  iOSDevicePixelRatio: 0
  urlCacheList:
  - xuan-cdn.feigo.fun
  - *************
  dontCacheFileNames:
  - .version
  isDevBuild: 0
  stripEngineCode: 0
  apkFileNameBase: 
  apkOutputDir: 
  appHost: 2
  compressMethod: 1
  runtimeEnv: 0
  framework: 1
  architecture: 2
  miniApkVersion: 0
  scriptingBackend: 0
  NativeWhiteListRegex:
  - com.bytedance.starksdk
  - _Backup~
  _appId: tt30d2466fafe3e0c907
  version: 
  autoVersion: 0
  _webglPackagePath: E:\ProjectsUnity\Lianliankan3D-fengling-douyin\Release\dygame\webgl_package-20250508_201646.zip
  _orientation: 0
  _iOSPerformancePlus: 0
  _menuButtonStyle: 0
  _publishType: 0
  publishDesc: 
  idePath: "C:/Users/<USER>/AppData/Local/Programs/@bytedminiprogram-ide/\u6296\u97F3\u5F00\u53D1\u8005\u5DE5\u5177.exe"
