; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 38
		Millisecond: 405
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/sword.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/sword.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5312475311935442037, "Model::sword", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-26.6292,0.6760442,-85.76152
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: **********907629477, "Geometry::", "Mesh" {
		Vertices: *1104 {
			a: 0.3945825,0.5528638,-0.1997739,0.3945825,0.5641106,-0.2802459,0.3481219,0.5641106,-0.2802459,0.3945825,0.9017022,-0.366241,0.3945825,0.873315,-0.2452273,0.0537793,0.9017022,-0.366241,0.1134407,0.873315,-0.2452273,0.3945825,0.5965515,-0.4185405,0.3945825,0.6417317,-0.5546517,0.2682775,0.5965515,-0.4185405,0.254961,0.6042076,-0.4416054,-0.3945825,0.6417317,-0.5546517,-0.254961,0.6042076,-0.4416054,-0.2682775,0.5965515,-0.4185405,-0.3945825,0.5965515,-0.4185405,0.3945825,0.8562232,-0.1229343,0.3945825,0.850516,-1.443823E-14,0.1741301,0.8562232,-0.1229343,0.2355311,0.850516,-1.443823E-14,-0.1741301,0.8562232,-0.1229343,-0.2355311,0.850516,-7.219114E-15,-0.3945825,0.8562232,-0.1229343,-0.3945825,0.850516,-7.219114E-15,-0.1741301,0.8562232,0.1229343,-0.3945825,0.8562232,0.1229343,-0.2355311,0.850516,-7.219114E-15,-0.3945825,0.850516,-7.219114E-15,-0.1134407,0.873315,0.2452273,-0.3945825,0.873315,0.2452273,-0.1741301,0.8562232,0.1229343,-0.3945825,0.8562232,0.1229343,0.3945825,0.873315,-0.2452273,0.3945825,0.8562232,-0.1229343,0.1134407,0.873315,-0.2452273,0.1741301,0.8562232,-0.1229343,-0.0537793,0.9017022,-0.366241,-0.1134407,0.873315,-0.2452273,-0.3945825,0.9017022,-0.366241,-0.3945825,0.873315,-0.2452273,-0.2682775,0.5965515,-0.4185405,-0.3945825,0.5965515,-0.4185405,-0.3481219,0.5641106,-0.2802459,-0.3945825,0.5641106,-0.2802459,-0.3945825,0.5528638,-0.1997739,-0.3481219,0.5641106,-0.2802459,-0.3945825,0.5641106,-0.2802459,0.3945825,0.9017022,-0.366241,1.718149E-12,0.9381574,-0.4760672,0.3945825,0.9412369,-0.4853442,-0.3945825,0.9412369,-0.4853442,0.0537793,0.9017022,-0.366241,-0.0537793,0.9017022,-0.366241,-0.3945825,0.9017022,-0.366241,0.3945825,0.5641106,-0.2802459,0.3945825,0.5965515,-0.4185405,0.3481219,0.5641106,-0.2802459,0.2682775,0.5965515,-0.4185405,0.3945825,0.5965515,0.4185405,0.3945825,0.5641106,0.2802459,0.2682775,0.5965515,0.4185405,0.3481219,0.5641106,0.2802459,0.3945825,0.6417317,0.5546517,0.3945825,0.5965515,0.4185405,-0.3945825,0.6417317,0.5546517,0.254961,0.6042076,0.4416054,0.2682775,0.5965515,0.4185405,-0.254961,0.6042076,0.4416054,-0.2682775,0.5965515,0.4185405,-0.3945825,0.5965515,0.4185405,0.3945825,0.8562232,0.1229343,0.3945825,0.873315,0.2452273,0.1741301,0.8562232,0.1229343,0.1134407,0.873315,0.2452273,0.1988039,-0.6760441,0.3443384,-0.1988039,-0.6760441,0.3443384,0.1988039,-0.507419,0.3443384,-0.1988039,-0.507419,0.3443384,0.3945825,0.7649353,-0.8094,0.3945825,1.052866,-0.715347,-0.3945825,0.7649353,-0.8094,-0.3945825,1.052866,-0.715347,0.3945825,0.6417317,-0.5546517,0.3945825,0.6994153,-0.6878695,-0.3945825,0.6417317,-0.5546517,-0.3945825,0.6994153,-0.6878695,0.254961,0.4511129,0.4416054,-0.254961,0.4511129,0.4416054,0.254961,0.6042076,0.4416054,-0.254961,0.6042076,0.4416054,0.3976078,-0.507419,-1.443823E-14,0.3976078,-0.6760441,-1.443823E-14,0.1988039,-0.507419,0.3443384,0.1988039,-0.6760441,0.3443384,0.3945825,0.4511129,0.1997739,0.3945825,0.4511129,-0.1997739,0.254961,0.4511129,0.4416054,0.3410108,0.4511129,-1.443823E-14,0.1705054,0.4511129,0.2953241,-0.254961,0.4511129,0.4416054,-0.1705054,0.4511129,0.2953241,-0.3410108,0.4511129,-7.219114E-15,-0.3945825,0.4511129,0.1997739,0.254961,0.4511129,-0.4416054,0.1705054,0.4511129,-0.2953241,-0.254961,0.4511129,-0.4416054,-0.1705054,0.4511129,-0.2953241,-0.3945825,0.4511129,-0.1997739,0.3945825,0.5641106,0.2802459,0.3945825,0.5528638,0.1997739,0.3481219,0.5641106,0.2802459,0.3945825,0.9917125,0.6019156,0.3945825,1.052866,0.715347,-0.3945825,0.9917125,0.6019156,-0.3945825,1.052866,0.715347,0.3945825,1.052866,-0.715347,0.3945825,0.9917125,-0.6019156,-0.3945825,1.052866,-0.715347,-0.3945825,0.9917125,-0.6019156,0.3945825,0.7649353,0.8094,0.3945825,0.7142291,0.715347,-0.3945825,0.7649353,0.8094,0.3945825,0.6994153,0.6878695,-0.3945825,0.6994153,0.6878695,-0.3945825,0.7142291,0.715347,0.3945825,0.9412369,0.4853442,0.3945825,0.9917125,0.6019156,-0.3945825,0.9412369,0.4853442,-0.3945825,0.9917125,0.6019156,0.3945825,0.6994153,-0.6878695,0.3945825,0.7142291,-0.715347,-0.3945825,0.6994153,-0.6878695,0.3945825,0.7649353,-0.8094,-0.3945825,0.7649353,-0.8094,-0.3945825,0.7142291,-0.715347,-0.3945825,0.5641106,0.2802459,-0.3481219,0.5641106,0.2802459,-0.3945825,0.5528638,0.1997739,-0.254961,0.4511129,-0.4416054,-0.254961,0.6042076,-0.4416054,-0.3945825,0.4511129,-0.1997739,-0.2682775,0.5965515,-0.4185405,-0.3481219,0.5641106,-0.2802459,-0.3945825,0.5528638,-0.1997739,0.3945825,0.7649353,-0.8094,0.3945825,0.7142291,-0.715347,0.3945825,1.052866,-0.715347,0.3945825,0.9917125,-0.6019156,0.3945825,0.6994153,-0.6878695,0.3945825,0.6417317,-0.5546517,0.3945825,0.9412369,-0.4853442,0.3945825,0.5965515,-0.4185405,0.3945825,0.9017022,-0.366241,0.3945825,0.5641106,-0.2802459,0.3945825,0.873315,-0.2452273,0.3945825,0.5528638,-0.1997739,0.3945825,0.8562232,-0.1229343,0.3945825,0.4511129,-0.1997739,0.3945825,0.4511129,0.1997739,0.3945825,0.850516,-1.443823E-14,0.3945825,0.8562232,0.1229343,0.3945825,0.5528638,0.1997739,0.3945825,0.873315,0.2452273,0.3945825,0.5641106,0.2802459,0.3945825,0.9017022,0.366241,0.3945825,0.5965515,0.4185405,0.3945825,0.9412369,0.4853442,0.3945825,0.6417317,0.5546517,0.3945825,0.9917125,0.6019156,0.3945825,0.6994153,0.6878695,0.3945825,1.052866,0.715347,0.3945825,0.7142291,0.715347,0.3945825,0.7649353,0.8094,0.3945825,0.6994153,0.6878695,0.3945825,0.6417317,0.5546517,-0.3945825,0.6994153,0.6878695,-0.3945825,0.6417317,0.5546517,0.3945825,0.7649353,0.8094,-0.3945825,0.7649353,0.8094,0.3945825,1.052866,0.715347,-0.3945825,1.052866,0.715347,0.3945825,0.850516,-1.443823E-14,0.3945825,0.8562232,0.1229343,0.2355311,0.850516,-1.443823E-14,0.1741301,0.8562232,0.1229343,-0.3945825,0.4511129,-0.1997739,-0.3945825,0.5528638,-0.1997739,-0.3945825,0.4511129,0.1997739,-0.3945825,0.8562232,-0.1229343,-0.3945825,0.850516,-7.219114E-15,-0.3945825,0.8562232,0.1229343,-0.3945825,0.5528638,0.1997739,-0.3945825,0.873315,0.2452273,-0.3945825,0.5641106,0.2802459,-0.3945825,0.9017022,0.366241,-0.3945825,0.5965515,0.4185405,-0.3945825,0.9412369,0.4853442,-0.3945825,0.6417317,0.5546517,-0.3945825,0.9917125,0.6019156,-0.3945825,0.6994153,0.6878695,-0.3945825,1.052866,0.715347,-0.3945825,0.7142291,0.715347,-0.3945825,0.7649353,0.8094,-0.3945825,0.873315,-0.2452273,-0.3945825,0.5641106,-0.2802459,-0.3945825,0.9017022,-0.366241,-0.3945825,0.5965515,-0.4185405,-0.3945825,0.9412369,-0.4853442,-0.3945825,0.6417317,-0.5546517,-0.3945825,0.9917125,-0.6019156,-0.3945825,0.6994153,-0.6878695,-0.3945825,1.052866,-0.715347,-0.3945825,0.7142291,-0.715347,-0.3945825,0.7649353,-0.8094,-0.0537793,0.9017022,0.366241,-0.3945825,0.9017022,0.366241,-0.1134407,0.873315,0.2452273,-0.3945825,0.873315,0.2452273,0.3945825,0.9917125,-0.6019156,0.3945825,0.9412369,-0.4853442,-0.3945825,0.9917125,-0.6019156,-0.3945825,0.9412369,-0.4853442,0.3945825,0.873315,0.2452273,0.3945825,0.9017022,0.366241,0.1134407,0.873315,0.2452273,0.0537793,0.9017022,0.366241,-0.1988039,-0.6760441,-0.3443384,-0.1988039,-0.507419,-0.3443384,-0.3976078,-0.6760441,-7.219114E-15,-0.3976078,-0.507419,-7.219114E-15,-0.2682775,0.5965515,0.4185405,-0.3481219,0.5641106,0.2802459,-0.3945825,0.5965515,0.4185405,-0.3945825,0.5641106,0.2802459,0.3945825,0.9017022,0.366241,0.3945825,0.9412369,0.4853442,0.0537793,0.9017022,0.366241,1.718149E-12,0.9381574,0.4760672,-0.3945825,0.9412369,0.4853442,-0.0537793,0.9017022,0.366241,-0.3945825,0.9017022,0.366241,-0.1134407,0.873315,-0.2452273,-0.1741301,0.8562232,-0.1229343,-0.3945825,0.873315,-0.2452273,-0.3945825,0.8562232,-0.1229343,-0.3945825,0.4511129,0.1997739,-0.3945825,0.5528638,0.1997739,-0.254961,0.4511129,0.4416054,-0.3481219,0.5641106,0.2802459,-0.2682775,0.5965515,0.4185405,-0.254961,0.6042076,0.4416054,0.3976078,-0.507419,-1.443823E-14,0.2801161,-0.507419,-1.443823E-14,0.1988039,-0.507419,-0.3443384,0.140058,-0.507419,-0.2425876,-0.1988039,-0.507419,-0.3443384,-0.140058,-0.507419,-0.2425876,-0.2801161,-0.507419,-7.219114E-15,-0.3976078,-0.507419,-7.219114E-15,0.1988039,-0.507419,0.3443384,0.140058,-0.507419,0.2425876,-0.1988039,-0.507419,0.3443384,-0.140058,-0.507419,0.2425876,0.254961,0.4511129,-0.4416054,0.3945825,0.4511129,-0.1997739,0.254961,0.6042076,-0.4416054,0.2682775,0.5965515,-0.4185405,0.3481219,0.5641106,-0.2802459,0.3945825,0.5528638,-0.1997739,0.3945825,0.4511129,0.1997739,0.254961,0.4511129,0.4416054,0.3945825,0.5528638,0.1997739,0.3481219,0.5641106,0.2802459,0.2682775,0.5965515,0.4185405,0.254961,0.6042076,0.4416054,-0.1988039,-0.6760441,-0.3443384,0.1988039,-0.6760441,-0.3443384,-0.1988039,-0.507419,-0.3443384,0.1988039,-0.507419,-0.3443384,-0.3976078,-0.6760441,-7.219114E-15,-0.3976078,-0.507419,-7.219114E-15,-0.1988039,-0.6760441,0.3443384,-0.1988039,-0.507419,0.3443384,-0.254961,0.4511129,-0.4416054,0.254961,0.4511129,-0.4416054,-0.254961,0.6042076,-0.4416054,0.254961,0.6042076,-0.4416054,0.1988039,-0.507419,-0.3443384,0.1988039,-0.6760441,-0.3443384,0.3976078,-0.507419,-1.443823E-14,0.3976078,-0.6760441,-1.443823E-14,5.067818E-12,4.669872,-1.443823E-14,4.288154E-12,3.346166,-0.640775,0.321062,3.346166,-1.443823E-14,0.321062,3.346166,-1.443823E-14,4.288154E-12,3.346166,-0.640775,4.288154E-12,3.346166,0.640775,-0.321062,3.346166,-7.219114E-15,-0.321062,3.346166,-7.219114E-15,5.067818E-12,4.669872,-1.443823E-14,4.288154E-12,3.346166,0.640775,5.067818E-12,4.669872,-1.443823E-14,0.321062,3.346166,-1.443823E-14,4.288154E-12,3.346166,0.640775,4.288154E-12,3.346166,-0.640775,5.067818E-12,4.669872,-1.443823E-14,-0.321062,3.346166,-7.219114E-15,-0.2355311,0.850516,-7.219114E-15,-0.321062,3.346166,-7.219114E-15,-0.1741301,0.8562232,0.1229343,4.288154E-12,3.346166,0.640775,-0.1134407,0.873315,0.2452273,-0.0537793,0.9017022,0.366241,1.718149E-12,0.9381574,0.4760672,0.321062,3.346166,-1.443823E-14,0.2355311,0.850516,-1.443823E-14,4.288154E-12,3.346166,0.640775,0.1741301,0.8562232,0.1229343,0.1134407,0.873315,0.2452273,0.0537793,0.9017022,0.366241,1.718149E-12,0.9381574,0.4760672,4.288154E-12,3.346166,-0.640775,-0.321062,3.346166,-7.219114E-15,1.718149E-12,0.9381574,-0.4760672,-0.0537793,0.9017022,-0.366241,-0.1134407,0.873315,-0.2452273,-0.1741301,0.8562232,-0.1229343,-0.2355311,0.850516,-7.219114E-15,4.288154E-12,3.346166,-0.640775,1.718149E-12,0.9381574,-0.4760672,0.321062,3.346166,-1.443823E-14,0.0537793,0.9017022,-0.366241,0.1134407,0.873315,-0.2452273,0.1741301,0.8562232,-0.1229343,0.2355311,0.850516,-1.443823E-14,0.3410108,0.4511129,-1.443823E-14,0.2801161,-0.507419,-1.443823E-14,0.1705054,0.4511129,0.2953241,0.140058,-0.507419,0.2425876,0.1705054,0.4511129,-0.2953241,0.140058,-0.507419,-0.2425876,0.3410108,0.4511129,-1.443823E-14,0.2801161,-0.507419,-1.443823E-14,0.140058,-0.507419,0.2425876,-0.140058,-0.507419,0.2425876,0.1705054,0.4511129,0.2953241,-0.1705054,0.4511129,0.2953241,-0.1705054,0.4511129,-0.2953241,-0.3410108,0.4511129,-7.219114E-15,-0.140058,-0.507419,-0.2425876,-0.2801161,-0.507419,-7.219114E-15,-0.140058,-0.507419,-0.2425876,0.140058,-0.507419,-0.2425876,-0.1705054,0.4511129,-0.2953241,0.1705054,0.4511129,-0.2953241,-0.2801161,-0.507419,-7.219114E-15,-0.3410108,0.4511129,-7.219114E-15,-0.140058,-0.507419,0.2425876,-0.1705054,0.4511129,0.2953241,0.3976078,-0.6760441,-1.443823E-14,0.1988039,-0.6760441,-0.3443384,0.1988039,-0.6760441,0.3443384,-0.1988039,-0.6760441,0.3443384,0.1988039,-0.6760441,0.3443384,0.1988039,-0.6760441,-0.3443384,-0.1988039,-0.6760441,-0.3443384,-0.1988039,-0.6760441,0.3443384,-0.3976078,-0.6760441,-7.219114E-15,-0.1988039,-0.6760441,-0.3443384
		} 
		PolygonVertexIndex: *708 {
			a: 0,2,-2,3,5,-5,6,4,-6,7,9,-9,10,8,-10,11,8,-11,12,11,-11,13,11,-13,14,11,-14,15,17,-17,18,16,-18,19,21,-21,22,20,-22,23,25,-25,26,24,-26,27,29,-29,30,28,-30,31,33,-33,34,32,-34,35,37,-37,38,36,-38,39,41,-41,42,40,-42,43,45,-45,46,48,-48,49,47,-49,50,46,-48,51,47,-50,52,51,-50,53,55,-55,56,54,-56,57,59,-59,60,58,-60,61,63,-63,64,62,-64,65,62,-65,66,64,-64,67,66,-64,68,67,-64,69,71,-71,72,70,-72,73,75,-75,76,74,-76,77,79,-79,80,78,-80,81,83,-83,84,82,-84,85,87,-87,88,86,-88,89,91,-91,92,90,-92,93,95,-95,96,94,-96,97,96,-96,98,97,-96,99,97,-99,100,99,-99,101,100,-99,94,96,-103,103,102,-97,104,102,-104,105,104,-104,100,104,-106,106,104,-101,101,106,-101,107,109,-109,110,112,-112,113,111,-113,114,116,-116,117,115,-117,118,120,-120,121,119,-121,122,121,-121,123,122,-121,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,131,-131,133,132,-131,134,136,-136,137,139,-139,140,138,-140,141,140,-140,142,141,-140,143,145,-145,146,144,-146,147,144,-147,148,147,-147,149,148,-147,150,148,-150,151,150,-150,152,150,-152,153,152,-152,154,152,-154,155,154,-154,156,154,-156,157,156,-156,158,157,-156,159,157,-159,160,157,-160,161,160,-160,162,160,-162,163,162,-162,164,162,-164,165,164,-164,166,164,-166,167,166,-166,168,166,-168,169,168,-168,170,168,-170,171,170,-170,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,187,-187,189,188,-187,190,189,-187,191,189,-191,192,191,-191,193,191,-193,194,193,-193,195,193,-195,196,195,-195,197,195,-197,198,197,-197,199,197,-199,200,199,-199,201,199,-201,187,202,-186,185,202,-204,202,204,-204,203,204,-206,204,206,-206,205,206,-208,206,208,-208,207,208,-210,208,210,-210,209,210,-212,212,211,-211,213,215,-215,216,214,-216,217,219,-219,220,218,-220,221,223,-223,224,222,-224,225,227,-227,228,226,-228,229,231,-231,232,230,-232,233,235,-235,236,234,-236,237,234,-237,238,237,-237,239,237,-239,240,242,-242,243,241,-243,244,246,-246,247,245,-247,248,247,-247,249,248,-247,250,252,-252,253,251,-253,254,253,-253,255,253,-255,256,255,-255,257,256,-255,250,251,-259,259,258,-252,260,258,-260,257,260,-257,261,260,-260,256,260,-262,262,264,-264,265,263,-265,266,263,-266,267,263,-267,268,270,-270,271,269,-271,272,269,-272,273,269,-273,274,276,-276,277,275,-277,278,280,-280,281,279,-281,282,284,-284,285,283,-285,286,288,-288,289,287,-289,290,292,-292,293,295,-295,296,294,-296,297,299,-299,300,302,-302,303,305,-305,306,308,-308,309,307,-309,310,309,-309,311,309,-311,312,309,-312,313,315,-315,316,314,-316,317,316,-316,318,317,-316,319,318,-316,320,322,-322,323,321,-323,324,321,-324,325,321,-325,326,321,-326,327,329,-329,330,328,-330,331,330,-330,332,331,-330,333,332,-330,334,336,-336,337,335,-337,338,340,-340,341,339,-341,342,344,-344,345,343,-345,346,348,-348,349,347,-349,350,352,-352,353,351,-353,354,356,-356,357,355,-357,358,360,-360,361,363,-363,364,363,-366,366,367,-366
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *2124 {
				a: 0,-0.9903742,-0.1384157,0,-0.9801239,-0.1983865,0,-0.983003,-0.1835897,0,0.9623022,0.2719822,0,0.9587674,0.2841918,0,0.983003,0.1835897,0,0.9804893,0.1965728,0,0.983003,0.1835897,0,0.9587674,0.2841918,0,-0.9623022,-0.2719822,0,-0.9582326,-0.2859898,0,-0.9342806,-0.3565389,0,-0.9490806,-0.3150335,0,-0.9342806,-0.3565389,0,-0.9582326,-0.2859898,0,-0.9342806,-0.3565389,0,-0.9342806,-0.3565389,0,-0.9490806,-0.3150335,0,-0.9490806,-0.3150335,0,-0.9342806,-0.3565389,0,-0.9490806,-0.3150335,0,-0.9582326,-0.2859898,0,-0.9342806,-0.3565389,0,-0.9490806,-0.3150335,0,-0.9623022,-0.2719822,0,-0.9342806,-0.3565389,0,-0.9582326,-0.2859898,0,0.9957132,0.09249406,0,0.9943666,0.1059954,0,1,0,0,1,0,0,1,0,0,0.9943666,0.1059954,0,0.9943666,0.1059954,0,0.9957132,0.09249406,0,1,0,0,1,0,0,1,0,0,0.9957132,0.09249406,0,0.9943666,-0.1059954,0,1,0,0,0.9957132,-0.09249406,0,1,0,0,0.9957132,-0.09249406,0,1,0,0,0.9804893,-0.1965728,0,0.9943666,-0.1059954,0,0.983003,-0.1835897,0,0.9957132,-0.09249406,0,0.983003,-0.1835897,0,0.9943666,-0.1059954,0,0.983003,0.1835897,0,0.9804893,0.1965728,0,0.9957132,0.09249406,0,0.9943666,0.1059954,0,0.9957132,0.09249406,0,0.9804893,0.1965728,0,0.9587674,0.2841918,0,0.9623022,0.2719822,0,0.9804893,0.1965728,0,0.983003,0.1835897,0,0.9804893,0.1965728,0,0.9623022,0.2719822,0,-0.9582326,-0.2859898,0,-0.9801239,-0.1983865,0,-0.9623022,-0.2719822,0,-0.983003,-0.1835897,0,-0.9623022,-0.2719822,0,-0.9801239,-0.1983865,0,-0.9903742,-0.1384157,0,-0.983003,-0.1835897,0,-0.9801239,-0.1983865,0,0.9623022,0.2719822,0,0.9342806,0.3565389,0,0.9490806,0.3150335,0,0.9342806,0.3565389,0,0.9490806,0.3150335,0,0.9342806,0.3565389,0,0.9587674,0.2841918,0,0.9623022,0.2719822,0,0.9490806,0.3150335,0,0.9587674,0.2841918,0,0.9490806,0.3150335,0,0.9342806,0.3565389,0,0.9623022,0.2719822,0,0.9587674,0.2841918,0,0.9342806,0.3565389,0,-0.983003,-0.1835897,0,-0.9801239,-0.1983865,0,-0.9623022,-0.2719822,0,-0.9582326,-0.2859898,0,-0.9623022,-0.2719822,0,-0.9801239,-0.1983865,0,-0.9623022,0.2719822,0,-0.9582326,0.2859898,0,-0.983003,0.1835897,0,-0.9801239,0.1983865,0,-0.983003,0.1835897,0,-0.9582326,0.2859898,0,-0.9342806,0.3565389,0,-0.9342806,0.3565389,0,-0.9623022,0.2719822,0,-0.9490806,0.3150335,0,-0.9623022,0.2719822,0,-0.9342806,0.3565389,0,-0.9582326,0.2859898,0,-0.9623022,0.2719822,0,-0.9490806,0.3150335,0,-0.9490806,0.3150335,0,-0.9490806,0.3150335,0,-0.9342806,0.3565389,0,-0.9582326,0.2859898,0,-0.9490806,0.3150335,0,-0.9342806,0.3565389,0,-0.9623022,0.2719822,0,-0.9582326,0.2859898,0,-0.9342806,0.3565389,0,0.9957132,-0.09249406,0,0.9943666,-0.1059954,0,0.983003,-0.1835897,0,0.9804893,-0.1965728,0,0.983003,-0.1835897,0,0.9943666,-0.1059954,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.3105056,-0.9505715,0,0.3105056,-0.9505715,0,0.3105056,-0.9505715,0,0.3105056,-0.9505715,0,0.3105056,-0.9505715,0,0.3105056,-0.9505715,0,-0.9342806,-0.3565389,0,-0.9342806,-0.3565389,0,-0.8997754,-0.4363533,0,-0.8997754,-0.4363533,0,-0.8997754,-0.4363533,0,-0.9342806,-0.3565389,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.983003,0.1835897,0,-0.9801239,0.1983865,0,-0.9903742,0.1384157,0,0.8997754,-0.4363533,0,0.8997754,-0.4363533,0,0.8802279,-0.4745513,0,0.8802279,-0.4745513,0,0.8802279,-0.4745513,0,0.8997754,-0.4363533,0,0.8802279,0.4745513,0,0.8802279,0.4745513,0,0.8997754,0.4363533,0,0.8997754,0.4363533,0,0.8997754,0.4363533,0,0.8802279,0.4745513,0,-0.8802279,0.4745513,0,-0.8802279,0.4745513,0,-0.8802279,0.4745513,0,-0.8997754,0.4363533,0,-0.8802279,0.4745513,0,-0.8802279,0.4745513,0,-0.8997754,0.4363533,0,-0.8997754,0.4363533,0,-0.8802279,0.4745513,0,-0.8802279,0.4745513,0,-0.8997754,0.4363533,0,-0.8802279,0.4745513,0,0.9342806,-0.3565389,0,0.9342806,-0.3565389,0,0.8997754,-0.4363533,0,0.8997754,-0.4363533,0,0.8997754,-0.4363533,0,0.9342806,-0.3565389,0,-0.8997754,-0.4363533,0,-0.8997754,-0.4363533,0,-0.8802279,-0.4745513,0,-0.8802279,-0.4745513,0,-0.8802279,-0.4745513,0,-0.8997754,-0.4363533,0,-0.8802279,-0.4745513,0,-0.8802279,-0.4745513,0,-0.8997754,-0.4363533,0,-0.8802279,-0.4745513,0,-0.8802279,-0.4745513,0,-0.8997754,-0.4363533,0,-0.983003,0.1835897,0,-0.9903742,0.1384157,0,-0.9801239,0.1983865,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.8997754,0.4363533,0,-0.8997754,0.4363533,0,-0.9342806,0.3565389,0,-0.9342806,0.3565389,0,-0.9342806,0.3565389,0,-0.8997754,0.4363533,0,0.3105056,0.9505715,0,0.3105056,0.9505715,0,0.3105056,0.9505715,0,0.3105056,0.9505715,0,0.3105056,0.9505715,0,0.3105056,0.9505715,0,1,0,0,1,0,0,0.9957132,-0.09249406,0,0.9943666,-0.1059954,0,0.9957132,-0.09249406,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.9587674,-0.2841918,0,0.9804893,-0.1965728,0,0.9623022,-0.2719822,0,0.983003,-0.1835897,0,0.9623022,-0.2719822,0,0.9804893,-0.1965728,0,0.8997754,0.4363533,0,0.8997754,0.4363533,0,0.9342806,0.3565389,0,0.9342806,0.3565389,0,0.9342806,0.3565389,0,0.8997754,0.4363533,0,0.983003,-0.1835897,0,0.9804893,-0.1965728,0,0.9623022,-0.2719822,0,0.9587674,-0.2841918,0,0.9623022,-0.2719822,0,0.9804893,-0.1965728,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,0,-0.9582326,0.2859898,0,-0.9623022,0.2719822,0,-0.9801239,0.1983865,0,-0.983003,0.1835897,0,-0.9801239,0.1983865,0,-0.9623022,0.2719822,0,0.9623022,-0.2719822,0,0.9587674,-0.2841918,0,0.9342806,-0.3565389,0,0.9490806,-0.3150335,0,0.9342806,-0.3565389,0,0.9587674,-0.2841918,0,0.9342806,-0.3565389,0,0.9342806,-0.3565389,0,0.9490806,-0.3150335,0,0.9587674,-0.2841918,0,0.9342806,-0.3565389,0,0.9490806,-0.3150335,0,0.9623022,-0.2719822,0,0.9342806,-0.3565389,0,0.9587674,-0.2841918,0,0.9804893,0.1965728,0,0.983003,0.1835897,0,0.9943666,0.1059954,0,0.9957132,0.09249406,0,0.9943666,0.1059954,0,0.983003,0.1835897,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.873743,0.2119244,-0.4377913,0.873743,0.2119244,-0.4377913,0.873743,0.2119244,-0.4377913,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.873743,0.2119244,0.4377913,-0.873743,0.2119244,0.4377913,-0.873743,0.2119244,0.4377913,0.873743,0.2119244,0.4377913,0.873743,0.2119244,0.4377913,0.873743,0.2119244,0.4377913,-0.873743,0.2119244,-0.4377913,-0.873743,0.2119244,-0.4377913,-0.873743,0.2119244,-0.4377913,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,0.893631,-0.03062652,0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,-0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.893631,-0.03062652,-0.4477562,0.8647177,-0.05493484,0.499245,0.8647177,-0.05493484,0.499245,0.8647177,-0.05493484,0.499245,0.8647177,-0.05493484,0.499245,0.8647177,-0.05493484,0.499245,0.8647177,-0.05493484,0.499245,0.8647177,-0.05493484,-0.499245,0.8647177,-0.05493484,-0.499245,0.8647177,-0.05493484,-0.499245,0.8647177,-0.05493484,-0.499245,0.8647177,-0.05493484,-0.499245,0.8647177,-0.05493484,-0.499245,0,-0.05493485,0.99849,0,-0.05493485,0.99849,0,-0.05493485,0.99849,0,-0.05493485,0.99849,0,-0.05493485,0.99849,0,-0.05493485,0.99849,-0.8647177,-0.05493484,-0.499245,-0.8647177,-0.05493484,-0.499245,-0.8647177,-0.05493484,-0.499245,-0.8647177,-0.05493484,-0.499245,-0.8647177,-0.05493484,-0.499245,-0.8647177,-0.05493484,-0.499245,0,-0.05493485,-0.99849,0,-0.05493485,-0.99849,0,-0.05493485,-0.99849,0,-0.05493485,-0.99849,0,-0.05493485,-0.99849,0,-0.05493485,-0.99849,-0.8647177,-0.05493484,0.499245,-0.8647177,-0.05493484,0.499245,-0.8647177,-0.05493484,0.499245,-0.8647177,-0.05493484,0.499245,-0.8647177,-0.05493484,0.499245,-0.8647177,-0.05493484,0.499245,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *736 {
				a: -3.106949,2.16044,-3.106949,2.800237,-2.741118,2.800237,3.106949,4.429072,3.106949,3.450342,0.4234591,4.429072,0.8932337,3.450342,-3.106949,4.607578,-3.106949,5.73682,-2.112422,4.607578,-2.007567,4.798935,3.106949,5.73682,2.007567,4.798935,2.112422,4.607578,3.106949,4.607578,3.106949,1.2796,3.106949,0.3105706,1.371103,1.2796,1.854576,0.3105706,-1.371103,1.2796,-1.854576,0.3105706,-3.106949,1.2796,-3.106949,0.3105706,1.371103,1.2796,3.106949,1.2796,1.854576,0.3105706,3.106949,0.3105706,0.8932337,2.864152,3.106949,2.864152,1.371103,1.891856,3.106949,1.891856,3.106949,2.864152,3.106949,1.891856,0.8932337,2.864152,1.371103,1.891856,-0.4234591,4.429072,-0.8932337,3.450342,-3.106949,4.429072,-3.106949,3.450342,2.112422,4.281255,3.106949,4.281255,2.741118,3.162762,3.106949,3.162762,3.106949,2.16044,2.741118,2.800237,3.106949,2.800237,3.106949,4.973691,1.354804E-11,5.88486,3.106949,5.961826,-3.106949,5.961826,0.4234591,4.973691,-0.4234591,4.973691,-3.106949,4.973691,-3.106949,3.162762,-3.106949,4.281255,-2.741118,3.162762,-2.112422,4.281255,3.106949,4.281255,3.106949,3.162762,2.112422,4.281255,2.741118,3.162762,3.106949,5.73682,3.106949,4.607578,-3.106949,5.73682,2.007567,4.798935,2.112422,4.607578,-2.007567,4.798935,-2.112422,4.607578,-3.106949,4.607578,-3.106949,1.891856,-3.106949,2.864152,-1.371103,1.891856,-0.8932337,2.864152,1.565385,-5.323182,-1.565385,-5.323182,1.565385,-3.995426,-1.565385,-3.995426,-3.106949,3.746476,-3.106949,6.131537,3.106949,3.746476,3.106949,6.131537,-3.106949,6.015579,-3.106949,7.158652,3.106949,6.015579,3.106949,7.158652,2.007567,3.55207,-2.007567,3.55207,2.007567,4.75754,-2.007567,4.75754,1.565385,-3.995426,1.565385,-5.323182,-1.565385,-3.995426,-1.565385,-5.323182,3.106949,1.573023,3.106949,-1.573023,2.007567,3.477208,2.685125,-1.136868E-13,1.342562,2.325386,-2.007567,3.477208,-1.342562,2.325386,-2.685125,-5.684342E-14,-3.106949,1.573023,2.007567,-3.477208,1.342562,-2.325386,-2.007567,-3.477208,-1.342562,-2.325386,-3.106949,-1.573023,3.106949,2.800237,3.106949,2.16044,2.741118,2.800237,-3.106949,7.877491,-3.106949,8.892183,3.106949,7.877491,3.106949,8.892183,3.106949,8.892183,3.106949,7.877491,-3.106949,8.892183,-3.106949,7.877491,3.106949,8.468169,3.106949,7.626824,-3.106949,8.468169,3.106949,7.381027,-3.106949,7.381027,-3.106949,7.626824,-3.106949,6.45186,-3.106949,7.452098,3.106949,6.45186,3.106949,7.452098,-3.106949,7.381027,-3.106949,7.626824,3.106949,7.381027,-3.106949,8.468169,3.106949,8.468169,3.106949,7.626824,-3.106949,2.800237,-2.741118,2.800237,-3.106949,2.16044,-2.007567,3.55207,-2.007567,4.75754,0.1911968,3.55207,-1.797858,4.697257,-0.5404654,4.441816,0.1911968,4.353258,6.373229,6.023112,5.632654,5.623851,5.632654,8.290283,4.739493,7.80876,5.416296,5.507208,4.367336,5.053006,3.821608,7.411314,3.295594,4.697257,2.883788,7.100018,2.206661,4.441816,1.930924,6.876496,1.573023,4.353258,0.9679866,6.741915,1.573023,3.55207,-1.573023,3.55207,1.136868E-13,6.696977,-0.9679866,6.741915,-1.573023,4.353258,-1.930924,6.876496,-2.206661,4.441816,-2.883788,7.100018,-3.295594,4.697257,-3.821608,7.411314,-4.367336,5.053006,-4.739493,7.80876,-5.416296,5.507208,-5.632654,8.290283,-5.632654,5.623851,-6.373229,6.023112,3.106949,7.158652,3.106949,6.015579,-3.106949,7.158652,-3.106949,6.015579,3.106949,3.746476,-3.106949,3.746476,3.106949,6.131537,-3.106949,6.131537,-3.106949,0.3105706,-3.106949,1.2796,-1.854576,0.3105706,-1.371103,1.2796,-1.573023,3.55207,-1.573023,4.353258,1.573023,3.55207,-0.9679866,6.741915,-5.684342E-14,6.696977,0.9679866,6.741915,1.573023,4.353258,1.930924,6.876496,2.206661,4.441816,2.883788,7.100018,3.295594,4.697257,3.821608,7.411314,4.367336,5.053006,4.739493,7.80876,5.416296,5.507208,5.632654,8.290283,5.632654,5.623851,6.373229,6.023112,-1.930924,6.876496,-2.206661,4.441816,-2.883788,7.100018,-3.295594,4.697257,-3.821608,7.411314,-4.367336,5.053006,-4.739493,7.80876,-5.416296,5.507208,-5.632654,8.290283,-5.632654,5.623851,-6.373229,6.023112,0.4234591,4.429072,3.106949,4.429072,0.8932337,3.450342,3.106949,3.450342,3.106949,7.452098,3.106949,6.45186,-3.106949,7.452098,-3.106949,6.45186,-3.106949,3.450342,-3.106949,4.429072,-0.8932337,3.450342,-0.4234591,4.429072,-1.565385,-5.323182,-1.565385,-3.995426,1.565385,-5.323182,1.565385,-3.995426,-2.112422,4.281255,-2.741118,3.162762,-3.106949,4.281255,-3.106949,3.162762,-3.106949,4.973691,-3.106949,5.961826,-0.4234591,4.973691,-1.353255E-11,5.88486,3.106949,5.961826,0.4234591,4.973691,3.106949,4.973691,-0.8932337,2.864152,-1.371103,1.891856,-3.106949,2.864152,-3.106949,1.891856,-0.1911968,3.55207,-0.1911968,4.353258,2.007567,3.55207,0.5404654,4.441816,1.797858,4.697257,2.007567,4.75754,-3.13077,-1.136868E-13,-2.205638,-1.136868E-13,-1.565385,-2.711326,-1.102819,-1.910139,1.565385,-2.711326,1.102819,-1.910139,2.205638,-5.684342E-14,3.13077,-5.684342E-14,-1.565385,2.711326,-1.102819,1.910139,1.565385,2.711326,1.102819,1.910139,2.007567,3.55207,-0.1911968,3.55207,2.007567,4.75754,1.797858,4.697257,0.5404654,4.441816,-0.1911968,4.353258,0.1911968,3.55207,-2.007567,3.55207,0.1911968,4.353258,-0.5404654,4.441816,-1.797858,4.697257,-2.007567,4.75754,1.565385,-5.323182,-1.565385,-5.323182,1.565385,-3.995426,-1.565385,-3.995426,-1.565385,-5.323182,-1.565385,-3.995426,1.565385,-5.323182,1.565385,-3.995426,2.007567,3.55207,-2.007567,3.55207,2.007567,4.75754,-2.007567,4.75754,1.565385,-3.995426,1.565385,-5.323182,-1.565385,-3.995426,-1.565385,-5.323182,-1.777449E-11,35.93544,4.510907,25.27031,-1.13248,25.27031,2.528047,-1.136868E-13,1.306985E-11,-5.045473,1.306985E-11,5.045473,-2.528047,-5.684342E-14,-1.13248,25.27031,1.777404E-11,35.93544,4.510907,25.27031,1.797777E-11,35.93544,1.13248,25.27031,-4.510907,25.27031,-4.510907,25.27031,-1.797733E-11,35.93544,1.13248,25.27031,-0.8307874,6.744616,-1.13248,26.40463,0.2512207,6.789576,4.510907,26.40463,1.326204,6.92422,2.388556,7.147846,3.351402,7.43503,1.13248,26.40463,0.8307874,6.744616,-4.510907,26.40463,-0.2512207,6.789576,-1.326204,6.92422,-2.388556,7.147846,-3.351402,7.43503,-4.510907,26.40463,1.13248,26.40463,-3.351402,7.43503,-2.388556,7.147846,-1.326204,6.92422,-0.2512207,6.789576,0.8307874,6.744616,4.510907,26.40463,3.351402,7.43503,-1.13248,26.40463,2.388556,7.147846,1.326204,6.92422,0.2512207,6.789576,-0.8307874,6.744616,1.342562,3.674451,1.102819,-3.884459,-1.342562,3.674451,-1.102819,-3.884459,1.342562,3.674451,1.102819,-3.884459,-1.342562,3.674451,-1.102819,-3.884459,1.102819,-3.884459,-1.102819,-3.884459,1.342562,3.674451,-1.342562,3.674451,-1.342562,3.674451,1.342562,3.674451,-1.102819,-3.884459,1.102819,-3.884459,1.102819,-3.884459,-1.102819,-3.884459,1.342562,3.674451,-1.342562,3.674451,-1.102819,-3.884459,-1.342562,3.674451,1.102819,-3.884459,1.342562,3.674451,-3.106949,7.626824,-3.106949,8.468169,-3.106949,7.381027,-3.106949,7.626824,-3.106949,8.468169,-3.106949,7.381027,-3.106949,7.626824,-3.106949,8.468169,-3.106949,7.626824,-3.106949,7.381027
				}
			UVIndex: *708 {
				a: 0,2,1,3,5,4,6,4,5,7,9,8,10,8,9,11,8,10,12,11,10,13,11,12,14,11,13,15,17,16,18,16,17,19,21,20,22,20,21,23,25,24,26,24,25,27,29,28,30,28,29,31,33,32,34,32,33,35,37,36,38,36,37,39,41,40,42,40,41,43,45,44,46,48,47,49,47,48,50,46,47,51,47,49,52,51,49,53,55,54,56,54,55,57,59,58,60,58,59,61,63,62,64,62,63,65,62,64,66,64,63,67,66,63,68,67,63,69,71,70,72,70,71,73,75,74,76,74,75,77,79,78,80,78,79,81,83,82,84,82,83,85,87,86,88,86,87,89,91,90,92,90,91,93,95,94,96,94,95,97,96,95,98,97,95,99,97,98,100,99,98,101,100,98,94,96,102,103,102,96,104,102,103,105,104,103,100,104,105,106,104,100,101,106,100,107,109,108,110,112,111,113,111,112,114,116,115,117,115,116,118,120,119,121,119,120,122,121,120,123,122,120,124,126,125,127,125,126,128,130,129,131,129,130,132,131,130,133,132,130,134,136,135,137,139,138,140,138,139,141,140,139,142,141,139,143,145,144,146,144,145,147,144,146,148,147,146,149,148,146,150,148,149,151,150,149,152,150,151,153,152,151,154,152,153,155,154,153,156,154,155,157,156,155,158,157,155,159,157,158,160,157,159,161,160,159,162,160,161,163,162,161,164,162,163,165,164,163,166,164,165,167,166,165,168,166,167,169,168,167,170,168,169,171,170,169,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182,184,186,185,187,185,186,188,187,186,189,188,186,190,189,186,191,189,190,192,191,190,193,191,192,194,193,192,195,193,194,196,195,194,197,195,196,198,197,196,199,197,198,200,199,198,201,199,200,187,202,185,185,202,203,202,204,203,203,204,205,204,206,205,205,206,207,206,208,207,207,208,209,208,210,209,209,210,211,212,211,210,213,215,214,216,214,215,217,219,218,220,218,219,221,223,222,224,222,223,225,227,226,228,226,227,229,231,230,232,230,231,233,235,234,236,234,235,237,234,236,238,237,236,239,237,238,240,242,241,243,241,242,244,246,245,247,245,246,248,247,246,249,248,246,250,252,251,253,251,252,254,253,252,255,253,254,256,255,254,257,256,254,250,251,258,259,258,251,260,258,259,257,260,256,261,260,259,256,260,261,262,264,263,265,263,264,266,263,265,267,263,266,268,270,269,271,269,270,272,269,271,273,269,272,274,276,275,277,275,276,278,280,279,281,279,280,282,284,283,285,283,284,286,288,287,289,287,288,290,292,291,293,295,294,296,294,295,297,299,298,300,302,301,303,305,304,306,308,307,309,307,308,310,309,308,311,309,310,312,309,311,313,315,314,316,314,315,317,316,315,318,317,315,319,318,315,320,322,321,323,321,322,324,321,323,325,321,324,326,321,325,327,329,328,330,328,329,331,330,329,332,331,329,333,332,329,334,336,335,337,335,336,338,340,339,341,339,340,342,344,343,345,343,344,346,348,347,349,347,348,350,352,351,353,351,352,354,356,355,357,355,356,358,360,359,361,363,362,364,363,365,366,367,365
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *236 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 16426, "Material::textile", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8196079,0.7529412,0.6705883
			P: "DiffuseColor", "Color", "", "A",0.8196079,0.7529412,0.6705883
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 11274, "Material::stone", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5843138,0.6862745,0.7568628
			P: "DiffuseColor", "Color", "", "A",0.5843138,0.6862745,0.7568628
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 10732, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh sword, Model::RootNode
	C: "OO",5312475311935442037,0

	;Geometry::, Model::Mesh sword
	C: "OO",**********907629477,5312475311935442037

	;Material::textile, Model::Mesh sword
	C: "OO",16426,5312475311935442037

	;Material::stone, Model::Mesh sword
	C: "OO",11274,5312475311935442037

	;Material::wood, Model::Mesh sword
	C: "OO",19362,5312475311935442037

	;Material::_defaultMat, Model::Mesh sword
	C: "OO",10732,5312475311935442037

}
