; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 38
		Millisecond: 373
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/shovel.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/shovel.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4648662931753636547, "Model::shovel", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-55.51077,1.588205E-13,-64.79334
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5374378488957032589, "Geometry::", "Mesh" {
		Vertices: *612 {
			a: -0.5519999,-1.624301E-14,7.792399,-0.2944,-1.624301E-14,8.05,-0.5519999,0.368,7.792399,-0.2944,0.368,8.05,-0.8096,-1.624301E-14,8.221015,-1.3984,-1.624301E-14,8.221015,-0.8096,0.368,8.221015,-1.3984,0.368,8.221015,-0.7230159,0.368,8.221015,-0.7230159,-1.624301E-14,8.221015,-1.484984,-1.624301E-14,8.221015,-1.484984,0.368,8.221015,-1.3984,-1.624301E-14,3.40974,-1.3984,0.368,3.40974,-1.3984,-1.624301E-14,7.792399,-1.3984,0.368,7.792399,-0.2944,0.368,8.05,-0.2944,-1.624301E-14,8.05,-0.2944,0.368,8.97,-0.2944,-1.624301E-14,8.97,-1.9136,-1.624301E-14,8.05,-1.9136,0.368,8.05,-1.9136,-1.624301E-14,8.97,-1.9136,0.368,8.97,-0.8096,0.368,3.40974,-0.8096,0.368,7.792399,-1.3984,0.368,3.40974,-0.8096,0.368,8.221015,-1.3984,0.368,8.221015,-1.3984,0.368,7.792399,-1.656,0.368,7.792399,-1.484984,0.368,8.221015,-1.484984,0.368,8.541384,-1.9136,0.368,8.97,-0.7230159,0.368,8.541384,-0.2944,0.368,8.97,-0.5519999,0.368,7.792399,-0.2944,0.368,8.05,-0.7230159,0.368,8.221015,-1.9136,0.368,8.05,-0.8096,0.368,3.40974,-0.8096,-1.624301E-14,3.40974,-0.8096,0.368,7.792399,-0.8096,-1.624301E-14,7.792399,-0.8096,-1.624301E-14,7.792399,-0.5519999,-1.624301E-14,7.792399,-0.8096,0.368,7.792399,-0.5519999,0.368,7.792399,-0.2944,-1.624301E-14,8.05,-0.7230159,-1.624301E-14,8.541384,-0.2944,-1.624301E-14,8.97,-1.9136,-1.624301E-14,8.97,-0.5519999,-1.624301E-14,7.792399,-1.484984,-1.624301E-14,8.541384,-1.484984,-1.624301E-14,8.221015,-1.656,-1.624301E-14,7.792399,-1.9136,-1.624301E-14,8.05,-1.3984,-1.624301E-14,7.792399,-1.3984,-1.624301E-14,8.221015,-0.7230159,-1.624301E-14,8.221015,-0.8096,-1.624301E-14,7.792399,-0.8096,-1.624301E-14,8.221015,-0.8096,-1.624301E-14,3.40974,-1.3984,-1.624301E-14,3.40974,-1.484984,0.368,8.221015,-1.484984,-1.624301E-14,8.221015,-1.484984,0.368,8.541384,-1.484984,-1.624301E-14,8.541384,-0.7230159,-1.624301E-14,8.221015,-0.7230159,0.368,8.221015,-0.7230159,-1.624301E-14,8.541384,-0.7230159,0.368,8.541384,-0.2944,-1.624301E-14,8.97,-1.9136,-1.624301E-14,8.97,-0.2944,0.368,8.97,-1.9136,0.368,8.97,-1.656,-1.624301E-14,7.792399,-1.3984,-1.624301E-14,7.792399,-1.656,0.368,7.792399,-1.3984,0.368,7.792399,-1.656,-1.624301E-14,7.792399,-1.656,0.368,7.792399,-1.9136,-1.624301E-14,8.05,-1.9136,0.368,8.05,-1.484984,-1.624301E-14,8.541384,-0.7230159,-1.624301E-14,8.541384,-1.484984,0.368,8.541384,-0.7230159,0.368,8.541384,-0.8182638,-1.624301E-14,0.03761789,-0.5519999,-1.624301E-14,0.147908,-0.8182638,0.2208,0.03761789,-0.5519999,0.2208,0.147908,-2.208,-1.624301E-14,1.104,-2.208,0.2208,1.104,-2.208,-1.624301E-14,2.6128,-2.208,0.2208,2.6128,-1.884646,-1.624301E-14,0.3233541,-1.656,-1.624301E-14,0.147908,-1.884646,0.2208,0.3233541,-1.656,0.2208,0.147908,-0.5519999,0.2208,0.147908,-0.8096,0.2208,2.128721,-0.8182638,0.2208,0.03761789,-1.3984,0.2208,2.128721,-1.104,0.2208,0,-1.389736,0.2208,0.03761789,-1.656,0.2208,0.147908,-1.3984,0.2208,2.6128,-2.208,0.2208,2.6128,-1.884646,0.2208,0.3233541,-2.060092,0.2208,0.5519999,-2.170382,0.2208,0.8182638,-2.208,0.2208,1.104,-0.8096,0.2208,2.6128,-0.3233541,0.2208,0.3233541,-0.147908,0.2208,0.5519999,-0.03761789,0.2208,0.8182638,0,0.2208,2.6128,0,0.2208,1.104,-0.8096,0.2208,2.128721,-0.8096,0.368,2.6128,-1.3984,0.2208,2.128721,-1.3984,0.368,2.6128,-1.656,-1.624301E-14,0.147908,-1.389736,-1.624301E-14,0.03761789,-1.656,0.2208,0.147908,-1.389736,0.2208,0.03761789,-0.03761789,0.2208,0.8182638,-0.03761789,-1.624301E-14,0.8182638,0,0.2208,1.104,0,-1.624301E-14,1.104,-0.8096,0.368,2.6128,-1.3984,0.368,2.6128,-0.147908,0.2208,0.5519999,-0.147908,-1.624301E-14,0.5519999,-0.03761789,0.2208,0.8182638,-0.03761789,-1.624301E-14,0.8182638,-0.8096,0.2208,2.128721,-0.8096,0.2208,2.6128,-0.8096,0.368,2.6128,-0.8096,-1.624301E-14,2.6128,-1.3984,-1.624301E-14,2.6128,-2.208,-1.624301E-14,2.6128,-1.3984,0.2208,2.6128,-2.208,0.2208,2.6128,-2.060092,-1.624301E-14,0.5519999,-2.060092,0.2208,0.5519999,-2.170382,-1.624301E-14,0.8182638,-2.170382,0.2208,0.8182638,-1.389736,-1.624301E-14,0.03761789,-1.104,-1.624301E-14,0,-1.389736,0.2208,0.03761789,-1.104,0.2208,0,-0.8182638,-1.624301E-14,0.03761789,-0.8182638,0.2208,0.03761789,-1.3984,-1.624301E-14,2.6128,-1.3984,0.2208,2.6128,-1.3984,0.368,2.6128,-1.3984,0.2208,2.128721,-0.5519999,-1.624301E-14,0.147908,-0.3233541,-1.624301E-14,0.3233541,-0.5519999,0.2208,0.147908,-0.3233541,0.2208,0.3233541,-0.8096,-1.624301E-14,2.6128,-0.8182638,-1.624301E-14,0.03761789,-1.104,-1.624301E-14,0,-1.389736,-1.624301E-14,0.03761789,-1.3984,-1.624301E-14,2.6128,-1.656,-1.624301E-14,0.147908,-2.208,-1.624301E-14,2.6128,-1.884646,-1.624301E-14,0.3233541,-2.060092,-1.624301E-14,0.5519999,-2.170382,-1.624301E-14,0.8182638,-2.208,-1.624301E-14,1.104,-0.5519999,-1.624301E-14,0.147908,-0.3233541,-1.624301E-14,0.3233541,-0.147908,-1.624301E-14,0.5519999,-0.03761789,-1.624301E-14,0.8182638,0,-1.624301E-14,1.104,0,-1.624301E-14,2.6128,-0.3233541,0.2208,0.3233541,-0.3233541,-1.624301E-14,0.3233541,-0.147908,0.2208,0.5519999,-0.147908,-1.624301E-14,0.5519999,-2.170382,-1.624301E-14,0.8182638,-2.170382,0.2208,0.8182638,-2.208,-1.624301E-14,1.104,-2.208,0.2208,1.104,-1.3984,-1.624301E-14,3.40974,-0.8096,-1.624301E-14,3.40974,-1.3984,0.368,3.40974,-0.8096,0.368,3.40974,0,-1.624301E-14,2.6128,-0.8096,-1.624301E-14,2.6128,0,0.2208,2.6128,-0.8096,0.2208,2.6128,-1.884646,-1.624301E-14,0.3233541,-1.884646,0.2208,0.3233541,-2.060092,-1.624301E-14,0.5519999,-2.060092,0.2208,0.5519999,0,0.2208,1.104,0,-1.624301E-14,1.104,0,0.2208,2.6128,0,-1.624301E-14,2.6128
		} 
		PolygonVertexIndex: *426 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,6,4,-9,9,8,-5,5,7,-11,11,10,-8,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,27,-27,29,28,-27,30,28,-30,31,28,-31,32,31,-31,30,33,-33,32,33,-35,33,35,-35,34,35,-37,37,36,-36,38,34,-37,36,25,-39,27,38,-26,39,33,-31,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,48,-50,53,49,-52,54,53,-52,55,54,-52,56,55,-52,55,57,-55,58,54,-58,49,59,-53,60,52,-60,61,60,-60,58,60,-62,62,60,-59,63,62,-59,57,63,-59,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,103,-103,105,103,-105,106,103,-106,107,103,-107,108,107,-107,109,108,-107,110,108,-110,111,108,-111,112,108,-112,101,113,-101,100,113,-115,114,113,-116,115,113,-117,113,117,-117,118,116,-118,119,121,-121,122,120,-122,123,125,-125,126,124,-126,127,129,-129,130,128,-130,131,132,-25,26,24,-133,133,135,-135,136,134,-136,137,139,-139,40,138,-140,140,138,-41,41,140,-41,141,143,-143,144,142,-144,145,147,-147,148,146,-148,149,151,-151,152,150,-152,150,152,-154,154,153,-153,155,12,-157,157,156,-13,13,157,-13,156,157,-159,159,161,-161,162,160,-162,62,63,-164,164,163,-64,165,164,-64,166,165,-64,167,166,-64,168,166,-168,169,168,-168,170,168,-170,171,170,-170,172,171,-170,173,172,-170,164,174,-164,174,175,-164,175,176,-164,176,177,-164,177,178,-164,179,163,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1278 {
				a: 0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.5,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-0.8660254,0.258819,0,-0.9659258,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.7071068,0,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.9567446,-0.2909294,0,0.9567446,-0.2909294,0,0.9567446,-0.2909294,0,0.9567446,-0.2909294,0,0.9567446,-0.2909294,0,0.9567446,-0.2909294,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,-0.5,0,-0.8660254,0.9659258,0,-0.258819,0.9914449,0,-0.1305262,0.9659258,0,-0.258819,0.9914449,0,-0.1305262,0.9659258,0,-0.258819,0.9914449,0,-0.1305262,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8660254,0,-0.5,0.9659258,0,-0.258819,0.8660254,0,-0.5,0.9659258,0,-0.258819,0.8660254,0,-0.5,0.9659258,0,-0.258819,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.258819,0,-0.9659258,-0.258819,0,-0.9659258,0,0,-1,0,0,-1,0,0,-1,-0.258819,0,-0.9659258,0,0,-1,0,0,-1,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0.258819,0,-0.9659258,0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.5,0,-0.8660254,0.5,0,-0.8660254,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.5,0,-0.8660254,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0,-0.7071068,0.8660254,0,-0.5,0.7071068,0,-0.7071068,0.8660254,0,-0.5,0.7071068,0,-0.7071068,0.8660254,0,-0.5,-0.9659258,0,-0.258819,-0.9914449,0,-0.1305262,-0.9659258,0,-0.258819,-0.9914449,0,-0.1305262,-0.9659258,0,-0.258819,-0.9914449,0,-0.1305262,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.7071068,0,-0.7071068,-0.8660254,0,-0.5,-0.7071068,0,-0.7071068,-0.8660254,0,-0.5,-0.7071068,0,-0.7071068,-0.8660254,0,-0.5,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *408 {
				a: -40.31288,-1.255853E-13,-43.1814,-1.255853E-13,-40.31288,2.897638,-43.1814,2.897638,-6.374803,-1.233413E-13,-11.01102,-1.233413E-13,-6.374803,2.897638,-11.01102,2.897638,-5.693038,2.897638,-5.693038,-1.233413E-13,-11.69279,-1.233413E-13,-11.69279,2.897638,26.84835,-1.278977E-13,26.84835,2.897638,61.35748,-1.278977E-13,61.35748,2.897638,-63.38583,2.897638,-63.38583,-1.278977E-13,-70.62992,2.897638,-70.62992,-1.278977E-13,63.38583,-1.278977E-13,63.38583,2.897638,70.62992,-1.278977E-13,70.62992,2.897638,6.374803,26.84835,6.374803,61.35748,11.01102,26.84835,6.374803,64.73241,11.01102,64.73241,11.01102,61.35748,13.03937,61.35748,11.69279,64.73241,11.69279,67.25499,15.06772,70.62992,5.693038,67.25499,2.31811,70.62992,4.346457,61.35748,2.31811,63.38583,5.693038,64.73241,15.06772,63.38583,-26.84835,2.897638,-26.84835,-1.278977E-13,-61.35748,2.897638,-61.35748,-1.278977E-13,6.374803,-1.235788E-13,4.346457,-1.235788E-13,6.374803,2.897638,4.346457,2.897638,-2.31811,63.38583,-5.693038,67.25499,-2.31811,70.62992,-15.06772,70.62992,-4.346457,61.35748,-11.69279,67.25499,-11.69279,64.73241,-13.03937,61.35748,-15.06772,63.38583,-11.01102,61.35748,-11.01102,64.73241,-5.693038,64.73241,-6.374803,61.35748,-6.374803,64.73241,-6.374803,26.84835,-11.01102,26.84835,-64.73241,2.897638,-64.73241,-1.278977E-13,-67.25499,2.897638,-67.25499,-1.278977E-13,64.73241,-1.278977E-13,64.73241,2.897638,67.25499,-1.278977E-13,67.25499,2.897638,-2.31811,-1.229261E-13,-15.06772,-1.229261E-13,-2.31811,2.897638,-15.06772,2.897638,13.03937,-1.235788E-13,11.01102,-1.235788E-13,13.03937,2.897638,11.01102,2.897638,52.60652,-1.261972E-13,52.60652,2.897638,55.47503,-1.261972E-13,55.47503,2.897638,11.69279,-1.231637E-13,5.693038,-1.231637E-13,11.69279,2.897638,5.693038,2.897638,5.839224,-1.277196E-13,3.569918,-1.277196E-13,5.839224,1.738583,3.569918,1.738583,8.692913,-1.278977E-13,8.692913,1.738583,20.57323,-1.278977E-13,20.57323,1.738583,13.32312,-1.282894E-13,11.05381,-1.282894E-13,13.32312,1.738583,11.05381,1.738583,4.346457,1.16463,6.374803,16.76158,6.443022,0.2962039,11.01102,16.76158,8.692913,-1.22377E-16,10.94281,0.2962039,13.03937,1.16463,11.01102,20.57323,17.38583,20.57323,14.83973,2.546095,16.2212,4.346457,17.08962,6.443022,17.38583,8.692913,6.374803,20.57323,2.546095,2.546095,1.16463,4.346457,0.2962039,6.443022,2.477401E-16,20.57323,2.477401E-16,8.692913,6.374803,16.54236,6.374803,20.52633,11.01102,16.54236,11.01102,20.52633,12.49249,-1.281522E-13,10.22319,-1.281522E-13,12.49249,1.738583,10.22319,1.738583,-6.349238,1.738583,-6.349238,-1.278873E-13,-8.618545,1.738583,-8.618545,-1.278873E-13,6.374803,20.57323,11.01102,20.57323,-3.569918,1.738583,-3.569918,-1.278239E-13,-5.839224,1.738583,-5.839224,-1.278239E-13,-16.76158,1.738583,-20.57323,1.738583,-20.57323,2.897638,-20.57323,-1.278977E-13,-11.01102,-1.264496E-13,-17.38583,-1.264496E-13,-11.01102,1.738583,-17.38583,1.738583,10.22319,-1.282566E-13,10.22319,1.738583,12.49249,-1.282566E-13,12.49249,1.738583,10.88785,-1.279769E-13,8.618545,-1.279769E-13,10.88785,1.738583,8.618545,1.738583,6.349238,-1.278185E-13,6.349238,1.738583,20.57323,-1.278977E-13,20.57323,1.738583,20.57323,2.897638,16.76158,1.738583,2.739294,-1.276983E-13,0.4699886,-1.276983E-13,2.739294,1.738583,0.4699886,1.738583,-6.374803,20.57323,-6.443022,0.2962039,-8.692913,9.089778E-30,-10.94281,0.2962039,-11.01102,20.57323,-13.03937,1.16463,-17.38583,20.57323,-14.83973,2.546095,-16.2212,4.346457,-17.08962,6.443022,-17.38583,8.692913,-4.346457,1.16463,-2.546095,2.546095,-1.16463,4.346457,-0.2962039,6.443022,1.822484E-29,8.692913,1.822484E-29,20.57323,-0.4699886,1.738583,-0.4699886,-1.277447E-13,-2.739294,1.738583,-2.739294,-1.277447E-13,8.618545,-1.280456E-13,8.618545,1.738583,10.88785,-1.280456E-13,10.88785,1.738583,11.01102,-1.260079E-13,6.374803,-1.260079E-13,11.01102,2.897638,6.374803,2.897638,2.063517E-31,-1.264496E-13,-6.374803,-1.264496E-13,2.063517E-31,1.738583,-6.374803,1.738583,11.05381,-1.283358E-13,11.05381,1.738583,13.32312,-1.283358E-13,13.32312,1.738583,-8.692913,1.738583,-8.692913,-1.278977E-13,-20.57323,1.738583,-20.57323,-1.278977E-13
				}
			UVIndex: *426 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,6,4,8,9,8,4,5,7,10,11,10,7,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,27,26,29,28,26,30,28,29,31,28,30,32,31,30,30,33,32,32,33,34,33,35,34,34,35,36,37,36,35,38,34,36,36,25,38,27,38,25,39,33,30,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,48,49,53,49,51,54,53,51,55,54,51,56,55,51,55,57,54,58,54,57,49,59,52,60,52,59,61,60,59,58,60,61,62,60,58,63,62,58,57,63,58,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,103,102,105,103,104,106,103,105,107,103,106,108,107,106,109,108,106,110,108,109,111,108,110,112,108,111,101,113,100,100,113,114,114,113,115,115,113,116,113,117,116,118,116,117,119,121,120,122,120,121,123,125,124,126,124,125,127,129,128,130,128,129,131,132,24,26,24,132,133,135,134,136,134,135,137,139,138,40,138,139,140,138,40,41,140,40,141,143,142,144,142,143,145,147,146,148,146,147,149,151,150,152,150,151,150,152,153,154,153,152,155,12,156,157,156,12,13,157,12,156,157,158,159,161,160,162,160,161,62,63,163,164,163,63,165,164,63,166,165,63,167,166,63,168,166,167,169,168,167,170,168,169,171,170,169,172,171,169,173,172,169,164,174,163,174,175,163,175,176,163,176,177,163,177,178,163,179,163,178,180,182,181,183,181,182,184,186,185,187,185,186,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *142 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 11274, "Material::stone", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5843138,0.6862745,0.7568628
			P: "DiffuseColor", "Color", "", "A",0.5843138,0.6862745,0.7568628
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh shovel, Model::RootNode
	C: "OO",4648662931753636547,0

	;Geometry::, Model::Mesh shovel
	C: "OO",5374378488957032589,4648662931753636547

	;Material::wood, Model::Mesh shovel
	C: "OO",19362,4648662931753636547

	;Material::stone, Model::Mesh shovel
	C: "OO",11274,4648662931753636547

}
