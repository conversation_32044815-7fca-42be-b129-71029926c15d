; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 938
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/paddle.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/paddle.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5041307787666490253, "Model::paddle", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-4.78968,3.69584,-100.7245
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4763917191890120681, "Geometry::", "Mesh" {
		Vertices: *216 {
			a: -0.289,0.272,-2.25,-0.289,0.272,5.028,-0.289,-6.046008E-14,-2.25,-0.289,7.219114E-15,5.028,-0.70516,-0.19584,5.742,-0.70516,0.46784,5.742,-0.70516,-0.19584,7.68,-0.70516,0.46784,7.68,0.70516,-0.19584,5.742,0.70516,-0.19584,7.68,0.70516,0.46784,5.742,0.70516,0.46784,7.68,-0.289,-6.046008E-14,-2.25,0.289,-6.046008E-14,-2.25,-0.289,0.272,-2.25,0.289,0.272,-2.25,-0.289,7.219114E-15,5.028,-0.289,0.272,5.028,-0.70516,-0.19584,5.742,-0.70516,0.46784,5.742,0.70516,0.46784,7.68,0.70516,-0.19584,7.68,0.4795088,0.3616512,7.952,0.4795088,-0.0896512,7.952,0.289,7.219114E-15,5.028,0.70516,-0.19584,5.742,0.289,0.272,5.028,0.70516,0.46784,5.742,-0.70516,-0.19584,7.68,-0.70516,0.46784,7.68,-0.4795088,-0.0896512,7.952,-0.4795088,0.3616512,7.952,0.70516,-0.19584,7.68,0.70516,-0.19584,5.742,-0.70516,-0.19584,7.68,-0.70516,-0.19584,5.742,0.70516,0.46784,5.742,-0.70516,0.46784,5.742,0.289,0.272,5.028,-0.289,0.272,5.028,0.70516,-0.19584,7.68,-0.70516,-0.19584,7.68,0.4795088,-0.0896512,7.952,-0.4795088,-0.0896512,7.952,0.289,0.272,5.028,-0.289,0.272,5.028,0.289,0.272,-2.25,-0.289,0.272,-2.25,0.289,7.219114E-15,5.028,0.289,-6.046008E-14,-2.25,-0.289,7.219114E-15,5.028,-0.289,-6.046008E-14,-2.25,0.70516,0.46784,7.68,0.4795088,0.3616512,7.952,-0.70516,0.46784,7.68,-0.4795088,0.3616512,7.952,0.70516,0.46784,5.742,0.70516,0.46784,7.68,-0.70516,0.46784,5.742,-0.70516,0.46784,7.68,0.4795088,-0.0896512,7.952,-0.4795088,-0.0896512,7.952,0.4795088,0.3616512,7.952,-0.4795088,0.3616512,7.952,0.70516,-0.19584,5.742,0.289,7.219114E-15,5.028,-0.70516,-0.19584,5.742,-0.289,7.219114E-15,5.028,0.289,0.272,-2.25,0.289,-6.046008E-14,-2.25,0.289,0.272,5.028,0.289,7.219114E-15,5.028
		} 
		PolygonVertexIndex: *108 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *324 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.8639579,0,-0.5035641,-0.8639579,0,-0.5035641,-0.8639579,0,-0.5035641,-0.8639579,0,-0.5035641,-0.8639579,0,-0.5035641,-0.8639579,0,-0.5035641,0.7696325,0,0.6384871,0.7696325,0,0.6384871,0.7696325,0,0.6384871,0.7696325,0,0.6384871,0.7696325,0,0.6384871,0.7696325,0,0.6384871,0.8639579,0,-0.5035641,0.8639579,0,-0.5035641,0.8639579,0,-0.5035641,0.8639579,0,-0.5035641,0.8639579,0,-0.5035641,0.8639579,0,-0.5035641,-0.7696325,0,0.6384871,-0.7696325,0,0.6384871,-0.7696325,0,0.6384871,-0.7696325,0,0.6384871,-0.7696325,0,0.6384871,-0.7696325,0,0.6384871,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.9643813,-0.264516,0,0.9643813,-0.264516,0,0.9643813,-0.264516,0,0.9643813,-0.264516,0,0.9643813,-0.264516,0,0.9643813,-0.264516,0,-0.9315284,0.3636687,0,-0.9315284,0.3636687,0,-0.9315284,0.3636687,0,-0.9315284,0.3636687,0,-0.9315284,0.3636687,0,-0.9315284,0.3636687,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.9315284,0.3636687,0,0.9315284,0.3636687,0,0.9315284,0.3636687,0,0.9315284,0.3636687,0,0.9315284,0.3636687,0,0.9315284,0.3636687,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.9643813,-0.264516,0,-0.9643813,-0.264516,0,-0.9643813,-0.264516,0,-0.9643813,-0.264516,0,-0.9643813,-0.264516,0,-0.9643813,-0.264516,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *144 {
				a: -17.71654,2.141732,39.59055,2.141732,-17.71654,-4.156675E-13,39.59055,1.172396E-13,45.2126,-1.542047,45.2126,3.683779,60.47244,-1.542047,60.47244,3.683779,-45.2126,-1.542047,-60.47244,-1.542047,-45.2126,3.683779,-60.47244,3.683779,2.275591,9.345727E-13,-2.275591,9.345727E-13,2.275591,2.141732,-2.275591,2.141732,35.35048,-1.823653E-13,35.35048,2.141732,41.85779,-1.542047,41.85779,3.683779,-42.9964,3.683779,-42.9964,-1.542047,-45.77919,2.847647,-45.77919,-0.705915,-35.35048,2.943599E-13,-41.85779,-1.542047,-35.35048,2.141732,-41.85779,3.683779,42.9964,-1.542047,42.9964,3.683779,45.77919,-0.705915,45.77919,2.847647,5.552441,60.47244,5.552441,45.2126,-5.552441,60.47244,-5.552441,45.2126,-5.552441,44.5766,5.552441,44.5766,-2.275591,38.74691,2.275591,38.74691,5.552441,55.771,-5.552441,55.771,3.77566,58.07016,-3.77566,58.07016,-2.275591,39.59055,2.275591,39.59055,-2.275591,-17.71654,2.275591,-17.71654,2.275591,39.59055,2.275591,-17.71654,-2.275591,39.59055,-2.275591,-17.71654,5.552441,-54.99212,3.77566,-57.29128,-5.552441,-54.99212,-3.77566,-57.29128,-5.552441,45.2126,-5.552441,60.47244,5.552441,45.2126,5.552441,60.47244,3.77566,-0.705915,-3.77566,-0.705915,3.77566,2.847647,-3.77566,2.847647,-5.552441,-44.01008,-2.275591,-38.18039,5.552441,-44.01008,2.275591,-38.18039,17.71654,2.141732,17.71654,-5.364598E-13,-39.59055,2.141732,-39.59055,-3.552714E-15
				}
			UVIndex: *108 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *36 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh paddle, Model::RootNode
	C: "OO",5041307787666490253,0

	;Geometry::, Model::Mesh paddle
	C: "OO",4763917191890120681,5041307787666490253

	;Material::wood, Model::Mesh paddle
	C: "OO",19362,5041307787666490253

}
