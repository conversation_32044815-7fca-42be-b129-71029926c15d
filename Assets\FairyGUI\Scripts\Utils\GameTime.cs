using System;
using UnityEngine;


public class GameTime
{
	public GameTime()
	{
	}

	//private static float time = 0.0f;

	public static float time
	{
		get
		{
			return Time.time;
		}
	}

	public static float deltaTime
	{
		get
		{
			return Time.deltaTime;
		}
	}

	public static float fixedDeltaTime
	{
		get
		{
			return Time.fixedDeltaTime;
		}
		set
		{
			Time.fixedDeltaTime = value;
		}
	}

	public static float realtimeSinceStartup
	{
		get
		{
			return Time.realtimeSinceStartup;
		}
	}

	public static float timeScale
	{
		set
		{
			Time.timeScale = value;
		}
		get
		{
			return Time.timeScale;
		}
	}



	private static DateTime syncDateTime;
	private static float syncTimeOffset;
	public static void SetSyncDateTime(string datetime)
	{
		syncTimeOffset = GameTime.realtimeSinceStartup;
		syncDateTime = Convert.ToDateTime(datetime);
	}

	public static float DateTimeOffset2Sec(string datetimeStr)
	{
		DateTime dateTime = Convert.ToDateTime(datetimeStr);
		return DateTimeOffset2Sec(dateTime);
	}

	public static float DateTimeOffset2Sec(DateTime dateTime)
	{
		float deltaTime = GameTime.realtimeSinceStartup - syncTimeOffset;
		TimeSpan ts = dateTime - syncDateTime;
		return (float)(ts.TotalSeconds) - deltaTime;
	}

	public static string DateTimePassedStr(DateTime dateTime)
	{
		float seconds = -DateTimeOffset2Sec(dateTime);
		int deltaTime = Mathf.FloorToInt(seconds);

		int day = deltaTime / (24 * 60 * 60);
		int temp = deltaTime % (24 * 60 * 60);
		int hour = temp / (60 * 60);
		temp = temp % (60 * 60);
		int min = temp / 60;

		string str = "";

		if (day > 0)
			str += day.ToString() + Language.GetStr("Time", "d") + " ";

		if (hour > 0)
			str += hour.ToString() + Language.GetStr("Time", "h") + " ";

		if (min > 0)
			str += min.ToString() + Language.GetStr("Time", "min") + " ";

		if (String.IsNullOrEmpty(str))
			str = Language.GetStr("Time", "justNow");
		else
			str += Language.GetStr("Time", "ago");

		return str;
	}
}

