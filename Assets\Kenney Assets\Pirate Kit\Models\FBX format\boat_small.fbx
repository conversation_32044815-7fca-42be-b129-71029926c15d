; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 242
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/boat_small.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/boat_small.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5199907730025517087, "Model::boat_small_8angles", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-3.25,2.057448E-13,-99.62257
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5242762242652834000, "Geometry::", "Mesh" {
		Vertices: *468 {
			a: 3.75,2,1.7273,3.75,2.85,1.7273,3.75,2,6.14012,3.75,3.5,6.14012,3.75,2.85,-0.4226999,3.75,3.5,-1.521909,3.75,2,-0.4226999,3.75,2,-1.521909,3.75,2,1.7273,-3.75,2,1.7273,3.75,2.85,1.7273,-3.75,2.85,1.7273,3.75,2.85,-0.4226999,3.75,2.85,1.7273,-3.75,2.85,-0.4226999,-3.75,2.85,1.7273,-3.75,2,-0.4226999,3.75,2,-0.4226999,-3.75,2.85,-0.4226999,3.75,2.85,-0.4226999,3.75,2,1.7273,3.75,2,6.14012,-3.75,2,1.7273,-3.75,2,6.14012,3.4,0,6.45032,3.4,0,0,-3.4,0,6.45032,1.86,0,-2.75652,0,0,-3.81672,-1.86,0,-2.75652,-3.4,-4.511947E-16,0,2.44125,2,-6.135907,-2.887646E-14,2,-7.52742,1.86,0,-2.75652,0,0,-3.81672,2.087608,2,-4.497505,-1.443823E-14,2,-5.687442,2.087608,3.5,-4.497505,-1.443823E-14,3.5,-5.687442,-2.44125,2,-6.135907,-2.887646E-14,2,-7.52742,-2.325,3.5,-5.46003,-1.443823E-14,3.5,-6.78528,-3.75,2,6.14012,3.75,2,6.14012,-3.75,3.5,6.14012,3.75,3.5,6.14012,4.4625,2,7.298496,-4.4625,2,7.298496,4.25,3.5,7.298496,-4.25,3.5,7.298496,-2.725,3.5,7.298496,0,0,-3.81672,-2.887646E-14,2,-7.52742,-1.86,0,-2.75652,-2.44125,2,-6.135907,-2.44125,2,-6.135907,-2.325,3.5,-5.46003,-4.4625,2,-2.517975,-4.25,3.5,-2.01438,4.4625,2,-2.517975,2.44125,2,-6.135907,3.4,0,0,1.86,0,-2.75652,-2.087608,3.5,-4.497505,-2.087608,2,-4.497505,-3.75,3.5,-1.521909,-3.75,2,-1.521909,3.4,0,6.45032,-3.4,0,6.45032,4.4625,2,7.298496,-4.4625,2,7.298496,4.4625,2,-2.517975,4.4625,2,7.298496,4.25,3.5,-2.01438,4.25,3.5,7.298496,-3.75,2,-1.521909,-3.75,2.85,-0.4226999,-3.75,3.5,-1.521909,-3.75,3.5,6.14012,-3.75,2.85,1.7273,-3.75,2,1.7273,-3.75,2,6.14012,-3.75,2,-0.4226999,4.4625,2,-2.517975,3.75,2,6.14012,4.4625,2,7.298496,-4.4625,2,7.298496,-3.75,2,6.14012,-3.75,2,1.7273,-3.75,2,-0.4226999,-3.75,2,-1.521909,3.75,2,-1.521909,2.44125,2,-6.135907,3.75,2,1.7273,3.75,2,-0.4226999,2.087608,2,-4.497505,-2.887646E-14,2,-7.52742,-1.443823E-14,2,-5.687442,-2.087608,2,-4.497505,-2.44125,2,-6.135907,-4.4625,2,-2.517975,-1.443823E-14,2,-5.687442,-2.087608,2,-4.497505,-1.443823E-14,3.5,-5.687442,-2.087608,3.5,-4.497505,-4.4625,2,-2.517975,-4.4625,2,7.298496,-3.4,-4.511947E-16,0,-3.4,0,6.45032,4.25,3.5,7.298496,3.75,3.5,-1.521909,4.25,3.5,-2.01438,2.325,3.5,-5.46003,2.087608,3.5,-4.497505,-1.443823E-14,3.5,-6.78528,-1.443823E-14,3.5,-5.687442,-2.325,3.5,-5.46003,-2.087608,3.5,-4.497505,-3.75,3.5,-1.521909,-4.25,3.5,-2.01438,-3.75,3.5,6.14012,3.75,3.5,6.14012,-2.725,3.5,7.298496,-4.25,3.5,7.298496,-4.4625,2,-2.517975,-4.25,3.5,-2.01438,-4.4625,2,7.298496,-4.25,3.5,7.298496,-2.887646E-14,2,-7.52742,2.44125,2,-6.135907,-1.443823E-14,3.5,-6.78528,2.325,3.5,-5.46003,-1.86,0,-2.75652,-2.44125,2,-6.135907,-3.4,-4.511947E-16,0,-4.4625,2,-2.517975,2.087608,2,-4.497505,2.087608,3.5,-4.497505,3.75,2,-1.521909,3.75,3.5,-1.521909,4.4625,2,-2.517975,3.4,0,0,4.4625,2,7.298496,3.4,0,6.45032,2.44125,2,-6.135907,4.4625,2,-2.517975,2.325,3.5,-5.46003,4.25,3.5,-2.01438,3.75,2,-1.521909,3.75,2,-0.4226999,2.087608,2,-4.497505,-3.75,2,-0.4226999,-1.443823E-14,2,-5.687442,-2.087608,2,-4.497505,-3.75,2,-1.521909
		} 
		PolygonVertexIndex: *312 {
			a: 0,2,-2,2,3,-2,1,3,-5,3,5,-5,4,5,-7,7,6,-6,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,27,-27,29,28,-27,30,29,-27,31,33,-33,34,32,-34,35,37,-37,38,36,-38,39,41,-41,42,40,-42,43,45,-45,46,44,-46,47,49,-49,50,48,-50,51,50,-50,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,77,-80,81,80,-80,82,81,-80,77,83,-77,84,86,-86,87,85,-87,88,85,-88,89,88,-88,90,89,-88,91,90,-88,85,92,-85,93,84,-93,94,92,-86,95,92,-95,94,89,-96,90,95,-90,96,93,-93,97,93,-97,98,97,-97,99,97,-99,100,97,-100,91,100,-100,101,100,-92,87,101,-92,102,104,-104,105,103,-105,106,108,-108,109,107,-109,110,112,-112,113,111,-113,114,111,-114,115,114,-114,116,114,-116,117,116,-116,118,116,-118,119,118,-118,120,119,-118,121,119,-121,111,122,-111,123,110,-123,121,123,-123,124,123,-122,120,124,-122,125,127,-127,128,126,-128,129,131,-131,132,130,-132,133,135,-135,136,134,-136,137,139,-139,140,138,-140,141,143,-143,144,142,-144,145,147,-147,148,146,-148,149,151,-151,152,150,-152,153,152,-152,154,152,-154,155,152,-155
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *936 {
				a: -1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,0.2610611,-0.8497538,-0.4580019,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.495203,0,0.8687772,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,-0.4549548,0.3949008,-0.7981664,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.2610611,-0.8497538,-0.4580019,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,-0.8390304,0.2762346,-0.4687456,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.593768,-0.7330751,-0.3317236,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0.8729985,0,0.4877228,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0,-0.3904294,0.9206329,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,0.9901139,0.1402661,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,0.495203,0,0.8687772,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,-0.8831158,-0.4691553,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,-0.9901139,0.1402661,0,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,0.4549548,0.3949008,-0.7981664,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.593768,-0.7330751,-0.3317236,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,-0.8729985,0,0.4877228,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8831158,-0.4691553,0,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0.8390304,0.2762346,-0.4687456,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *312 {
				a: 13.60079,15.74803,13.60079,22.44094,48.3474,15.74803,48.3474,27.55906,-3.328346,22.44094,-11.98354,27.55906,-3.328346,15.74803,-11.98354,15.74803,29.52756,15.74803,-29.52756,15.74803,29.52756,22.44094,-29.52756,22.44094,-29.52756,-3.328346,-29.52756,13.60079,29.52756,-3.328346,29.52756,13.60079,29.52756,15.74803,-29.52756,15.74803,29.52756,22.44094,-29.52756,22.44094,-29.52756,13.60079,-29.52756,48.3474,29.52756,13.60079,29.52756,48.3474,26.77165,50.78992,26.77165,-1.599737E-33,-26.77165,50.78992,14.64567,-21.70488,0,-30.05291,-14.64567,-21.70488,-26.77165,-5.756728E-33,7.225335,52.05869,29.35119,52.05869,-1.975501,22.18646,14.88229,22.18646,-3.256001,15.74803,-22.17668,15.74803,-3.256001,27.55906,-22.17668,27.55906,-7.225335,-5.86666,-29.35119,-5.86666,-5.385168,6.989243,-26.45741,6.989243,29.52756,15.74803,-29.52756,15.74803,29.52756,27.55906,-29.52756,27.55906,35.13779,15.74803,-35.13779,15.74803,33.46457,27.55906,-33.46457,27.55906,-21.45669,27.55906,-14.88229,22.18646,-29.35119,52.05869,1.975501,22.18646,-7.225335,52.05869,-32.80303,3.990561,-28.60349,16.27975,-0.1710657,3.990561,2.474577,16.27975,0.1710657,40.28699,32.80303,40.28699,-13.05715,17.13315,11.8053,17.13315,22.89875,27.55906,22.89875,15.74803,-3.939653,27.55906,-3.939653,15.74803,26.77165,19.82988,-26.77165,19.82988,35.13779,36.93554,-35.13779,36.93554,19.82657,10.6637,-57.46847,10.6637,15.86126,22.59266,-57.46847,22.59266,11.98354,15.74803,3.328346,22.44094,11.98354,27.55906,-48.3474,27.55906,-13.60079,22.44094,-13.60079,15.74803,-48.3474,15.74803,3.328346,15.74803,35.13779,-19.82657,29.52756,48.3474,35.13779,57.46847,-35.13779,57.46847,-29.52756,48.3474,-29.52756,13.60079,-29.52756,-3.328346,-29.52756,-11.98354,29.52756,-11.98354,19.22244,-48.31423,29.52756,13.60079,29.52756,-3.328346,16.43786,-35.41343,-2.266812E-13,-59.27102,-1.129943E-13,-44.783,-16.43786,-35.41343,-19.22244,-48.31423,-35.13779,-19.82657,22.17668,15.74803,3.256001,15.74803,22.17668,27.55906,3.256001,27.55906,-19.82657,30.39241,57.46847,30.39241,-1.331387E-15,12.56006,50.78992,12.56006,-33.46457,57.46847,-29.52756,-11.98354,-33.46457,-15.86126,-18.30709,-42.99236,-16.43786,-35.41343,1.136868E-13,-53.4274,1.136868E-13,-44.783,18.30709,-42.99236,16.43786,-35.41343,29.52756,-11.98354,33.46457,-15.86126,29.52756,48.3474,-29.52756,48.3474,21.45669,57.46847,33.46457,57.46847,-19.82657,10.6637,-15.86126,22.59266,57.46847,10.6637,57.46847,22.59266,29.35119,-5.86666,7.225335,-5.86666,26.45741,6.989243,5.385168,6.989243,-11.8053,17.13315,-32.80303,40.28699,13.05715,17.13315,-0.1710657,40.28699,-22.89875,15.74803,-22.89875,27.55906,3.939653,15.74803,3.939653,27.55906,19.82657,30.39241,1.836396E-16,12.56006,-57.46847,30.39241,-50.78992,12.56006,32.80303,3.990561,0.1710657,3.990561,28.60349,16.27975,-2.474577,16.27975,-29.52756,-11.98354,-29.52756,-3.328346,-16.43786,-35.41343,29.52756,-3.328346,1.128666E-13,-44.783,16.43786,-35.41343,29.52756,-11.98354
				}
			UVIndex: *312 {
				a: 0,2,1,2,3,1,1,3,4,3,5,4,4,5,6,7,6,5,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,27,26,29,28,26,30,29,26,31,33,32,34,32,33,35,37,36,38,36,37,39,41,40,42,40,41,43,45,44,46,44,45,47,49,48,50,48,49,51,50,49,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,77,79,81,80,79,82,81,79,77,83,76,84,86,85,87,85,86,88,85,87,89,88,87,90,89,87,91,90,87,85,92,84,93,84,92,94,92,85,95,92,94,94,89,95,90,95,89,96,93,92,97,93,96,98,97,96,99,97,98,100,97,99,91,100,99,101,100,91,87,101,91,102,104,103,105,103,104,106,108,107,109,107,108,110,112,111,113,111,112,114,111,113,115,114,113,116,114,115,117,116,115,118,116,117,119,118,117,120,119,117,121,119,120,111,122,110,123,110,122,121,123,122,124,123,121,120,124,121,125,127,126,128,126,127,129,131,130,132,130,131,133,135,134,136,134,135,137,139,138,140,138,139,141,143,142,144,142,143,145,147,146,148,146,147,149,151,150,152,150,151,153,152,151,154,152,153,155,152,154
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *104 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh boat_small_8angles, Model::RootNode
	C: "OO",5199907730025517087,0

	;Geometry::, Model::Mesh boat_small_8angles
	C: "OO",5242762242652834000,5199907730025517087

	;Material::wood, Model::Mesh boat_small_8angles
	C: "OO",19362,5199907730025517087

}
