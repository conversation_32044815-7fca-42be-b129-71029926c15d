﻿#if UNITY_EDITOR
using System;
using System.IO;
using UnityEditor;
using UnityEngine;
using YooAsset;
using YooAsset.Editor;

public class YooAssetsBuilder
{
#if WXGAME
    public static string cdnDir = "lianliankan2_wechat";
#elif DYGAME
    public static string cdnDir = "lianliankan2_douyin";
#endif
    private static string FONT_OUTPUT_FOLDER = "Assets/_MyGame/Bundles/Fonts";
    private static string bundleVersion;
    private static bool shouldProcessFont;

    [MenuItem("Build/BuildTestBundles", priority = 0)]
    private static void BuildTestBundles()
    {
        bundleVersion = GetBuildPackageVersion();
        BuildBundles(bundleVersion, false);

        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var targetDir = Path.Join(Application.dataPath, "../Bundles/LocalCDN/" + buildTarget.ToString(), GameConfig.GetVer() + "/StreamingAssets/yoo/DefaultPackage");
        Copy2CDNDirection(bundleVersion, targetDir);
    }


    [MenuItem("Build/BuildBundles", priority = 0)]
    private static void BuildBundles()
    {
        bundleVersion = GetBuildPackageVersion();
        BuildBundles(bundleVersion, false);

        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var targetDir = Path.Join(Application.dataPath, "../../WebSite/" + cdnDir + "/" + buildTarget.ToString(), GameConfig.GetVer() + "/StreamingAssets/yoo/DefaultPackage");
        Copy2CDNDirection(bundleVersion, targetDir);
    }

    private static void Copy2CDNDirection(string version, string targetDir)
    {
        string defaultOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
        BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
        var bundleRootDir = Path.Join(defaultOutputRoot, buildTarget.ToString());
        var bundleDir = Path.Join(bundleRootDir, "DefaultPackage", version);

        CopyDirectory(bundleDir, targetDir);
        Debug.Log($"拷贝成功:" + bundleDir + " to " + targetDir);
    }

    [MenuItem("Build/Update2CDN", priority = 0)]
    private static void Update2CDN()
    {
        var websiteDir = Path.Join(Application.dataPath, "../../WebSite", cdnDir);
        var processInfo = new System.Diagnostics.ProcessStartInfo("git.exe", $"-C \"{websiteDir}\" pull --rebase");
        var process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();
        processInfo = new System.Diagnostics.ProcessStartInfo("git.exe", $"-C \"{websiteDir}\" add -A");
        process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();
        processInfo = new System.Diagnostics.ProcessStartInfo("git.exe", $"-C \"{websiteDir}\" commit -m \"Auto commit ==> {cdnDir}-{GameConfig.GetVer()}\"");
        process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();
        processInfo = new System.Diagnostics.ProcessStartInfo("git.exe", $"-C \"{websiteDir}\" push");
        process = System.Diagnostics.Process.Start(processInfo);
        process.WaitForExit();

        Debug.Log($"提交成功：" + websiteDir);

        // if (string.IsNullOrEmpty(bundleVersion))
        // {
        //     Debug.Log($"请先执行BuildBundles， 再执行Update2CDN");
        //     return;
        // }
        // string batFilePath = Path.Combine(defaultOutputRoot, "uploadCDN_wxgame.bat");
        // if (File.Exists(batFilePath))
        // {
        //     var startInfo = new System.Diagnostics.ProcessStartInfo(batFilePath, GameConfig.GetVer());
        //     startInfo.WorkingDirectory = defaultOutputRoot;
        //     var process = new System.Diagnostics.Process();
        //     process.StartInfo = startInfo;
        //     process.Start();
        // }
        // else
        // {
        //     Debug.LogError("uploadCDN_wxgame.bat file not found in bundleRootDir");
        // }
    }

    [MenuItem("Tools/ClearBundleCache", priority = 11)]
    private static void ClearBundleCache()
    {
        var package = YooAssets.GetPackage("DefaultPackage");
        package?.ClearPackageSandbox();
    }

    public static void BuildBundles(string packageVersion, bool isUpdate)
    {
        FontBuildPreprocessor.ProcessFont(FONT_OUTPUT_FOLDER);

        BuildTarget buildTarget = UnityEditor.EditorUserBuildSettings.activeBuildTarget;
        Debug.Log($"开始构建Bundles : {buildTarget}_{packageVersion}");

        // 构建参数
        string defaultOutputRoot = AssetBundleBuilderHelper.GetDefaultBuildOutputRoot();
        BuildParameters buildParameters = new BuildParameters();
        buildParameters.StreamingAssetsRoot = AssetBundleBuilderHelper.GetDefaultStreamingAssetsRoot();
        buildParameters.BuildOutputRoot = defaultOutputRoot;
        buildParameters.BuildTarget = buildTarget;
        buildParameters.BuildPipeline = AssetBundleBuilderSettingData.Setting.BuildPipeline;
        buildParameters.BuildMode = EBuildMode.IncrementalBuild;
        buildParameters.PackageName = AssetBundleBuilderSettingData.Setting.BuildPackage;
        buildParameters.PackageVersion = packageVersion;
        buildParameters.VerifyBuildingResult = true;
        buildParameters.SharedPackRule = new ZeroRedundancySharedPackRule();
        buildParameters.EncryptionServices = CreateEncryptionServicesInstance(AssetBundleBuilderSettingData.Setting.EncyptionClassName);
        buildParameters.CompressOption = AssetBundleBuilderSettingData.Setting.CompressOption;
        buildParameters.OutputNameStyle = AssetBundleBuilderSettingData.Setting.OutputNameStyle;
        buildParameters.CopyBuildinFileOption = isUpdate ? ECopyBuildinFileOption.None : ECopyBuildinFileOption.ClearAndCopyAll;
        buildParameters.CopyBuildinFileTags = AssetBundleBuilderSettingData.Setting.CopyBuildinFileTags;

        // 执行构建
        AssetBundleBuilder builder = new AssetBundleBuilder();
        var buildResult = builder.Run(buildParameters);
        if (buildResult.Success)
        {
            Debug.Log($"构建成功 : {buildResult.OutputPackageDirectory}");
        }
        else
        {
            Debug.LogError($"构建失败 : {buildResult.ErrorInfo}");
        }

        // 如果之前生成了字体文件，需要恢复TTF文件
        // if (shouldProcessFont)
        // {
        FontBuildPreprocessor.RestoreTTFFiles(FONT_OUTPUT_FOLDER);
        // }
    }

    private static string GetBuildPackageVersion()
    {
        return DateTime.Now.ToString("yyyyMMdd_HHmmss");
    }

    private static IEncryptionServices CreateEncryptionServicesInstance(string encyptionClassName)
    {
        var encryptionClassTypes = EditorTools.GetAssignableTypes(typeof(IEncryptionServices));
        for (int i = 0; i < encryptionClassTypes.Count; i++)
        {
            var classType = encryptionClassTypes[i];
            if (classType.Name == encyptionClassName)
            {
                return (IEncryptionServices)Activator.CreateInstance(classType);
            }
        }
        return null;
    }

    public static void CopyDirectory(string srcDir, string targetDir, bool overwrite = true)
    {
        if (!Directory.Exists(targetDir))
        {
            Directory.CreateDirectory(targetDir);
        }

        string[] files = Directory.GetFiles(srcDir);
        string[] directories = Directory.GetDirectories(srcDir);
        foreach (string file in files)
        {
            string fileName = Path.GetFileName(file);
            string destination = Path.Combine(targetDir, fileName);
            File.Copy(file, destination, overwrite);
        }

        foreach (string directory in directories)
        {
            string directoryName = Path.GetFileName(directory);
            string destination = Path.Combine(targetDir, directoryName);
            CopyDirectory(directory, destination, overwrite);
        }
    }
}

#endif