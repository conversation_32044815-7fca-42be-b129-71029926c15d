; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 917
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/hole.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/hole.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5747259039224972233, "Model::hole", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",-47.75003,1.082867E-14,-60.51787
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5657234347422342441, "Geometry::", "Mesh" {
		Vertices: *792 {
			a: 1.244741,5.414336E-15,-1.82255,-0.8597585,5.414336E-15,-1.82255,1.473491,0.8,-2.218757,-1.088508,0.8,-2.218757,1.244741,5.414336E-15,-1.82255,1.244741,3.609557E-15,1.82255,2.296992,3.609557E-15,0,-0.8597585,5.414336E-15,-1.82255,-0.8597585,5.414336E-15,1.82255,-1.912009,5.414336E-15,0,1.244741,3.609557E-15,1.82255,1.473491,0.8,2.218757,-0.8597585,5.414336E-15,1.82255,-1.088508,0.8,2.218757,2.296992,3.609557E-15,0,2.754492,0.8,2.887646E-14,1.244741,3.609557E-15,1.82255,1.473491,0.8,2.218757,3.76368,0.8,2.887646E-14,2.754492,0.8,2.887646E-14,1.978086,0.8,-3.09274,1.473491,0.8,-2.218757,-1.593103,0.8,-3.09274,-1.088508,0.8,-2.218757,-2.369509,0.8,2.887646E-14,-3.378696,0.8,2.887646E-14,1.978086,0.8,3.09274,1.473491,0.8,2.218757,-1.593103,0.8,3.09274,-1.088508,0.8,2.218757,-1.912009,5.414336E-15,0,-0.8597585,5.414336E-15,1.82255,-2.369509,0.8,2.887646E-14,-1.088508,0.8,2.218757,-3.726887,0.3905,0,-3.378696,0.8,2.887646E-14,-3.562561,0.3905,0.2846217,-1.593103,0.8,3.09274,-3.556899,0.3546655,0.3472044,-3.651636,3.609557E-15,0.7054425,-1.933216,3.609557E-15,3.681833,-1.088508,0.8,-2.218757,-0.8597585,5.414336E-15,-1.82255,-2.369509,0.8,2.887646E-14,-1.912009,5.414336E-15,0,1.473491,0.8,-2.218757,2.754492,0.8,2.887646E-14,1.244741,5.414336E-15,-1.82255,2.296992,3.609557E-15,0,-1.129269,0,-4.439221,-0.6919905,1.804779E-15,-3.681833,-1.297786,0.407,-4.14734,-1.010851,0.407,-3.650354,-0.7092163,0.04274906,-3.650354,1.408893,0.5435424,3.281586,1.433356,0.58608,3.281586,1.327403,-3.609557E-15,3.681833,1.720291,0.58608,3.778573,1.551773,-9.023893E-15,4.070454,2.316022,0.58608,2.822464,2.733706,-3.609557E-15,2.962153,2.581096,0.58608,3.281586,2.918131,-1.082867E-14,3.281586,2.918131,-1.082867E-14,3.281586,2.462678,-1.082867E-14,4.070454,2.581096,0.58608,3.281586,2.294161,0.58608,3.778573,-4.353296,0.3905,-1.032198,-4.741047,0.3905,-0.3605943,-4.581023,3.609557E-15,-1.426631,-5.1965,3.609557E-15,-0.3605943,4.097841,0.407,1.443823E-14,3.822105,0.407,0.4775886,3.76368,0.8,2.887646E-14,1.978086,0.8,3.09274,3.822581,0.3640533,0.5400124,3.981394,0,0.8010939,2.316022,0.58608,2.822464,2.069032,0.58608,3.250263,2.733706,-3.609557E-15,2.962153,2.581096,0.58608,3.281586,2.294161,0.58608,3.778573,2.316022,0.58608,2.822464,2.069032,0.58608,3.250263,1.720291,0.58608,3.778573,1.45144,0.58608,3.250263,1.433356,0.58608,3.281586,-3.354372,0.3905,-0.645216,-3.726887,0.3905,0,-3.577796,0.3905,-1.032198,-4.353296,0.3905,-1.032198,-4.353296,0.3905,0.3110089,-3.577796,0.3905,0.3110089,-3.562561,0.3905,0.2846217,-4.741047,0.3905,-0.3605943,-1.760183,0.407,-3.382132,-1.165709,0.407,-3.382132,-1.593103,0.8,-3.09274,1.978086,0.8,-3.09274,-0.7092163,0.04274906,-3.650354,2.318198,3.609557E-15,-3.681833,-0.6919905,1.804779E-15,-3.681833,-3.562561,0.3905,0.2846217,-3.556899,0.3546655,0.3472044,-3.577796,0.3905,0.3110089,2.462678,-1.082867E-14,4.070454,1.551773,-9.023893E-15,4.070454,2.294161,0.58608,3.778573,1.720291,0.58608,3.778573,1.408893,0.5435424,3.281586,1.45144,0.58608,3.250263,1.433356,0.58608,3.281586,-3.292663,3.609557E-15,-1.327201,-3.354372,0.3905,-0.645216,-3.35007,3.609557E-15,-1.426631,-3.577796,0.3905,-1.032198,-3.556899,0.3546655,0.3472044,-3.651636,3.609557E-15,0.7054425,-3.577796,0.3905,0.3110089,-4.353296,0.3905,0.3110089,-4.581023,3.609557E-15,0.7054425,-4.353296,0.3905,0.3110089,-4.581023,3.609557E-15,0.7054425,-4.741047,0.3905,-0.3605943,-5.1965,3.609557E-15,-0.3605943,-3.35007,3.609557E-15,-1.426631,-3.577796,0.3905,-1.032198,-4.581023,3.609557E-15,-1.426631,-4.353296,0.3905,-1.032198,5.038204,-1.804779E-15,0.0122267,4.582751,-1.804779E-15,-0.7766405,4.582751,-1.804779E-15,0.8010939,3.981394,0,0.8010939,3.995512,-1.804779E-15,-0.7766405,3.797128,-3.609557E-15,-1.120252,2.733706,-3.609557E-15,2.962153,3.595991,-7.219114E-15,-2.349605,3.087361,-1.804779E-15,-2.349605,2.318198,3.609557E-15,-3.681833,2.462678,-1.082867E-14,4.070454,2.296992,3.609557E-15,0,1.551773,-9.023893E-15,4.070454,1.244741,3.609557E-15,1.82255,1.327403,-3.609557E-15,3.681833,-1.933216,3.609557E-15,3.681833,-0.8597585,5.414336E-15,1.82255,-1.912009,5.414336E-15,0,2.918131,-1.082867E-14,3.281586,4.051443,-7.219114E-15,-1.560738,1.244741,5.414336E-15,-1.82255,-0.6919905,1.804779E-15,-3.681833,-0.8597585,5.414336E-15,-1.82255,-1.129269,0,-4.439221,-2.040174,0,-4.439221,-2.223509,1.804779E-15,-3.179031,-3.651636,3.609557E-15,0.7054425,-3.292663,3.609557E-15,-1.327201,-3.35007,3.609557E-15,-1.426631,-4.581023,3.609557E-15,-1.426631,-4.581023,3.609557E-15,0.7054425,-5.1965,3.609557E-15,-0.3605943,-2.495627,0,-3.650354,-2.223509,1.804779E-15,-3.179031,-1.881958,0.407,-3.171211,-3.292663,3.609557E-15,-1.327201,-1.593103,0.8,-3.09274,-3.378696,0.8,2.887646E-14,-3.354372,0.3905,-0.645216,-3.726887,0.3905,0,-1.760183,0.407,-3.382132,1.327403,-3.609557E-15,3.681833,-1.933216,3.609557E-15,3.681833,1.408893,0.5435424,3.281586,-1.593103,0.8,3.09274,1.45144,0.58608,3.250263,2.069032,0.58608,3.250263,1.978086,0.8,3.09274,-2.040174,0,-4.439221,-1.871657,0.407,-4.14734,-2.495627,0,-3.650354,-2.158592,0.407,-3.650354,4.051443,-7.219114E-15,-1.560738,3.714408,0.2035,-1.560738,3.595991,-7.219114E-15,-2.349605,3.427473,0.2035,-2.057724,4.051443,-7.219114E-15,-1.560738,3.797128,-3.609557E-15,-1.120252,3.714408,0.2035,-1.560738,3.542094,0.2035,-1.262282,-2.495627,0,-3.650354,-2.158592,0.407,-3.650354,-2.223509,1.804779E-15,-3.179031,-1.881958,0.407,-3.171211,-2.040174,0,-4.439221,-1.129269,0,-4.439221,-1.871657,0.407,-4.14734,-1.297786,0.407,-4.14734,3.714408,0.2035,-1.560738,3.542094,0.2035,-1.262282,3.427473,0.2035,-2.057724,3.082846,0.2035,-2.057724,3.595991,-7.219114E-15,-2.349605,3.427473,0.2035,-2.057724,3.087361,-1.804779E-15,-2.349605,3.082846,0.2035,-2.057724,-1.010851,0.407,-3.650354,-0.7092163,0.04274906,-3.650354,-1.165709,0.407,-3.382132,-1.010851,0.407,-3.650354,-1.165709,0.407,-3.382132,-1.297786,0.407,-4.14734,-1.760183,0.407,-3.382132,-1.871657,0.407,-4.14734,-1.881958,0.407,-3.171211,-2.158592,0.407,-3.650354,2.318198,3.609557E-15,-3.681833,3.087361,-1.804779E-15,-2.349605,1.978086,0.8,-3.09274,3.082846,0.2035,-2.057724,3.76368,0.8,2.887646E-14,3.542094,0.2035,-1.262282,3.797128,-3.609557E-15,-1.120252,3.829456,0.3806574,-0.5036513,3.995512,-1.804779E-15,-0.7766405,3.829164,0.407,-0.4653619,4.097841,0.407,1.443823E-14,3.822105,0.407,0.4775886,3.840363,0.407,0.5092131,3.822581,0.3640533,0.5400124,4.582751,-1.804779E-15,0.8010939,3.981394,0,0.8010939,4.414234,0.407,0.5092131,3.822581,0.3640533,0.5400124,3.840363,0.407,0.5092131,4.701169,0.407,0.0122267,4.414234,0.407,0.5092131,4.414234,0.407,-0.4847597,3.840363,0.407,-0.4847597,4.097841,0.407,1.443823E-14,3.829164,0.407,-0.4653619,3.840363,0.407,0.5092131,3.822105,0.407,0.4775886,5.038204,-1.804779E-15,0.0122267,4.582751,-1.804779E-15,0.8010939,4.701169,0.407,0.0122267,4.414234,0.407,0.5092131,3.829456,0.3806574,-0.5036513,3.840363,0.407,-0.4847597,3.829164,0.407,-0.4653619,3.995512,-1.804779E-15,-0.7766405,4.582751,-1.804779E-15,-0.7766405,3.829456,0.3806574,-0.5036513,4.414234,0.407,-0.4847597,3.840363,0.407,-0.4847597,4.582751,-1.804779E-15,-0.7766405,5.038204,-1.804779E-15,0.0122267,4.414234,0.407,-0.4847597,4.701169,0.407,0.0122267,2.296992,3.609557E-15,0,1.244741,3.609557E-15,1.82255,1.244741,5.414336E-15,-1.82255,-0.8597585,5.414336E-15,-1.82255,-0.8597585,5.414336E-15,1.82255,-1.912009,5.414336E-15,0
		} 
		PolygonVertexIndex: *528 {
			a: 0,2,-2,3,1,-3,4,6,-6,5,7,-5,5,8,-8,8,9,-8,10,12,-12,13,11,-13,14,16,-16,17,15,-17,18,20,-20,21,19,-21,22,21,-21,23,21,-23,24,23,-23,25,24,-23,18,19,-27,27,26,-20,28,26,-28,25,28,-25,29,28,-28,24,28,-30,30,32,-32,33,31,-33,34,36,-36,37,35,-37,38,37,-37,39,37,-39,40,37,-40,41,43,-43,44,42,-44,45,47,-47,48,46,-48,49,51,-51,52,50,-52,53,50,-53,54,56,-56,57,55,-57,58,57,-57,59,61,-61,62,60,-62,63,65,-65,66,64,-66,67,69,-69,70,68,-70,71,73,-73,74,72,-74,75,72,-75,76,75,-75,77,76,-75,78,77,-75,77,79,-77,80,82,-82,83,81,-83,84,81,-84,85,84,-84,86,84,-86,87,89,-89,90,88,-90,90,91,-89,91,92,-89,93,88,-93,94,91,-91,95,97,-97,97,98,-97,96,98,-100,98,100,-100,101,99,-101,102,104,-104,105,107,-107,108,106,-108,109,111,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,117,-120,121,123,-123,124,122,-124,125,127,-127,128,126,-128,129,131,-131,132,130,-132,133,130,-133,134,133,-133,135,134,-133,136,134,-136,137,136,-136,138,137,-136,139,138,-136,140,138,-140,141,140,-140,142,140,-142,143,142,-142,144,142,-144,145,142,-145,146,145,-145,147,139,-136,134,136,-149,140,149,-139,150,138,-150,151,150,-150,152,150,-152,146,152,-152,153,152,-147,144,153,-147,154,153,-145,155,154,-145,156,154,-156,157,156,-156,158,157,-156,159,158,-156,160,158,-160,154,161,-154,162,164,-164,165,163,-165,166,165,-165,167,166,-165,168,166,-168,163,165,-170,170,172,-172,173,171,-173,174,173,-173,175,173,-175,176,173,-176,177,179,-179,180,178,-180,181,183,-183,184,182,-184,185,187,-187,188,186,-188,189,191,-191,192,190,-192,193,195,-195,196,194,-196,197,199,-199,200,198,-200,201,203,-203,204,202,-204,205,207,-207,208,210,-210,211,209,-211,212,211,-211,213,211,-213,214,213,-213,215,217,-217,218,216,-218,219,218,-218,220,218,-220,221,220,-220,222,221,-220,223,221,-223,224,222,-220,225,224,-220,226,228,-228,229,231,-231,232,230,-232,233,232,-232,234,236,-236,237,235,-237,238,235,-238,239,238,-238,235,238,-241,241,240,-239,242,244,-244,245,243,-245,246,248,-248,249,251,-251,252,250,-252,253,252,-252,254,256,-256,257,255,-257,258,260,-260,261,259,-261,262,259,-262,263,262,-262
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1584 {
				a: 0,0.443811,0.8961204,0,0.443811,0.8961204,0,0.443811,0.8961204,0,0.443811,0.8961204,0,0.443811,0.8961204,0,0.443811,0.8961204,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.443811,-0.8961204,0,0.443811,-0.8961204,0,0.443811,-0.8961204,0,0.443811,-0.8961204,0,0.443811,-0.8961204,0,0.443811,-0.8961204,-0.7760631,0.443811,-0.4480602,-0.7760631,0.443811,-0.4480602,-0.7760631,0.443811,-0.4480602,-0.7760631,0.443811,-0.4480602,-0.7760631,0.443811,-0.4480602,-0.7760631,0.443811,-0.4480602,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7760631,0.443811,-0.4480602,0.7760631,0.443811,-0.4480602,0.7760631,0.443811,-0.4480602,0.7760631,0.443811,-0.4480602,0.7760631,0.443811,-0.4480602,0.7760631,0.443811,-0.4480602,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,-0.6973571,0.5929508,0.4026193,0.7760631,0.443811,0.4480602,0.7760631,0.443811,0.4480602,0.7760631,0.443811,0.4480602,0.7760631,0.443811,0.4480602,0.7760631,0.443811,0.4480602,0.7760631,0.443811,0.4480602,-0.7760631,0.443811,0.4480602,-0.7760631,0.443811,0.4480602,-0.7760631,0.443811,0.4480602,-0.7760631,0.443811,0.4480602,-0.7760631,0.443811,0.4480602,-0.7760631,0.443811,0.4480602,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,-0.775209,0.4457968,0.4475671,0.775209,0.4457968,-0.4475671,0.775209,0.4457968,-0.4475671,0.775209,0.4457968,-0.4475671,0.775209,0.4457968,-0.4475671,0.775209,0.4457968,-0.4475671,0.775209,0.4457968,-0.4475671,0.775209,0.4457968,0.4475671,0.775209,0.4457968,0.4475671,0.775209,0.4457968,0.4475671,0.775209,0.4457968,0.4475671,0.775209,0.4457968,0.4475671,0.775209,0.4457968,0.4475671,-0.6092959,0.7106414,-0.3517772,-0.6092959,0.7106414,-0.3517772,-0.6092959,0.7106414,-0.3517772,-0.6092959,0.7106414,-0.3517772,-0.6092959,0.7106414,-0.3517772,-0.6092959,0.7106414,-0.3517772,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0.6973571,0.5929508,0.4026193,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0,0.5929508,-0.8052387,0.6092959,0.7106414,0.3517772,0.6092959,0.7106414,0.3517772,0.6092959,0.7106414,0.3517772,0,0.4457968,0.8951342,0,0.4457968,0.8951342,0,0.4457968,0.8951342,0,0.4457968,0.8951342,0,0.4457968,0.8951342,0,0.4457968,0.8951342,-0.775209,0.4457968,-0.4475671,-0.775209,0.4457968,-0.4475671,-0.775209,0.4457968,-0.4475671,0.6092959,0.7106414,-0.3517772,0.6092959,0.7106414,-0.3517772,0.6092959,0.7106414,-0.3517772,0.6092959,0.7106414,-0.3517772,0.6092959,0.7106414,-0.3517772,0.6092959,0.7106414,-0.3517772,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,0,0.7106414,0.7035543,-0.6092959,0.7106414,0.3517772,-0.6092959,0.7106414,0.3517772,-0.6092959,0.7106414,0.3517772,-0.6092959,0.7106414,0.3517772,-0.6092959,0.7106414,0.3517772,-0.6092959,0.7106414,0.3517772,0,0.7106414,-0.7035543,0,0.7106414,-0.7035543,0,0.7106414,-0.7035543,0,0.7106414,-0.7035543,0,0.7106414,-0.7035543,0,0.7106414,-0.7035543,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,-0.6973571,0.5929508,-0.4026193,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,0,0.5929508,0.8052387,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,0.4952981,0.8203086,-0.2859605,0.4952981,0.8203086,-0.2859605,0.4952981,0.8203086,-0.2859605,0.4952981,0.8203086,-0.2859605,0.4952981,0.8203086,-0.2859605,0.4952981,0.8203086,-0.2859605,0.4952981,0.8203086,0.2859605,0.4952981,0.8203086,0.2859605,0.4952981,0.8203086,0.2859605,0.4952981,0.8203086,0.2859605,0.4952981,0.8203086,0.2859605,0.4952981,0.8203086,0.2859605,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.8203086,-0.5719211,0,0.8203086,-0.5719211,0,0.8203086,-0.5719211,0,0.8203086,-0.5719211,0,0.8203086,-0.5719211,0,0.8203086,-0.5719211,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,0.6973571,0.5929508,-0.4026193,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,0.4063152,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,0.5827795,0.8126303,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,0.7037585,0.5827796,0.4063152,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,-0.7037585,0.5827796,-0.4063152,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0,0.5827795,-0.8126303,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0.7037585,0.5827796,-0.4063152,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *528 {
				a: 9.801114,6.36904,-6.769752,6.36904,11.60229,13.39847,-8.570933,13.39847,-9.801114,-14.35079,-9.801114,14.35079,-18.08655,-8.95607E-30,6.769752,-14.35079,6.769752,14.35079,15.05519,-8.95607E-30,-9.801114,6.36904,-11.60229,13.39847,6.769752,6.36904,8.570933,13.39847,-9.043274,6.951594,-10.84445,13.98102,7.527593,6.951594,9.328773,13.98102,-29.63527,2.264547E-13,-21.68891,2.264547E-13,-15.57548,-24.35228,-11.60229,-17.47053,12.54412,-24.35228,8.570933,-17.47053,18.65755,2.264547E-13,26.60391,2.264547E-13,-15.57548,24.35228,-11.60229,17.47053,12.54412,24.35228,8.570933,17.47053,7.527593,5.786485,-9.043274,5.786485,9.328773,12.81591,-10.84445,12.81591,-14.67279,-12.59331,-13.30196,-8.589018,-12.08497,-12.59331,14.81764,-8.589018,-11.63592,-12.94372,-9.566036,-16.41181,17.49569,-16.41181,10.84445,12.81591,9.043274,5.786485,-9.328773,12.81591,-7.527593,5.786485,-9.328773,13.98102,10.84445,13.98102,-7.527593,6.951594,9.043274,6.951594,34.71742,-5.697634,27.83115,-5.697634,33.39051,-1.753991,28.87185,-1.753991,27.68431,-5.283415,27.92428,2.35446,28.02059,2.728641,30.33277,-2.426791,32.53926,2.728641,33.86617,-2.426791,-28.36485,2.044042,-30.96183,-3.11139,-32.53926,2.044042,-33.86617,-3.11139,-10.88875,-14.63045,-18.06124,-14.63045,-12.21566,-9.475019,-16.73433,-9.475019,10.10031,-21.82038,16.20661,-21.82038,8.307186,-26.19076,17.99973,-26.19076,16.13323,-13.9886,11.79093,-13.9886,14.81764,-10.14565,-13.30196,-10.14565,11.36714,-14.40855,10.21204,-17.96844,-10.12846,-12.23747,-14.01807,-12.23747,-9.436587,-17.96844,-20.32359,25.83926,-18.06426,29.75254,-18.23639,22.22413,-16.29159,25.59262,-13.5456,29.75254,-11.42866,25.59262,-11.28626,25.83926,26.41238,-5.080441,29.34557,-7.888658E-15,28.17162,-8.12754,34.27792,-8.12754,34.27792,2.448889,28.17162,2.448889,28.05166,2.241115,37.33108,-2.839325,13.85971,-13.21028,9.178813,-13.21028,12.54412,-9.367336,-15.57548,-9.367336,5.584381,-16.7721,-18.25353,-17.19013,5.448744,-17.19013,-15.96669,18.63091,-16.37116,18.22986,-16.20661,18.63091,19.39117,-14.28815,12.21869,-14.28815,18.06426,-9.132719,13.5456,-9.132719,16.83064,13.87352,16.44953,14.2477,16.73433,14.2477,22.01355,12.24278,17.60598,16.61317,22.91759,12.24278,21.12447,16.61317,-28.00707,0.02195793,-28.75304,-3.947376,-28.17162,0.4230089,-34.27792,0.4230089,-36.07104,-3.947376,-15.01816,-19.80264,-13.22504,-24.17302,-21.12447,-19.80264,-22.91759,-24.17302,26.3785,-7.98286,28.17162,-3.612475,36.07104,-7.98286,34.27792,-3.612475,39.6709,0.09627322,36.08466,-6.11528,36.08466,6.307827,31.34956,6.307827,31.46073,-6.11528,29.89865,-8.820879,21.52525,23.32404,28.31489,-18.50083,24.30993,-18.50083,18.25353,-28.99081,19.39117,32.05082,18.08655,-2.309866E-29,12.21869,32.05082,9.801114,14.35079,10.45199,28.99081,-15.22217,28.99081,-6.769752,14.35079,-15.05519,-1.635122E-29,22.97741,25.83926,31.90113,-12.28928,9.801114,-14.35079,-5.448744,-28.99081,-6.769752,-14.35079,-8.89188,-34.9545,-16.06436,-34.9545,-17.50794,-25.03174,-28.75304,5.554666,-25.92648,-10.4504,-26.3785,-11.23332,-36.07104,-11.23332,-36.07104,5.554666,-40.91732,-2.839325,-19.65061,-28.74294,-12.92415,-16.41181,-14.21551,-12.43196,3.91293,-16.41181,-14.81764,-8.589018,13.30196,-8.589018,8.806398,-12.59331,14.67279,-12.59331,-16.13323,-12.43196,10.45199,-17.19013,-15.22217,-17.19013,11.09365,-11.8751,-12.54412,-9.367336,11.42866,-11.45915,16.29159,-11.45915,15.57548,-9.367336,-22.2393,-18.2931,-20.91239,-14.34945,-15.06682,-18.2931,-16.39373,-14.34945,-5.307739,-27.70332,-3.980829,-24.9016,1.864745,-27.70332,0.5378355,-24.9016,26.59339,-17.62232,22.58843,-17.62232,25.26648,-14.8206,22.55288,-14.8206,-34.71742,-1.542298,-33.39051,2.401346,-30.43209,-1.542298,-29.03408,2.401346,16.06436,-20.37077,8.89188,-20.37077,14.73745,-16.42712,10.21879,-16.42712,-29.24731,-12.28928,-27.89051,-9.939228,-26.98798,-16.20255,-24.27438,-16.20255,-28.31489,-15.17639,-26.98798,-12.37467,-24.30993,-15.17639,-24.27438,-12.37467,20.91239,14.99681,22.09993,11.46738,18.47368,14.99681,7.959457,-28.74294,9.178813,-26.63096,10.21879,-32.65622,13.85971,-26.63096,14.73745,-32.65622,14.81857,-24.97016,16.99679,-28.74294,15.98001,-17.96844,3.867225,-17.96844,13.30196,-10.14565,1.894637,-15.97852,-14.81764,-10.14565,-5.33763,-15.97852,-7.310219,-17.96844,-11.64215,-14.24619,-10.43438,-17.96844,-11.9021,-13.9886,-16.13323,-13.9886,18.30438,16.69763,18.59192,16.69763,18.73193,16.2815,36.08466,-3.676072,31.34956,-3.676072,34.75775,0.2675716,30.09907,-0.148562,30.23908,0.2675716,-37.01708,0.09627322,-34.75775,4.009552,-34.75775,-3.817005,-30.23908,-3.817005,-32.26646,1.143249E-13,-30.1509,-3.664267,-30.23908,4.009552,-30.09531,3.76054,19.75207,-20.05003,12.57959,-20.05003,18.42517,-16.10639,13.9065,-16.10639,-18.51105,16.49849,-18.42517,16.75374,-18.2488,16.75374,-31.46073,-3.56386,-36.08466,-3.56386,-30.1532,0.1245362,-34.75775,0.3797837,-30.23908,0.3797837,-12.74634,-19.99392,-19.91882,-19.99392,-14.07325,-16.05028,-18.59192,-16.05028,-18.08655,-8.95607E-30,-9.801114,14.35079,-9.801114,-14.35079,6.769752,-14.35079,6.769752,14.35079,15.05519,-8.95607E-30
				}
			UVIndex: *528 {
				a: 0,2,1,3,1,2,4,6,5,5,7,4,5,8,7,8,9,7,10,12,11,13,11,12,14,16,15,17,15,16,18,20,19,21,19,20,22,21,20,23,21,22,24,23,22,25,24,22,18,19,26,27,26,19,28,26,27,25,28,24,29,28,27,24,28,29,30,32,31,33,31,32,34,36,35,37,35,36,38,37,36,39,37,38,40,37,39,41,43,42,44,42,43,45,47,46,48,46,47,49,51,50,52,50,51,53,50,52,54,56,55,57,55,56,58,57,56,59,61,60,62,60,61,63,65,64,66,64,65,67,69,68,70,68,69,71,73,72,74,72,73,75,72,74,76,75,74,77,76,74,78,77,74,77,79,76,80,82,81,83,81,82,84,81,83,85,84,83,86,84,85,87,89,88,90,88,89,90,91,88,91,92,88,93,88,92,94,91,90,95,97,96,97,98,96,96,98,99,98,100,99,101,99,100,102,104,103,105,107,106,108,106,107,109,111,110,112,114,113,115,113,114,116,118,117,119,117,118,120,117,119,121,123,122,124,122,123,125,127,126,128,126,127,129,131,130,132,130,131,133,130,132,134,133,132,135,134,132,136,134,135,137,136,135,138,137,135,139,138,135,140,138,139,141,140,139,142,140,141,143,142,141,144,142,143,145,142,144,146,145,144,147,139,135,134,136,148,140,149,138,150,138,149,151,150,149,152,150,151,146,152,151,153,152,146,144,153,146,154,153,144,155,154,144,156,154,155,157,156,155,158,157,155,159,158,155,160,158,159,154,161,153,162,164,163,165,163,164,166,165,164,167,166,164,168,166,167,163,165,169,170,172,171,173,171,172,174,173,172,175,173,174,176,173,175,177,179,178,180,178,179,181,183,182,184,182,183,185,187,186,188,186,187,189,191,190,192,190,191,193,195,194,196,194,195,197,199,198,200,198,199,201,203,202,204,202,203,205,207,206,208,210,209,211,209,210,212,211,210,213,211,212,214,213,212,215,217,216,218,216,217,219,218,217,220,218,219,221,220,219,222,221,219,223,221,222,224,222,219,225,224,219,226,228,227,229,231,230,232,230,231,233,232,231,234,236,235,237,235,236,238,235,237,239,238,237,235,238,240,241,240,238,242,244,243,245,243,244,246,248,247,249,251,250,252,250,251,253,252,251,254,256,255,257,255,256,258,260,259,261,259,260,262,259,261,263,262,261
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *176 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 11698, "Material::sand", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8470588,0.7607843,0.6078432
			P: "DiffuseColor", "Color", "", "A",0.8470588,0.7607843,0.6078432
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 14462, "Material::iron", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3764706,0.3764706,0.3764706
			P: "DiffuseColor", "Color", "", "A",0.3764706,0.3764706,0.3764706
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh hole, Model::RootNode
	C: "OO",5747259039224972233,0

	;Geometry::, Model::Mesh hole
	C: "OO",5657234347422342441,5747259039224972233

	;Material::sand, Model::Mesh hole
	C: "OO",11698,5747259039224972233

	;Material::iron, Model::Mesh hole
	C: "OO",14462,5747259039224972233

}
