; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 9
		Day: 20
		Hour: 20
		Minute: 27
		Second: 35
		Millisecond: 850
	}
	Creator: "Made using Asset Forge (www.assetforge.io)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/formationLarge_rock.fbx.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:/Unity/Asset Export/AssetsD:/Unity/Asset Export/Assets/Kenney Assets/Pirate Kit/Models/FBX format/formationLarge_rock.fbx.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",100
		P: "OriginalUnitScaleFactor", "double", "Number", "",100
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5469913624421570810, "Model::formationLarge_rock", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",40.45149,2.057448E-13,-106.3856
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,0.77,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4817104679963605047, "Geometry::", "Mesh" {
		Vertices: *684 {
			a: -10.72924,4.1605,5.888035,-9.654244,7.049916E-17,6.775587,-11.27165,4.1605,6.430444,-11.12346,1.409983E-16,8.244803,-9.654244,7.049916E-17,6.775587,-10.96642,7.049916E-17,3.244803,-11.12346,1.409983E-16,8.244803,-12.70769,1.409983E-16,3.244803,-13.62346,1.409983E-16,6.450934,-10.96642,0,2.006,-7.491928,0,0,-7.491928,0,8.024,-6.988363,0,7.733267,-5.505728,0,1.146733,-3.983717,0,0.268,-3.983717,0,9.467999,0,0,2.568,0,0,7.168,-10.06166,12.2248,2.355995,-9.744998,17.9242,2.428213,-10.06166,12.2248,3.839633,-9.744998,17.9242,3.659632,-10.96642,0,2.006,-7.491928,0,0,-10.47999,8.7556,2.28684,-7.491928,8.7556,0.56168,-10.47999,8.7556,2.28684,-10.06166,12.2248,2.355995,-10.47999,8.7556,4.012,-10.06166,12.2248,3.839633,-6.336196,12.2248,1.539619,-7.244054,12.2248,4.689252,-6.652861,17.9242,1.750621,-7.406383,17.9242,4.364816,-10.96642,0,2.006,-10.47999,8.7556,2.28684,-10.96642,7.049916E-17,3.244803,-10.47999,8.7556,4.012,-10.73528,4.1605,4.632536,-10.47999,8.7556,5.73716,-10.73528,4.1605,5.88455,-4.503862,8.7556,4.012,-7.203702,8.7556,4.99993,-4.922192,12.2248,3.839633,-7.244054,12.2248,4.689252,-10.47999,8.7556,2.28684,-7.491928,8.7556,0.56168,-10.06166,12.2248,2.355995,-7.491928,12.2248,0.8723575,-7.203702,8.7556,4.99993,-10.47999,8.7556,4.012,-7.244054,12.2248,4.689252,-10.06166,12.2248,3.839633,-7.491928,8.7556,0.56168,-4.503862,8.7556,2.28684,-7.491928,12.2248,0.8723575,-4.922192,12.2248,2.355995,-6.336196,12.2248,1.539619,-4.503862,8.7556,2.28684,-4.503862,8.7556,4.012,-4.922192,12.2248,2.355995,-4.922192,12.2248,3.839633,-7.244054,12.2248,4.689252,-10.06166,12.2248,3.839633,-7.406383,17.9242,4.364816,-9.744998,17.9242,3.659632,-10.06166,12.2248,2.355995,-7.491928,12.2248,0.8723575,-9.744998,17.9242,2.428213,-7.612118,17.9242,1.196794,-7.491928,12.2248,0.8723575,-6.336196,12.2248,1.539619,-7.612118,17.9242,1.196794,-6.652861,17.9242,1.750621,-11.12346,1.409983E-16,8.244803,-13.62346,1.409983E-16,6.450934,-11.27165,4.1605,6.430444,-12.02083,4.1605,5.892873,-12.70769,1.409983E-16,3.244803,-11.66084,4.1605,4.632536,-13.62346,1.409983E-16,6.450934,-12.02083,4.1605,5.892873,-5.505728,0,1.146733,-3.983717,0,0.268,-4.800763,1.652,1.659722,-3.983717,1.652,1.188,-10.96642,7.049916E-17,3.244803,-10.73528,4.1605,4.632536,-12.70769,1.409983E-16,3.244803,-11.66084,4.1605,4.632536,-7.491928,0,8.024,-9.654244,7.049916E-17,6.775587,-7.491928,8.7556,7.46232,-10.72924,4.1605,5.888035,-10.73528,4.1605,5.88455,-10.47999,8.7556,5.73716,-4.245932,4.112916,2.137924,-4.3403,5.8115,2.536434,-4.503862,8.7556,2.28684,-4.503862,8.7556,4.012,-4.3403,5.8115,5.029585,-4.503862,8.7556,5.73716,-4.168214,2.714,5.408862,-4.168214,2.714,5.930947,-7.491928,0,0,-5.505728,0,1.146733,-7.491928,8.7556,0.56168,-4.800763,1.652,1.659722,-4.413588,2.714,1.951386,-4.245932,4.112916,2.137924,-4.503862,8.7556,2.28684,-4.168214,2.714,5.930947,-5.768744,2.714,6.855013,-4.503862,8.7556,5.73716,-7.491928,8.7556,7.46232,-6.283398,1.652,7.220278,-7.491928,0,8.024,-6.988363,0,7.733267,0,0,7.168,-3.983717,0,9.467999,-0.7967434,1.652,6.708,-3.983717,1.652,8.547999,-3.983717,0,0.268,0,0,2.568,-3.983717,1.652,1.188,-0.7967434,1.652,3.028,0,0,2.568,0,0,7.168,-0.7967434,1.652,3.028,-0.7967434,1.652,4.077668,-0.7967434,1.652,6.708,-1.306659,2.714,3.2488,-1.306659,2.714,4.130521,-3.983717,0,9.467999,-6.988363,0,7.733267,-3.983717,1.652,8.547999,-5.577204,1.652,7.628,-6.283398,1.652,7.220278,-1.306659,2.714,4.130521,-3.777462,2.714,4.130521,-2.109776,5.8115,4.029685,-3.839339,5.8115,4.029685,-3.983717,2.714,1.7032,-1.306659,2.714,3.2488,-3.983717,5.8115,2.330561,-2.109776,5.8115,3.412481,-4.413588,2.714,1.951386,-3.983717,2.714,1.7032,-4.245932,4.112916,2.137924,-3.983717,5.8115,2.330561,-4.3403,5.8115,2.536434,-1.306659,2.714,3.2488,-1.306659,2.714,4.130521,-2.109776,5.8115,3.412481,-2.109776,5.8115,4.029685,-3.839339,5.8115,4.029685,-3.777462,2.714,4.130521,-3.709473,5.8115,4.738182,-3.591941,2.714,5.142658,-4.168214,2.714,5.408862,-4.3403,5.8115,5.029585,-3.591941,2.714,5.142658,-3.709473,5.8115,4.738182,-3.738176,1.652,4.077668,-3.296457,1.652,6.487519,-3.777462,2.714,4.130521,-3.591941,2.714,5.142658,-3.406419,2.714,6.154797,-3.983717,1.652,1.188,-0.7967434,1.652,3.028,-3.983717,2.714,1.7032,-1.306659,2.714,3.2488,-3.983717,1.652,1.188,-3.983717,2.714,1.7032,-4.800763,1.652,1.659722,-4.413588,2.714,1.951386,-3.296457,1.652,6.487519,-5.577204,1.652,7.628,-3.406419,2.714,6.154797,-5.322246,2.714,7.1128,-0.7967434,1.652,4.077668,-3.738176,1.652,4.077668,-1.306659,2.714,4.130521,-3.777462,2.714,4.130521,-5.577204,1.652,7.628,-6.283398,1.652,7.220278,-5.322246,2.714,7.1128,-5.768744,2.714,6.855013,-2.109776,5.8115,3.412481,-2.109776,5.8115,4.029685,-3.983717,5.8115,2.330561,-3.839339,5.8115,4.029685,-4.3403,5.8115,5.029585,-3.709473,5.8115,4.738182,-4.3403,5.8115,2.536434,-6.652861,17.9242,1.750621,-7.406383,17.9242,4.364816,-7.612118,17.9242,1.196794,-9.744998,17.9242,3.659632,-9.744998,17.9242,2.428213,-4.503862,8.7556,4.012,-4.503862,8.7556,5.73716,-7.203702,8.7556,4.99993,-7.491928,8.7556,7.46232,-10.47999,8.7556,4.012,-10.47999,8.7556,5.73716,-4.922192,12.2248,2.355995,-4.922192,12.2248,3.839633,-6.336196,12.2248,1.539619,-7.244054,12.2248,4.689252,-10.73528,4.1605,4.632536,-10.73528,4.1605,5.88455,-11.66084,4.1605,4.632536,-11.27165,4.1605,6.430444,-12.02083,4.1605,5.892873,-10.72924,4.1605,5.888035,-0.7967434,1.652,4.077668,-0.7967434,1.652,6.708,-3.738176,1.652,4.077668,-3.296457,1.652,6.487519,-3.983717,1.652,8.547999,-5.577204,1.652,7.628,-5.322246,2.714,7.1128,-4.168214,2.714,5.930947,-3.406419,2.714,6.154797,-3.591941,2.714,5.142658,-4.168214,2.714,5.408862,-5.768744,2.714,6.855013
		} 
		PolygonVertexIndex: *408 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,7,-7,5,9,-5,9,10,-5,4,10,-12,11,10,-13,10,13,-13,13,14,-13,12,14,-16,14,16,-16,17,15,-17,18,20,-20,21,19,-21,22,24,-24,25,23,-25,26,28,-28,29,27,-29,30,32,-32,33,31,-33,34,36,-36,37,35,-37,38,37,-37,39,37,-39,40,39,-39,41,43,-43,44,42,-44,45,47,-47,48,46,-48,49,51,-51,52,50,-52,53,55,-55,56,54,-56,57,56,-56,58,60,-60,61,59,-61,62,64,-64,65,63,-65,66,68,-68,69,67,-69,70,72,-72,73,71,-73,74,76,-76,77,75,-77,78,80,-80,81,79,-81,82,84,-84,85,83,-85,86,88,-88,89,87,-89,90,92,-92,93,91,-93,94,93,-93,95,94,-93,96,98,-98,99,97,-99,100,97,-100,101,100,-100,102,100,-102,103,102,-102,104,106,-106,107,105,-107,108,107,-107,109,108,-107,110,109,-107,111,113,-113,113,114,-113,112,114,-116,114,116,-116,117,115,-117,118,120,-120,121,119,-121,122,124,-124,125,123,-125,126,128,-128,129,127,-129,130,127,-130,128,131,-130,132,129,-132,133,135,-135,136,134,-136,137,134,-137,138,140,-140,141,139,-141,142,144,-144,145,143,-145,146,148,-148,149,147,-149,150,149,-149,151,153,-153,154,152,-154,155,157,-157,158,156,-158,159,161,-161,162,160,-162,163,165,-165,166,164,-166,167,164,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,190,-190,191,189,-191,190,192,-192,193,191,-193,194,192,-191,195,197,-197,198,196,-198,199,198,-198,200,202,-202,203,201,-203,204,203,-203,205,203,-205,206,208,-208,209,207,-209,210,212,-212,213,211,-213,214,213,-213,211,213,-216,216,218,-218,219,217,-219,217,219,-221,221,220,-220,222,224,-224,225,223,-225,226,223,-226,227,222,-224
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1224 {
				a: 0.6707768,0.3164124,0.6707768,0.6707768,0.3164124,0.6707768,0.6707768,0.3164124,0.6707768,0.6707768,0.3164124,0.6707768,0.6707768,0.3164124,0.6707768,0.6707768,0.3164124,0.6707768,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.9961229,0.08797266,0,-0.9960794,0.08846357,0,-0.9984601,0.0554755,0,-0.9984601,0.0554755,0,-0.9984601,0.0554755,0,-0.9960794,0.08846357,0,-0.4992302,0.05547081,-0.864692,-0.4989114,0.06595416,-0.8641399,-0.4992302,0.05547081,-0.864692,-0.4989054,0.06613331,-0.8641295,-0.4992302,0.05547081,-0.864692,-0.4989114,0.06595416,-0.8641399,-0.9961986,0.08711131,0,-0.997072,0.07646829,0,-0.9961229,0.08797266,0,-0.9960794,0.08846357,0,-0.9961229,0.08797266,0,-0.997072,0.07646829,0,0.959987,0.04309366,0.2767089,0.959987,0.04309366,0.2767089,0.959987,0.04309366,0.2767089,0.959987,0.04309366,0.2767089,0.959987,0.04309366,0.2767089,0.959987,0.04309366,0.2767089,-0.9984603,0.05547081,0,-0.9984603,0.05547081,0,-0.9961986,0.08711131,0,-0.997072,0.07646829,0,-0.9961986,0.08711131,0,-0.9984603,0.05547081,0,-0.9984603,0.05547081,0,-0.997072,0.07646829,0,-0.9984603,0.05547081,0,-0.9984603,0.05547081,0,-0.997072,0.07646829,0,-0.9984603,0.05547081,0,-0.9984603,0.05547081,0,-0.9984603,0.05547081,0,-0.9984603,0.05547081,0,0.3423123,0.08775659,0.935479,0.3423123,0.08775659,0.935479,0.3423123,0.08775659,0.935479,0.3423123,0.08775659,0.935479,0.3423123,0.08775659,0.935479,0.3423123,0.08775659,0.935479,-0.4989114,0.06595416,-0.8641399,-0.4991333,0.05885454,-0.8645242,-0.4989054,0.06613331,-0.8641295,-0.4991409,0.05859598,-0.8645374,-0.4989054,0.06613331,-0.8641295,-0.4991333,0.05885454,-0.8645242,-0.287725,0.08210377,0.9541873,-0.2880959,0.0646371,0.9554175,-0.287725,0.08210377,0.9541873,-0.2880894,0.06498675,0.9553959,-0.287725,0.08210377,0.9541873,-0.2880959,0.0646371,0.9554175,0.4989054,0.06613331,-0.8641295,0.4988192,0.06868362,-0.8639804,0.4989114,0.06595416,-0.8641399,0.498503,0.07732303,-0.8634325,0.4989114,0.06595416,-0.8641399,0.4988192,0.06868362,-0.8639804,0.498719,0.07153694,-0.8638066,0.498503,0.07732303,-0.8634325,0.4988192,0.06868362,-0.8639804,0.9961986,0.08711131,0,0.9928081,0.1197165,0,0.997072,0.07646829,0,0.9928081,0.1197165,0,0.997072,0.07646829,0,0.9928081,0.1197165,0,-0.2880959,0.0646371,0.9554175,-0.2883911,0.04622859,0.9563962,-0.2880894,0.06498675,0.9553959,-0.2883911,0.04622859,0.9563962,-0.2880894,0.06498675,0.9553959,-0.2883911,0.04622859,0.9563962,-0.4991333,0.05885454,-0.8645242,-0.4996249,0.03872496,-0.8653758,-0.4991409,0.05859598,-0.8645374,-0.4996249,0.03872496,-0.8653758,-0.4991409,0.05859598,-0.8645374,-0.4996249,0.03872496,-0.8653758,0.4988192,0.06868362,-0.8639804,0.4991072,0.05973542,-0.8644789,0.498719,0.07153694,-0.8638066,0.4991072,0.05973542,-0.8644789,0.498719,0.07153694,-0.8638066,0.4991072,0.05973542,-0.8644789,-0.5530387,0.3164124,0.7707342,-0.5530387,0.3164124,0.7707342,-0.5530387,0.3164124,0.7707342,-0.5530387,0.3164124,0.7707342,-0.5530387,0.3164124,0.7707342,-0.5530387,0.3164124,0.7707342,-0.9121427,0.3164124,-0.2605357,-0.9121427,0.3164124,-0.2605357,-0.9121427,0.3164124,-0.2605357,-0.9121427,0.3164124,-0.2605357,-0.9121427,0.3164124,-0.2605357,-0.9121427,0.3164124,-0.2605357,-0.4503584,0.4344068,-0.7800435,-0.4552219,0.4136329,-0.7884674,-0.4503584,0.4344068,-0.7800435,-0.4550324,0.4144661,-0.7881392,-0.4503584,0.4344068,-0.7800435,-0.4552219,0.4136329,-0.7884674,0,0.3164124,-0.9486217,0,0.3164124,-0.9486217,0,0.3164124,-0.9486217,0,0.3164124,-0.9486217,0,0.3164124,-0.9486217,0,0.3164124,-0.9486217,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,-0.4992302,0.05547081,0.864692,0.9984603,0.05547081,0,0.9961986,0.08711131,0,0.9984603,0.05547081,0,0.997072,0.07646829,0,0.9984603,0.05547081,0,0.9961986,0.08711131,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.997072,0.07646829,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.997072,0.07646829,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.9984603,0.05547081,0,0.4992302,0.05547081,-0.864692,0.4989054,0.06613331,-0.8641295,0.4992302,0.05547081,-0.864692,0.4992302,0.05547081,-0.864692,0.4992302,0.05547081,-0.864692,0.4989054,0.06613331,-0.8641295,0.4992302,0.05547081,-0.864692,0.4992302,0.05547081,-0.864692,0.4989054,0.06613331,-0.8641295,0.4992302,0.05547081,-0.864692,0.4992302,0.05547081,-0.864692,0.4989054,0.06613331,-0.8641295,0.4989114,0.06595416,-0.8641399,0.4992302,0.05547081,-0.864692,0.4989054,0.06613331,-0.8641295,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4992302,0.05547081,0.864692,0.4503584,0.4344068,0.7800435,0.4503584,0.4344068,0.7800435,0.4503584,0.4344068,0.7800435,0.4503584,0.4344068,0.7800435,0.4503584,0.4344068,0.7800435,0.4503584,0.4344068,0.7800435,0.4503584,0.4344068,-0.7800435,0.4550324,0.4144661,-0.7881392,0.4503584,0.4344068,-0.7800435,0.4549138,0.4149863,-0.7879339,0.4503584,0.4344068,-0.7800435,0.4550324,0.4144661,-0.7881392,0.9007168,0.4344068,0,0.9010436,0.4337284,0,0.9007168,0.4344068,0,0.9009734,0.4338744,0,0.9007168,0.4344068,0,0.9010436,0.4337284,0,0.9007168,0.4344068,0,0.9007168,0.4344068,0,0.9009734,0.4338744,0,0.9010436,0.4337284,0,0.9367097,0.3501072,0,0.9009734,0.4338744,0,0.9392895,0.343126,0,0.9009734,0.4338744,0,0.9367097,0.3501072,0,-0.4503584,0.4344068,0.7800435,-0.4503584,0.4344068,0.7800435,-0.4503584,0.4344068,0.7800435,-0.4470288,0.4479518,0.7742767,-0.4503584,0.4344068,0.7800435,-0.4503584,0.4344068,0.7800435,-0.445934,0.4522958,0.7723804,-0.4503584,0.4344068,0.7800435,-0.4470288,0.4479518,0.7742767,0,-0.01723827,0.9998515,0,0.03253649,0.9994705,0,-0.007842666,0.9999692,0,0.03253649,0.9994705,0,-0.007842666,0.9999692,0,0.03253649,0.9994705,0.4781527,0.29237,-0.8281847,0.4924815,0.1727654,-0.8530029,0.4773483,0.2975811,-0.8267915,0.4924815,0.1727654,-0.8530029,0.4773483,0.2975811,-0.8267915,0.4924815,0.1727654,-0.8530029,-0.4787546,0.2884024,-0.8292274,-0.4924815,0.1727654,-0.8530029,-0.4781527,0.29237,-0.8281847,-0.4924815,0.1727654,-0.8530029,-0.4781527,0.29237,-0.8281847,-0.4924815,0.1727654,-0.8530029,-0.4924815,0.1727654,-0.8530029,-0.4924815,0.1727654,-0.8530029,-0.4924815,0.1727654,-0.8530029,0.9367097,0.3501072,0,0.9679922,0.2509803,0,0.9392895,0.343126,0,0.9679922,0.2509803,0,0.9392895,0.343126,0,0.9679922,0.2509803,0,0.9835195,0.01377834,-0.1802762,0.9835195,0.01377834,-0.1802762,0.9831824,0.02958171,-0.1802144,0.9829956,0.03542267,-0.1801801,0.9831824,0.02958171,-0.1802144,0.9835195,0.01377834,-0.1802762,0.4156176,0.1332579,0.8997247,0.4156176,0.1332579,0.8997247,0.4156176,0.1332579,0.8997247,0.4156176,0.1332579,0.8997247,0.4156176,0.1332579,0.8997247,0.4156176,0.1332579,0.8997247,0.9826026,0.04531296,-0.1801081,0.9831824,0.02958171,-0.1802144,0.9826026,0.04531296,-0.1801081,0.9829956,0.03542267,-0.1801801,0.9826026,0.04531296,-0.1801081,0.9831824,0.02958171,-0.1802144,0.9826026,0.04531296,-0.1801081,0.9826026,0.04531296,-0.1801081,0.9829956,0.03542267,-0.1801801,0.4550324,0.4144661,-0.7881392,0.4781527,0.29237,-0.8281847,0.4549138,0.4149863,-0.7879339,0.4773483,0.2975811,-0.8267915,0.4549138,0.4149863,-0.7879339,0.4781527,0.29237,-0.8281847,-0.4550324,0.4144661,-0.7881392,-0.4552219,0.4136329,-0.7884674,-0.4781527,0.29237,-0.8281847,-0.4787546,0.2884024,-0.8292274,-0.4781527,0.29237,-0.8281847,-0.4552219,0.4136329,-0.7884674,0.4251562,0.3103981,0.8502324,0.4251562,0.3103981,0.8502324,0.4251562,0.3103981,0.8502324,0.4251562,0.3103981,0.8502324,0.4251562,0.3103981,0.8502324,0.4251562,0.3103981,0.8502324,0,-0.04970604,0.9987639,0,-0.01723827,0.9998515,0,-0.04970604,0.9987639,0,-0.007842666,0.9999692,0,-0.04970604,0.9987639,0,-0.01723827,0.9998515,-0.4470288,0.4479518,0.7742767,-0.4399224,0.4752614,0.7619679,-0.445934,0.4522958,0.7723804,-0.4399224,0.4752614,0.7619679,-0.445934,0.4522958,0.7723804,-0.4399224,0.4752614,0.7619679,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *456 {
				a: -92.52118,39.60551,-91.47752,5.071363,-98.5612,39.60551,-107.838,5.071363,-76.01767,53.35107,-86.34978,25.54963,-87.5863,64.91972,-100.0606,25.54963,-107.2713,50.79475,-86.34978,15.79528,-58.99156,-2.061533E-33,-58.99156,63.1811,-55.02648,60.89186,-43.35218,9.029395,-31.36785,2.110236,-31.36785,74.55118,0,20.22047,0,56.44094,18.55114,91.71495,19.11979,136.6613,30.23333,91.71495,28.816,136.6613,82.67873,-1.636155,51.08818,-1.636155,80.4674,67.41189,53.29953,67.41189,18.00661,58.56695,18.55114,86.08137,31.59055,58.56695,30.23333,86.08137,-25.46694,98.09004,-51.27689,98.09004,-27.75397,143.0089,-49.17623,143.0089,15.79528,-4.789892,18.00661,64.25816,25.54963,-4.789892,31.59055,64.25816,36.47667,28.02047,45.17449,64.25816,46.33504,28.02047,-44.15955,67.14175,-66.79668,67.14175,-46.7865,94.56409,-66.25443,94.56409,80.4674,66.75079,53.29953,66.75079,77.88704,94.14935,54.52266,94.14935,-42.94085,64.26971,-69.88573,64.26971,-43.95129,91.67879,-67.12389,91.67879,48.87685,71.31219,21.70897,71.31219,47.65371,98.71075,24.28934,98.71075,37.14565,98.71075,-18.00661,72.69147,-31.59055,72.69147,-18.55114,100.2059,-30.23333,100.2059,-43.95129,93.75986,-67.12389,93.75986,-45.91256,138.6851,-65.14582,138.6851,77.88704,95.27421,54.52266,95.27421,76.01199,140.1851,56.61956,140.1851,47.65371,98.20367,37.14565,98.20367,47.19599,143.1611,38.4743,143.1611,-33.31428,-32.84612,-57.54268,-32.84612,-42.59112,1.68803,-49.85167,1.68803,52.04842,-28.2226,60.29136,6.311542,78.30316,-28.2226,70.61216,6.311542,42.05879,-6.019318,28.22047,-6.019318,39.27121,8.422374,31.84252,8.422374,86.34978,8.08422,84.52976,42.61837,100.0606,8.08422,91.81763,42.61837,-19.49763,-4.671319,-39.1577,-4.671319,-21.70897,64.37672,-49.98252,28.13904,-50.0374,28.13904,-48.87685,64.37672,-16.83405,34.18983,-19.97192,47.58513,-18.00661,70.80277,-31.59055,70.80277,-39.60303,47.58513,-45.17449,70.80277,-42.58946,23.15776,-46.70037,23.15776,51.08818,1.636155,33.0294,1.636155,48.87685,70.6842,26.20253,14.66409,22.41406,23.03919,20.5364,34.07126,21.70897,70.6842,-51.77364,20.00402,-66.32587,20.00402,-53.29953,67.64903,-80.4674,67.64903,-71.2734,11.62892,-82.67873,-1.39901,-78.10026,-1.39901,-28.22047,-21.2335,-64.44095,-21.2335,-31.84252,-6.791807,-60.8189,-6.791807,26.11024,7.60709,-10.11024,7.60709,22.48819,22.04878,-6.488189,22.04878,-20.22047,1.792589E-17,-56.44094,5.003613E-17,-23.84252,14.44169,-32.10762,14.44169,-52.8189,14.44169,-25.5811,23.71784,-32.52379,23.71784,10.11024,-34.85991,-17.2084,-34.85991,6.488189,-20.41822,-8,-20.41822,-14.42082,-20.41822,-10.28865,20.30055,-29.7438,20.30055,-16.61241,44.70324,-30.23101,44.70324,20.45984,25.76493,-3.880315,25.76493,17.98992,50.52703,0.9518065,50.52703,37.77931,20.34564,33.87086,20.34564,37.37045,31.5289,36.34079,45.10776,39.58289,45.10776,-25.5811,23.26832,-32.52379,23.26832,-26.86993,48.46456,-31.72981,48.46456,-25.7594,46.24403,-26.62821,21.85195,-31.43106,46.24403,-34.73057,21.85195,-47.65543,17.86136,-47.63316,42.4706,-42.65711,17.86136,-42.16166,42.4706,-26.27464,14.56873,-45.56598,14.56873,-26.62821,22.93953,-34.73057,22.93953,-42.83294,22.93953,22.48819,21.2052,-6.488189,21.2052,20.45984,30.27543,-3.880315,30.27543,31.84252,9.055394,33.87086,18.12562,39.27121,9.055394,37.77931,18.12562,-46.06229,1.786971,-66.14103,1.786971,-45.66498,10.58367,-62.53112,10.58367,-6.27357,14.58774,-29.43445,14.58774,-10.28865,22.96029,-29.7438,22.96029,-8,-23.71187,-14.42082,-23.71187,-8.289763,-14.20768,-12.34939,-14.20768,16.61241,26.86993,16.61241,31.72981,31.36785,18.35088,30.23101,31.72981,34.17559,39.60303,29.20845,37.30852,34.17559,19.97192,52.38473,13.78442,58.31797,34.36863,59.93794,9.423572,76.73227,28.816,76.73227,19.11979,35.46349,31.59055,35.46349,45.17449,56.72206,39.36953,58.99156,58.75843,82.51962,31.59055,82.51962,45.17449,38.75742,18.55114,38.75742,30.23333,49.89131,12.12299,57.03979,36.92325,84.52976,36.47667,84.52976,46.33504,91.81763,36.47667,88.75317,50.63342,94.6522,46.40057,84.48223,46.36248,6.27357,32.10762,6.27357,52.8189,29.43445,32.10762,25.95636,51.08283,31.36785,67.30708,43.91499,60.06299,41.90745,56.0063,32.82058,46.70037,26.8222,48.46296,28.283,40.49337,32.82058,42.58946,45.42318,53.97649
				}
			UVIndex: *408 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,7,6,5,9,4,9,10,4,4,10,11,11,10,12,10,13,12,13,14,12,12,14,15,14,16,15,17,15,16,18,20,19,21,19,20,22,24,23,25,23,24,26,28,27,29,27,28,30,32,31,33,31,32,34,36,35,37,35,36,38,37,36,39,37,38,40,39,38,41,43,42,44,42,43,45,47,46,48,46,47,49,51,50,52,50,51,53,55,54,56,54,55,57,56,55,58,60,59,61,59,60,62,64,63,65,63,64,66,68,67,69,67,68,70,72,71,73,71,72,74,76,75,77,75,76,78,80,79,81,79,80,82,84,83,85,83,84,86,88,87,89,87,88,90,92,91,93,91,92,94,93,92,95,94,92,96,98,97,99,97,98,100,97,99,101,100,99,102,100,101,103,102,101,104,106,105,107,105,106,108,107,106,109,108,106,110,109,106,111,113,112,113,114,112,112,114,115,114,116,115,117,115,116,118,120,119,121,119,120,122,124,123,125,123,124,126,128,127,129,127,128,130,127,129,128,131,129,132,129,131,133,135,134,136,134,135,137,134,136,138,140,139,141,139,140,142,144,143,145,143,144,146,148,147,149,147,148,150,149,148,151,153,152,154,152,153,155,157,156,158,156,157,159,161,160,162,160,161,163,165,164,166,164,165,167,164,166,168,170,169,171,169,170,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182,184,186,185,187,185,186,188,190,189,191,189,190,190,192,191,193,191,192,194,192,190,195,197,196,198,196,197,199,198,197,200,202,201,203,201,202,204,203,202,205,203,204,206,208,207,209,207,208,210,212,211,213,211,212,214,213,212,211,213,215,216,218,217,219,217,218,217,219,220,221,220,219,222,224,223,225,223,224,226,223,225,227,222,223
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *136 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19362, "Material::wood", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.7294118,0.4627451,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.7294118,0.4627451,0.2784314
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}

	Material: 11698, "Material::sand", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8470588,0.7607843,0.6078432
			P: "DiffuseColor", "Color", "", "A",0.8470588,0.7607843,0.6078432
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "EmissiveFactor", "Number", "", "A",0
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh formationLarge_rock, Model::RootNode
	C: "OO",5469913624421570810,0

	;Geometry::, Model::Mesh formationLarge_rock
	C: "OO",4817104679963605047,5469913624421570810

	;Material::wood, Model::Mesh formationLarge_rock
	C: "OO",19362,5469913624421570810

	;Material::sand, Model::Mesh formationLarge_rock
	C: "OO",11698,5469913624421570810

}
