fileFormatVersion: 2
guid: ffa55b68f3cf8254e9f8245962103f98
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: armLeft 2
    100002: armRight 2
    100004: body 2
    100006: Group 85
    100008: Group 85 1
    100010: legLeft 2
    100012: legRight 2
    100014: //RootNode
    400000: armLeft 2
    400002: armRight 2
    400004: body 2
    400006: Group 85
    400008: Group 85 1
    400010: legLeft 2
    400012: legRight 2
    400014: //RootNode
    2300000: armLeft 2
    2300002: armRight 2
    2300004: body 2
    2300006: Group 85
    2300008: Group 85 1
    2300010: legLeft 2
    2300012: legRight 2
    3300000: armLeft 2
    3300002: armRight 2
    3300004: body 2
    3300006: Group 85
    3300008: Group 85 1
    3300010: legLeft 2
    3300012: legRight 2
    4300000: armLeft 2
    4300002: armRight 2
    4300004: body 2
    4300006: Group 85
    4300008: Group 85
    4300010: legLeft 2
    4300012: legRight 2
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
